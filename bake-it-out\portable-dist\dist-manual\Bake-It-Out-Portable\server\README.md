# 🧁 Bake It Out - Unified Game Server

**The easiest way to run a complete gaming server with cloud saves, multiplayer, and web dashboard!**

## ⚡ 30-Second Setup

**Just want to get started? Choose your setup method:**

```bash
# Super Simple (Recommended)
SIMPLE-SETUP.bat

# Or with Node.js
npm run quick-setup

# Or automatic (if working)
setup-easy.bat
```

**That's it! Your server will be running at:**
- 🎮 **Game Server**: http://localhost:3001
- 📊 **Dashboard**: http://localhost:3001/dashboard
- 🔍 **Health Check**: http://localhost:3001/health

---

## 🚀 What You Get

✅ **Cloud Saves** - Players can save games to the cloud
✅ **Multiplayer Gaming** - Real-time multiplayer rooms
✅ **User Accounts** - Secure registration and login
✅ **Web Dashboard** - Beautiful admin interface
✅ **Analytics** - Game metrics and player statistics
✅ **Auto-Setup** - Everything configured automatically

## 📋 Requirements

- **Node.js 18+** ([Download here](https://nodejs.org/))
- **That's it!** (MongoDB will be set up automatically)

## 🛠️ Setup Options

### Option 1: Super Simple (Recommended)
```bash
# Just run this - it always works!
SIMPLE-SETUP.bat
```
**What it does:**
- ✅ Installs dependencies
- ✅ Creates .env configuration file
- ✅ Creates necessary directories
- ✅ Gives clear next steps
- ✅ No complex automation that can fail

### Option 2: Node.js Setup
```bash
# 1. Install dependencies
npm install

# 2. Quick setup
npm run quick-setup

# 3. Start server
npm start
```

### Option 3: Manual Setup
```bash
# 1. Install dependencies
npm install

# 2. Copy environment file
copy .env.example .env

# 3. Create directories
mkdir logs uploads backups

# 4. Start MongoDB (choose one):
# - Docker: docker run -d -p 27017:27017 mongo:6.0
# - Local: Install MongoDB locally
# - Cloud: Use MongoDB Atlas

# 5. Start server
npm start
```

## 🎯 What Happens During Setup

### Automatic Setup (`setup-easy.bat` or `npm run setup-easy`)
✅ **Checks Node.js** - Verifies you have Node.js 18+
✅ **Installs Dependencies** - Downloads all required packages
✅ **Creates Configuration** - Sets up .env file with secure defaults
✅ **Sets Up MongoDB** - Automatically starts MongoDB with Docker
✅ **Generates Security Keys** - Creates secure JWT secrets
✅ **Tests Server** - Validates everything works
✅ **Opens Dashboard** - Launches the admin interface

### Quick Setup (`npm run quick-setup`)
✅ **Creates .env file** - Basic configuration
✅ **Creates directories** - logs, uploads, backups
✅ **Generates secure keys** - JWT secrets

## 🔧 Configuration Made Easy

### Database Options (Choose One)
```bash
# Option 1: Docker (Easiest - Automatic)
# Just run setup-easy.bat and it handles everything!

# Option 2: Local MongoDB
# Download from: https://www.mongodb.com/try/download/community
# Default connection: mongodb://localhost:27017/bake-it-out

# Option 3: MongoDB Atlas (Cloud)
# 1. Create account at: https://cloud.mongodb.com/
# 2. Create cluster and get connection string
# 3. Update MONGODB_URI in .env file
```

### Environment Variables (Auto-Generated)
The setup scripts automatically create a `.env` file with:
- ✅ **Secure JWT secrets** (randomly generated)
- ✅ **Database connection** (MongoDB local/Docker)
- ✅ **CORS settings** (allows game clients)
- ✅ **Rate limiting** (prevents abuse)
- ✅ **Security settings** (production-ready)

## 🚀 Starting Your Server

### After Setup
```bash
# Start the server
npm start

# Your server is now running at:
# 🌐 API: http://localhost:3001
# 📊 Dashboard: http://localhost:3001/dashboard
# 🔍 Health: http://localhost:3001/health
```

### First Time Login
1. **Create a game account** in the Bake It Out game
2. **Use those credentials** to login to the dashboard
3. **You're now an admin!** 🎉

## 🎮 Connecting Your Game

### Update Game Settings
In your game client, make sure the API URL points to your server:
```javascript
// In your game's environment settings
NEXT_PUBLIC_API_URL=http://localhost:3001
```

### Test the Connection
1. **Start your game client**
2. **Register/Login** in the game
3. **Try cloud saves** - Save a game to the cloud
4. **Check the dashboard** - See your save in the admin panel
5. **Try multiplayer** - Create a room and invite friends

## 🆘 Troubleshooting

### Common Issues & Solutions

#### "Port 3001 already in use"
```bash
# Find what's using the port
netstat -ano | findstr :3001

# Kill the process (Windows)
taskkill /PID <process_id> /F

# Or change the port in .env
PORT=3002
```

#### "MongoDB connection failed"
```bash
# Check if MongoDB is running
# For Docker:
docker ps | grep mongo

# Start MongoDB container
docker start bake-it-out-mongo

# For local MongoDB:
# Make sure MongoDB service is running
```

#### "Cannot find module" errors
```bash
# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install
```

#### Dashboard won't load
```bash
# Check server is running
curl http://localhost:3001/health

# Check logs
tail -f logs/combined.log

# Restart server
npm start
```

### Getting Help
- 📖 **Check the logs**: `logs/combined.log`
- 🔍 **Health check**: http://localhost:3001/health
- 📊 **Dashboard**: http://localhost:3001/dashboard
- 🐛 **Debug mode**: `DEBUG=* npm start`

## 🎉 Success Indicators

### Your server is working when you see:
✅ **Server starts** without errors
✅ **Health check** returns "OK"
✅ **Dashboard loads** at http://localhost:3001/dashboard
✅ **Game can connect** and save to cloud
✅ **Multiplayer works** - can create/join rooms

### Dashboard Features Available:
✅ **User Management** - View and manage players
✅ **Game Rooms** - Monitor multiplayer sessions
✅ **Cloud Saves** - Manage save files
✅ **Analytics** - View game statistics
✅ **Real-time Updates** - Live server monitoring

**🎮 Ready to start your gaming server? Run `setup-easy.bat` and you'll be up and running in 30 seconds!**

### Production Deployment

#### Option 1: Docker Compose (Recommended)

```bash
# Copy environment file
cp .env.example .env

# Edit .env for production
nano .env

# Start all services
docker-compose up -d

# With admin interface
docker-compose --profile admin up -d

# With caching
docker-compose --profile cache up -d

# Full stack with proxy
docker-compose --profile proxy up -d
```

#### Option 2: Manual Deployment

```bash
# Install dependencies
npm ci --only=production

# Set environment variables
export NODE_ENV=production
export MONGODB_URI=mongodb://localhost:27017/bake-it-out
export JWT_SECRET=your-super-secret-key

# Start server
npm start
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `NODE_ENV` | Environment mode | `development` |
| `PORT` | Server port | `3001` |
| `MONGODB_URI` | MongoDB connection string | `mongodb://localhost:27017/bake-it-out` |
| `JWT_SECRET` | JWT signing secret | Required |
| `JWT_EXPIRES_IN` | JWT expiration time | `7d` |
| `ALLOWED_ORIGINS` | CORS allowed origins | `http://localhost:3000,http://localhost:3002` |

### Security Configuration

```env
# Strong JWT secret (generate with: openssl rand -base64 32)
JWT_SECRET=your-super-secret-jwt-key-change-in-production

# Rate limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
AUTH_RATE_LIMIT_MAX=10

# CORS
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
```

## 📡 API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - User login
- `POST /api/auth/refresh` - Refresh access token
- `GET /api/auth/me` - Get current user
- `POST /api/auth/logout` - Logout user
- `GET /api/auth/verify` - Verify token

### Cloud Saves
- `GET /api/saves` - List user's saves
- `GET /api/saves/:id` - Get specific save
- `POST /api/saves` - Create new save
- `PUT /api/saves/:id` - Update save
- `DELETE /api/saves/:id` - Delete save
- `GET /api/saves/:id/metadata` - Get save metadata

### User Management
- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update profile
- `PUT /api/users/settings` - Update settings
- `GET /api/users/stats` - Get user statistics
- `DELETE /api/users/account` - Delete account

### System
- `GET /health` - Health check
- `GET /` - API information

## 🗄️ Database Schema

### User Model
```javascript
{
  username: String,
  email: String,
  passwordHash: String,
  profile: {
    displayName: String,
    avatar: String,
    preferredLanguage: String
  },
  gameStats: {
    totalPlayTime: Number,
    highestLevel: Number,
    totalMoney: Number,
    achievementsUnlocked: Number,
    gamesPlayed: Number
  },
  settings: {
    autoSave: Boolean,
    syncFrequency: Number,
    maxSaves: Number
  }
}
```

### CloudSave Model
```javascript
{
  userId: ObjectId,
  saveName: String,
  description: String,
  gameData: Mixed,
  metadata: {
    version: Number,
    gameVersion: String,
    platform: String,
    level: Number,
    money: Number,
    playTime: Number
  },
  saveType: String, // 'manual', 'auto', 'checkpoint'
  checksum: String,
  size: Number
}
```

## 🔒 Security Features

- **JWT Authentication**: Secure token-based authentication
- **Rate Limiting**: Protection against brute force attacks
- **Input Validation**: Comprehensive data validation
- **CORS Protection**: Configurable cross-origin resource sharing
- **Helmet Security**: Security headers and protection
- **Data Integrity**: Checksum validation for save files
- **Password Hashing**: bcrypt with salt rounds

## 📊 Monitoring

### Health Check
```bash
curl http://localhost:3001/health
```

### Logs
```bash
# View logs in Docker
docker-compose logs -f bake-it-out-server

# View MongoDB logs
docker-compose logs -f mongo
```

### Database Admin
Access MongoDB admin interface at `http://localhost:8081` (when using admin profile)

## 🚀 Deployment Options

### Cloud Platforms

#### Heroku
```bash
# Install Heroku CLI
heroku create bake-it-out-server
heroku addons:create mongolab:sandbox
heroku config:set JWT_SECRET=your-secret
git push heroku main
```

#### DigitalOcean App Platform
```yaml
# app.yaml
name: bake-it-out-server
services:
- name: api
  source_dir: /server
  github:
    repo: your-repo
    branch: main
  run_command: npm start
  environment_slug: node-js
  instance_count: 1
  instance_size_slug: basic-xxs
  envs:
  - key: NODE_ENV
    value: production
databases:
- name: mongodb
  engine: MONGODB
```

#### AWS ECS/Fargate
Use the provided Dockerfile with AWS ECS or Fargate for scalable deployment.

### Self-Hosted

#### VPS Deployment
```bash
# On your VPS
git clone your-repo
cd bake-it-out/server
npm ci --only=production

# Install PM2 for process management
npm install -g pm2
pm2 start src/index.js --name bake-it-out-server
pm2 startup
pm2 save
```

## 🧪 Testing

```bash
# Run tests
npm test

# Run with coverage
npm run test:coverage

# Test specific endpoint
curl -X POST http://localhost:3001/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","email":"<EMAIL>","password":"password123"}'
```

## 🔧 Troubleshooting

### Common Issues

1. **MongoDB Connection Failed**
   - Check MongoDB is running
   - Verify connection string
   - Check network connectivity

2. **JWT Token Errors**
   - Ensure JWT_SECRET is set
   - Check token expiration
   - Verify token format

3. **CORS Errors**
   - Update ALLOWED_ORIGINS
   - Check request headers
   - Verify domain configuration

4. **Rate Limiting**
   - Check rate limit settings
   - Clear rate limit cache
   - Adjust limits for production

### Debug Mode
```bash
DEBUG=* npm run dev
```

## 📈 Performance

### Optimization Tips
- Use MongoDB indexes for queries
- Implement Redis caching for sessions
- Enable gzip compression
- Use CDN for static assets
- Monitor memory usage

### Scaling
- Horizontal scaling with load balancer
- Database sharding for large datasets
- Redis cluster for session storage
- Container orchestration with Kubernetes

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Make changes
4. Add tests
5. Submit pull request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Support

- GitHub Issues: Report bugs and feature requests
- Documentation: Check API documentation
- Community: Join our Discord server

---

**Bake It Out Server** - Powering cloud saves for the ultimate bakery management experience! 🧁☁️
