const express = require('express');
const { body, query, validationResult } = require('express-validator');
const CloudSave = require('../models/CloudSave');
const User = require('../models/User');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// All routes require authentication
router.use(authenticateToken);

// Validation rules
const saveValidation = [
  body('saveName')
    .isLength({ min: 1, max: 100 })
    .trim()
    .withMessage('Save name must be 1-100 characters'),
  body('gameData')
    .isObject()
    .withMessage('Game data must be an object'),
  body('gameData.player')
    .isObject()
    .withMessage('Player data is required'),
  body('gameData.equipment')
    .isArray()
    .withMessage('Equipment data must be an array'),
  body('gameData.inventory')
    .isArray()
    .withMessage('Inventory data must be an array'),
  body('gameData.orders')
    .isArray()
    .withMessage('Orders data must be an array')
];

// Get user's cloud saves
router.get('/', async (req, res) => {
  try {
    const {
      limit = 20,
      skip = 0,
      saveType = null,
      activeOnly = 'true'
    } = req.query;

    const options = {
      limit: Math.min(parseInt(limit), 50), // Max 50 saves per request
      skip: parseInt(skip),
      saveType: saveType || null,
      activeOnly: activeOnly === 'true'
    };

    const saves = await CloudSave.getUserSaves(req.userId, options);
    const totalCount = await CloudSave.countDocuments({
      userId: req.userId,
      ...(options.saveType && { saveType: options.saveType }),
      ...(options.activeOnly && { isActive: true })
    });

    res.json({
      saves,
      pagination: {
        total: totalCount,
        limit: options.limit,
        skip: options.skip,
        hasMore: options.skip + options.limit < totalCount
      }
    });

  } catch (error) {
    console.error('Get saves error:', error);
    res.status(500).json({
      error: 'Failed to retrieve saves',
      code: 'GET_SAVES_ERROR'
    });
  }
});

// Get specific save with full game data
router.get('/:saveId', async (req, res) => {
  try {
    const { saveId } = req.params;

    const save = await CloudSave.getSaveWithData(saveId, req.userId);
    
    if (!save) {
      return res.status(404).json({
        error: 'Save not found',
        code: 'SAVE_NOT_FOUND'
      });
    }

    // Verify data integrity
    if (!save.validateIntegrity()) {
      console.warn(`Data integrity check failed for save ${saveId}`);
      return res.status(422).json({
        error: 'Save data integrity check failed',
        code: 'INTEGRITY_CHECK_FAILED'
      });
    }

    res.json({ save });

  } catch (error) {
    console.error('Get save error:', error);
    res.status(500).json({
      error: 'Failed to retrieve save',
      code: 'GET_SAVE_ERROR'
    });
  }
});

// Create new cloud save
router.post('/', saveValidation, async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const {
      saveName,
      description = '',
      gameData,
      saveType = 'manual',
      metadata = {}
    } = req.body;

    // Check user's save limit
    const user = await User.findById(req.userId);
    const userSaveCount = await CloudSave.countDocuments({
      userId: req.userId,
      isActive: true
    });

    if (userSaveCount >= user.settings.maxSaves) {
      return res.status(409).json({
        error: `Maximum number of saves reached (${user.settings.maxSaves})`,
        code: 'SAVE_LIMIT_REACHED'
      });
    }

    // Extract metadata from game data
    const extractedMetadata = {
      level: gameData.player?.level || 1,
      money: gameData.player?.money || 0,
      playTime: gameData.player?.playTime || 0,
      gameVersion: metadata.gameVersion || '1.0.0',
      platform: metadata.platform || 'web',
      deviceInfo: metadata.deviceInfo || 'Unknown'
    };

    // Create new save
    const cloudSave = new CloudSave({
      userId: req.userId,
      saveName,
      description,
      gameData,
      saveType,
      metadata: { ...extractedMetadata, ...metadata }
    });

    await cloudSave.save();

    // Update user stats
    await User.findByIdAndUpdate(req.userId, {
      $inc: { 'gameStats.gamesPlayed': saveType === 'manual' ? 1 : 0 },
      $max: {
        'gameStats.highestLevel': extractedMetadata.level,
        'gameStats.totalMoney': extractedMetadata.money
      },
      $set: {
        'gameStats.totalPlayTime': extractedMetadata.playTime
      }
    });

    res.status(201).json({
      message: 'Save created successfully',
      save: {
        id: cloudSave._id,
        saveName: cloudSave.saveName,
        description: cloudSave.description,
        saveType: cloudSave.saveType,
        metadata: cloudSave.metadata,
        createdAt: cloudSave.createdAt,
        size: cloudSave.size
      }
    });

  } catch (error) {
    console.error('Create save error:', error);
    res.status(500).json({
      error: 'Failed to create save',
      code: 'CREATE_SAVE_ERROR'
    });
  }
});

// Update existing save
router.put('/:saveId', saveValidation, async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { saveId } = req.params;
    const {
      saveName,
      description,
      gameData,
      metadata = {}
    } = req.body;

    const existingSave = await CloudSave.findOne({
      _id: saveId,
      userId: req.userId,
      isActive: true
    });

    if (!existingSave) {
      return res.status(404).json({
        error: 'Save not found',
        code: 'SAVE_NOT_FOUND'
      });
    }

    // Create backup before updating
    await existingSave.createBackup();

    // Extract metadata from game data
    const extractedMetadata = {
      level: gameData.player?.level || 1,
      money: gameData.player?.money || 0,
      playTime: gameData.player?.playTime || 0,
      version: existingSave.metadata.version + 1
    };

    // Update save
    existingSave.saveName = saveName;
    existingSave.description = description || existingSave.description;
    existingSave.gameData = gameData;
    existingSave.metadata = {
      ...existingSave.metadata,
      ...extractedMetadata,
      ...metadata
    };

    await existingSave.save();

    res.json({
      message: 'Save updated successfully',
      save: {
        id: existingSave._id,
        saveName: existingSave.saveName,
        description: existingSave.description,
        metadata: existingSave.metadata,
        updatedAt: existingSave.updatedAt,
        size: existingSave.size
      }
    });

  } catch (error) {
    console.error('Update save error:', error);
    res.status(500).json({
      error: 'Failed to update save',
      code: 'UPDATE_SAVE_ERROR'
    });
  }
});

// Delete save (soft delete)
router.delete('/:saveId', async (req, res) => {
  try {
    const { saveId } = req.params;

    const save = await CloudSave.findOne({
      _id: saveId,
      userId: req.userId,
      isActive: true
    });

    if (!save) {
      return res.status(404).json({
        error: 'Save not found',
        code: 'SAVE_NOT_FOUND'
      });
    }

    // Soft delete
    save.isActive = false;
    await save.save();

    res.json({
      message: 'Save deleted successfully'
    });

  } catch (error) {
    console.error('Delete save error:', error);
    res.status(500).json({
      error: 'Failed to delete save',
      code: 'DELETE_SAVE_ERROR'
    });
  }
});

// Get save metadata only (for quick sync checks)
router.get('/:saveId/metadata', async (req, res) => {
  try {
    const { saveId } = req.params;

    const save = await CloudSave.findOne({
      _id: saveId,
      userId: req.userId,
      isActive: true
    }).select('metadata updatedAt checksum size');

    if (!save) {
      return res.status(404).json({
        error: 'Save not found',
        code: 'SAVE_NOT_FOUND'
      });
    }

    res.json({
      metadata: save.metadata,
      updatedAt: save.updatedAt,
      checksum: save.checksum,
      size: save.size
    });

  } catch (error) {
    console.error('Get metadata error:', error);
    res.status(500).json({
      error: 'Failed to get save metadata',
      code: 'GET_METADATA_ERROR'
    });
  }
});

module.exports = router;
