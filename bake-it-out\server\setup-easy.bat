@echo off
title Bake It Out - Easy Server Setup

echo.
echo ===============================================
echo           BAKE IT OUT - Easy Setup
echo ===============================================
echo This will set up everything you need!
echo.

REM Check Node.js
echo [1/5] Checking Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found!
    echo.
    echo Please install Node.js first:
    echo 1. Go to https://nodejs.org/
    echo 2. Download and install Node.js 18+
    echo 3. Restart this script
    echo.
    pause
    exit /b 1
)

echo SUCCESS: Node.js found!

echo.
echo [2/5] Installing dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    echo Try running: npm install
    pause
    exit /b 1
)
echo SUCCESS: Dependencies installed!

echo.
echo [3/5] Setting up configuration...
if not exist ".env" (
    copy ".env.example" ".env" >nul
    echo SUCCESS: Configuration file created!
) else (
    echo SUCCESS: Configuration file already exists!
)

echo.
echo [4/5] Creating directories...
if not exist "logs" mkdir logs
if not exist "uploads" mkdir uploads
if not exist "backups" mkdir backups
echo SUCCESS: Directories created!

echo.
echo [5/5] Setting up database...

REM Try to start MongoDB with Docker
docker --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Docker found! Setting up MongoDB...
    docker run -d --name bake-it-out-mongo -p 27017:27017 mongo:6.0 >nul 2>&1
    if %errorlevel% equ 0 (
        echo SUCCESS: MongoDB container created!
    ) else (
        echo INFO: MongoDB container might already exist
        docker start bake-it-out-mongo >nul 2>&1
        echo SUCCESS: MongoDB started!
    )
) else (
    echo INFO: Docker not found
    echo You can:
    echo 1. Install Docker for automatic MongoDB setup
    echo 2. Install MongoDB locally
    echo 3. Use MongoDB Atlas (cloud)
    echo 4. Continue anyway (server will work without database initially)
)

echo.
echo ===============================================
echo                 SETUP COMPLETE!
echo ===============================================
echo.
echo Your Bake It Out server is ready!
echo.
echo Server: http://localhost:3001
echo Dashboard: http://localhost:3001/dashboard
echo Health: http://localhost:3001/health
echo.
echo To start your server, run: npm start
echo.
echo Tips:
echo - Use any game account to login to dashboard
echo - Check README.md for more information
echo - Press Ctrl+C to stop the server when running
echo.
echo Press any key to continue...
pause >nul
