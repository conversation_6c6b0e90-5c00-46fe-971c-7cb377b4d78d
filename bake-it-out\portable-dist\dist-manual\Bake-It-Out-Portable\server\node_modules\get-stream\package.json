{"name": "get-stream", "version": "6.0.1", "description": "Get a stream as a string, buffer, or array", "license": "MIT", "repository": "sindresorhus/get-stream", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts", "buffer-stream.js"], "keywords": ["get", "stream", "promise", "concat", "string", "text", "buffer", "read", "data", "consume", "readable", "readablestream", "array", "object"], "devDependencies": {"@types/node": "^14.0.27", "ava": "^2.4.0", "into-stream": "^5.0.0", "tsd": "^0.13.1", "xo": "^0.24.0"}}