'use client'

import { <PERSON>vas, use<PERSON>rame } from '@react-three/fiber'
import { OrbitControls, Environment, ContactShadows, Text, Box, Sphere, Cylinder, Float, Html, Stars, PerspectiveCamera } from '@react-three/drei'
import { Physics } from '@react-three/cannon'
import { Suspense, useRef, useState, useEffect } from 'react'
import { useGame } from '@/contexts/GameContext'
import { useLanguage } from '@/contexts/LanguageContext'
import { useDiscordRPC } from '@/contexts/DiscordRPCContext'
import { Game3DWorld, Game3DScene, Loading3D, Button3D, FloatingPanel, HUD3D } from '@/components/game/Game3DWorld'
import { Kitchen3D } from '@/components/game/Kitchen3D'
import { DiningRoom3D } from '@/components/game/DiningRoom3D'
import { ClientOnly } from '@/components/ui/ClientOnly'
import * as THREE from 'three'

// 3D Storage/Inventory Scene
function Storage3D() {
  const { t } = useLanguage()
  const { inventory } = useGame()
  const [selectedItem, setSelectedItem] = useState<string | null>(null)

  return (
    <group>
      {/* Storage Environment */}
      <Box args={[15, 0.1, 15]} position={[0, -0.05, 0]}>
        <meshStandardMaterial color="#8B7355" />
      </Box>
      
      {/* Storage Shelves */}
      {Array.from({ length: 3 }).map((_, row) =>
        Array.from({ length: 4 }).map((_, col) => (
          <group key={`${row}-${col}`} position={[-6 + col * 4, 0, -4 + row * 4]}>
            {/* Shelf Structure */}
            <Box args={[3, 0.1, 1]} position={[0, 1, 0]}>
              <meshStandardMaterial color="#8B4513" />
            </Box>
            <Box args={[3, 0.1, 1]} position={[0, 2, 0]}>
              <meshStandardMaterial color="#8B4513" />
            </Box>
            <Box args={[0.1, 2, 1]} position={[-1.45, 1, 0]}>
              <meshStandardMaterial color="#8B4513" />
            </Box>
            <Box args={[0.1, 2, 1]} position={[1.45, 1, 0]}>
              <meshStandardMaterial color="#8B4513" />
            </Box>
            
            {/* Ingredient Containers */}
            {inventory.slice(row * 4 + col, row * 4 + col + 1).map((item, index) => (
              <Float key={item.name} speed={1} rotationIntensity={0.1} floatIntensity={0.1}>
                <group 
                  position={[0, 1.3, 0]}
                  onClick={() => setSelectedItem(item.name)}
                >
                  <Cylinder args={[0.4, 0.4, 0.6]}>
                    <meshStandardMaterial color="#F5F5DC" />
                  </Cylinder>
                  <Text
                    position={[0, 0.5, 0]}
                    fontSize={0.3}
                    color="#8B4513"
                    anchorX="center"
                    anchorY="middle"
                  >
                    {item.icon}
                  </Text>
                  <Text
                    position={[0, -0.5, 0]}
                    fontSize={0.2}
                    color="#8B4513"
                    anchorX="center"
                    anchorY="middle"
                  >
                    {item.quantity}
                  </Text>
                </group>
              </Float>
            ))}
          </group>
        ))
      )}
      
      {/* Storage Title */}
      <Text
        position={[0, 4, 0]}
        fontSize={0.8}
        color="#8B4513"
        anchorX="center"
        anchorY="middle"
      >
        📦 {t('storage.title', 'Storage Room')}
      </Text>
    </group>
  )
}

// 3D Shop Scene
function Shop3D() {
  const { t } = useLanguage()
  const [selectedEquipment, setSelectedEquipment] = useState<string | null>(null)

  const shopItems = [
    { name: 'Professional Oven', price: 5000, icon: '🔥', description: 'High-capacity oven' },
    { name: 'Industrial Mixer', price: 3000, icon: '🥄', description: 'Heavy-duty mixer' },
    { name: 'Display Case', price: 2000, icon: '🏪', description: 'Showcase your goods' },
    { name: 'Prep Station', price: 1500, icon: '🔪', description: 'Advanced prep tools' },
  ]

  return (
    <group>
      {/* Shop Environment */}
      <Box args={[20, 0.1, 20]} position={[0, -0.05, 0]}>
        <meshStandardMaterial color="#E6E6FA" />
      </Box>
      
      {/* Shop Counter */}
      <Box args={[8, 1.2, 2]} position={[0, 0.6, -6]}>
        <meshStandardMaterial color="#8B4513" />
      </Box>
      <Box args={[8.2, 0.2, 2.2]} position={[0, 1.3, -6]}>
        <meshStandardMaterial color="#D2B48C" />
      </Box>
      
      {/* Equipment Displays */}
      {shopItems.map((item, index) => (
        <Float key={item.name} speed={1} rotationIntensity={0.1} floatIntensity={0.2}>
          <group 
            position={[-6 + index * 4, 1.5, -6]}
            onClick={() => setSelectedEquipment(item.name)}
          >
            {/* Display Pedestal */}
            <Cylinder args={[0.8, 0.8, 0.2]} position={[0, 0, 0]}>
              <meshStandardMaterial color="#FFD700" metalness={0.8} roughness={0.2} />
            </Cylinder>
            
            {/* Equipment Model */}
            <Box args={[1, 1, 1]} position={[0, 0.8, 0]}>
              <meshStandardMaterial color="#8B4513" />
            </Box>
            
            {/* Price Tag */}
            <Text
              position={[0, 1.5, 0]}
              fontSize={0.3}
              color="#FF6B35"
              anchorX="center"
              anchorY="middle"
            >
              {item.icon} ${item.price}
            </Text>
            
            {/* Item Name */}
            <Text
              position={[0, -0.5, 0]}
              fontSize={0.2}
              color="#8B4513"
              anchorX="center"
              anchorY="middle"
            >
              {item.name}
            </Text>
          </group>
        </Float>
      ))}
      
      {/* Shop Title */}
      <Text
        position={[0, 4, 0]}
        fontSize={0.8}
        color="#8B4513"
        anchorX="center"
        anchorY="middle"
      >
        🛒 {t('shop.title', 'Equipment Shop')}
      </Text>
      
      {/* Shopkeeper */}
      <Float speed={0.5} rotationIntensity={0.1} floatIntensity={0.1}>
        <group position={[0, 1, -4]}>
          <Cylinder args={[0.4, 0.5, 1.5]} position={[0, 0.75, 0]}>
            <meshStandardMaterial color="#4169E1" />
          </Cylinder>
          <Sphere args={[0.3]} position={[0, 1.7, 0]}>
            <meshStandardMaterial color="#FDBCB4" />
          </Sphere>
          <Text
            position={[0, 2.2, 0]}
            fontSize={0.3}
            color="#8B4513"
            anchorX="center"
            anchorY="middle"
          >
            👨‍💼 Shop Owner
          </Text>
        </group>
      </Float>
    </group>
  )
}

// 3D Office/Management Scene
function Office3D() {
  const { t } = useLanguage()
  const { player, achievements } = useGame()

  return (
    <group>
      {/* Office Environment */}
      <Box args={[15, 0.1, 15]} position={[0, -0.05, 0]}>
        <meshStandardMaterial color="#2F4F4F" />
      </Box>
      
      {/* Desk */}
      <Box args={[4, 0.2, 2]} position={[0, 1, 0]}>
        <meshStandardMaterial color="#8B4513" />
      </Box>
      <Box args={[3.8, 0.8, 1.8]} position={[0, 0.4, 0]}>
        <meshStandardMaterial color="#654321" />
      </Box>
      
      {/* Computer */}
      <Box args={[0.8, 0.6, 0.1]} position={[0, 1.4, -0.5]}>
        <meshStandardMaterial color="#000000" />
      </Box>
      <Box args={[0.9, 0.7, 0.05]} position={[0, 1.4, -0.55]}>
        <meshStandardMaterial color="#C0C0C0" />
      </Box>
      
      {/* Achievement Trophies */}
      {achievements.slice(0, 3).map((achievement, index) => (
        <Float key={achievement.id} speed={1} rotationIntensity={0.2} floatIntensity={0.2}>
          <group position={[-2 + index * 2, 1.5, 0.5]}>
            <Cylinder args={[0.2, 0.2, 0.4]} position={[0, 0, 0]}>
              <meshStandardMaterial color="#FFD700" metalness={0.9} roughness={0.1} />
            </Cylinder>
            <Sphere args={[0.15]} position={[0, 0.3, 0]}>
              <meshStandardMaterial color="#FFD700" metalness={0.9} roughness={0.1} />
            </Sphere>
            <Text
              position={[0, -0.3, 0]}
              fontSize={0.15}
              color="#8B4513"
              anchorX="center"
              anchorY="middle"
            >
              {achievement.name}
            </Text>
          </group>
        </Float>
      ))}
      
      {/* Office Title */}
      <Text
        position={[0, 4, 0]}
        fontSize={0.8}
        color="#8B4513"
        anchorX="center"
        anchorY="middle"
      >
        🏢 {t('office.title', 'Management Office')}
      </Text>
      
      {/* Player Stats Display */}
      <Text
        position={[0, 3, 0]}
        fontSize={0.4}
        color="#8B4513"
        anchorX="center"
        anchorY="middle"
      >
        Level {player.level} | ${player.money} | {achievements.filter(a => a.unlocked).length} Achievements
      </Text>
    </group>
  )
}

// Main 3D Game Page
export default function Game3DPage() {
  const { t } = useLanguage()
  const { setGameActivity, setBakingActivity } = useDiscordRPC()
  const [currentScene, setCurrentScene] = useState<Game3DScene>('kitchen')
  const [showMenu, setShowMenu] = useState(false)

  useEffect(() => {
    setGameActivity('Playing 3D Bake It Out')
    setBakingActivity('Managing 3D Bakery')
  }, [setGameActivity, setBakingActivity])

  const renderCurrentScene = () => {
    switch (currentScene) {
      case 'kitchen':
        return <Kitchen3D />
      case 'dining':
        return <DiningRoom3D />
      case 'storage':
        return <Storage3D />
      case 'shop':
        return <Shop3D />
      case 'office':
        return <Office3D />
      default:
        return <Kitchen3D />
    }
  }

  return (
    <ClientOnly fallback={<div className="w-full h-screen bg-gradient-to-b from-blue-900 to-purple-900 flex items-center justify-center text-white text-2xl">Loading 3D Game...</div>}>
      <div className="w-full h-screen bg-gradient-to-b from-blue-900 via-purple-900 to-indigo-900">
        <Canvas
          camera={{ position: [0, 8, 15], fov: 75 }}
          shadows
          dpr={[1, 2]}
          gl={{ antialias: true, alpha: false }}
        >
          <Suspense fallback={<Loading3D />}>
            <Physics gravity={[0, -9.8, 0]}>
              {/* Global Lighting */}
              <ambientLight intensity={0.4} color="#FFF8DC" />
              <directionalLight
                position={[15, 15, 10]}
                intensity={1.2}
                castShadow
                shadow-mapSize-width={2048}
                shadow-mapSize-height={2048}
                shadow-camera-far={50}
                shadow-camera-left={-25}
                shadow-camera-right={25}
                shadow-camera-top={25}
                shadow-camera-bottom={-25}
              />
              <pointLight position={[0, 10, 0]} intensity={0.6} color="#FFE4B5" />
              <spotLight
                position={[0, 15, 0]}
                angle={Math.PI / 3}
                penumbra={0.5}
                intensity={0.8}
                castShadow
              />
              
              {/* Environment */}
              <Environment preset="sunset" background />
              <Stars radius={100} depth={50} count={1000} factor={4} saturation={0} fade speed={1} />
              
              {/* Scene Navigation */}
              <HUD3D>
                <group position={[-10, 4, 0]}>
                  <Button3D 
                    position={[0, 3, 0]} 
                    size={[2.5, 0.8, 0.3]}
                    onClick={() => setCurrentScene('kitchen')}
                    color={currentScene === 'kitchen' ? "#FF6B35" : "#4CAF50"}
                  >
                    🏪 {t('scene.kitchen', 'Kitchen')}
                  </Button3D>
                  
                  <Button3D 
                    position={[0, 2, 0]} 
                    size={[2.5, 0.8, 0.3]}
                    onClick={() => setCurrentScene('dining')}
                    color={currentScene === 'dining' ? "#FF6B35" : "#4CAF50"}
                  >
                    🍽️ {t('scene.dining', 'Dining')}
                  </Button3D>
                  
                  <Button3D 
                    position={[0, 1, 0]} 
                    size={[2.5, 0.8, 0.3]}
                    onClick={() => setCurrentScene('storage')}
                    color={currentScene === 'storage' ? "#FF6B35" : "#4CAF50"}
                  >
                    📦 {t('scene.storage', 'Storage')}
                  </Button3D>
                  
                  <Button3D 
                    position={[0, 0, 0]} 
                    size={[2.5, 0.8, 0.3]}
                    onClick={() => setCurrentScene('shop')}
                    color={currentScene === 'shop' ? "#FF6B35" : "#4CAF50"}
                  >
                    🛒 {t('scene.shop', 'Shop')}
                  </Button3D>
                  
                  <Button3D 
                    position={[0, -1, 0]} 
                    size={[2.5, 0.8, 0.3]}
                    onClick={() => setCurrentScene('office')}
                    color={currentScene === 'office' ? "#FF6B35" : "#4CAF50"}
                  >
                    🏢 {t('scene.office', 'Office')}
                  </Button3D>
                </group>
                
                {/* Game Menu Button */}
                <Button3D 
                  position={[10, 4, 0]} 
                  size={[2, 0.8, 0.3]}
                  color="#9C27B0"
                  onClick={() => setShowMenu(!showMenu)}
                >
                  ⚙️ {t('menu.title', 'Menu')}
                </Button3D>
              </HUD3D>
              
              {/* Current Scene */}
              {renderCurrentScene()}
              
              {/* Game Menu Panel */}
              {showMenu && (
                <FloatingPanel
                  position={[8, 2, 5]}
                  size={[3, 4]}
                  title={t('menu.title', 'Game Menu')}
                  onClose={() => setShowMenu(false)}
                >
                  <div className="space-y-3">
                    <button className="w-full p-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                      💾 {t('menu.save', 'Save Game')}
                    </button>
                    <button className="w-full p-2 bg-green-500 text-white rounded hover:bg-green-600">
                      📁 {t('menu.load', 'Load Game')}
                    </button>
                    <button className="w-full p-2 bg-orange-500 text-white rounded hover:bg-orange-600">
                      ⚙️ {t('menu.settings', 'Settings')}
                    </button>
                    <button className="w-full p-2 bg-red-500 text-white rounded hover:bg-red-600">
                      🏠 {t('menu.mainMenu', 'Main Menu')}
                    </button>
                  </div>
                </FloatingPanel>
              )}
              
              {/* Ground Shadows */}
              <ContactShadows
                position={[0, 0, 0]}
                opacity={0.3}
                scale={30}
                blur={1}
                far={15}
                resolution={256}
                color="#000000"
              />
              
              {/* Camera Controls */}
              <OrbitControls
                enablePan={true}
                enableZoom={true}
                enableRotate={true}
                minDistance={8}
                maxDistance={35}
                minPolarAngle={Math.PI / 6}
                maxPolarAngle={Math.PI / 2.2}
                enableDamping={true}
                dampingFactor={0.05}
                rotateSpeed={0.5}
                zoomSpeed={0.8}
                panSpeed={0.8}
                target={[0, 2, 0]}
              />
            </Physics>
          </Suspense>
        </Canvas>
        
        {/* 3D Game HUD Overlay */}
        <div className="absolute top-4 left-4 bg-black/20 backdrop-blur-sm rounded-lg p-4">
          <h1 className="text-2xl font-bold text-white mb-2">
            🧁 Bake It Out 3D
          </h1>
          <p className="text-white/80 text-sm">
            {t('game.3d.currentScene', 'Current Scene')}: {currentScene}
          </p>
          <p className="text-white/80 text-xs mt-1">
            {t('game.3d.controls', 'Drag to rotate • Scroll to zoom • Click to interact')}
          </p>
        </div>
      </div>
    </ClientOnly>
  )
}
