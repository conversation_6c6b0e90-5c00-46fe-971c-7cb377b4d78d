'use client'

import { Canvas, useFrame } from '@react-three/fiber'
import { OrbitControls, Environment, ContactShadows, Text, Box, Sphere, Cylinder, Float, Html, Stars } from '@react-three/drei'
import { Physics, useBox } from '@react-three/cannon'
import { Suspense, useRef, useState, useEffect } from 'react'
import { useGame } from '@/contexts/GameContext'
import { useLanguage } from '@/contexts/LanguageContext'
import { FloatingPanel, Button3D, HUD3D } from '@/components/game/Game3DWorld'
import * as THREE from 'three'

// 3D Customer Component
function Customer3D({ 
  position, 
  customerData, 
  onClick 
}: { 
  position: [number, number, number]
  customerData: {
    id: string
    name: string
    order: string
    satisfaction: number
    timeWaiting: number
  }
  onClick: () => void 
}) {
  const [ref] = useBox(() => ({ position, mass: 0 }))
  const [hovered, setHovered] = useState(false)
  const customerRef = useRef<THREE.Group>(null)

  useFrame((state) => {
    if (customerRef.current) {
      // Gentle bobbing animation
      customerRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime + position[0]) * 0.1
      
      // Impatience animation based on waiting time
      if (customerData.timeWaiting > 30) {
        customerRef.current.rotation.z = Math.sin(state.clock.elapsedTime * 4) * 0.1
      }
    }
  })

  const getSatisfactionColor = () => {
    if (customerData.satisfaction > 80) return "#4CAF50"
    if (customerData.satisfaction > 50) return "#FF9800"
    return "#F44336"
  }

  const getCustomerColor = () => {
    const colors = ["#FFB6C1", "#87CEEB", "#DDA0DD", "#F0E68C", "#98FB98"]
    return colors[customerData.id.length % colors.length]
  }

  return (
    <Float speed={hovered ? 2 : 1} rotationIntensity={0.1} floatIntensity={hovered ? 0.3 : 0.1}>
      <group 
        ref={customerRef}
        onClick={onClick}
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
      >
        {/* Customer Body */}
        <Cylinder args={[0.3, 0.4, 1.2]} position={[0, 0.6, 0]}>
          <meshStandardMaterial color={getCustomerColor()} />
        </Cylinder>
        
        {/* Customer Head */}
        <Sphere args={[0.25]} position={[0, 1.4, 0]}>
          <meshStandardMaterial color="#FDBCB4" />
        </Sphere>
        
        {/* Eyes */}
        <Sphere args={[0.05]} position={[-0.1, 1.45, 0.2]}>
          <meshStandardMaterial color="#000000" />
        </Sphere>
        <Sphere args={[0.05]} position={[0.1, 1.45, 0.2]}>
          <meshStandardMaterial color="#000000" />
        </Sphere>
        
        {/* Mouth - changes based on satisfaction */}
        <Box 
          args={[0.1, 0.02, 0.02]} 
          position={[0, 1.35, 0.22]}
          rotation={customerData.satisfaction > 50 ? [0, 0, Math.PI / 6] : [0, 0, -Math.PI / 6]}
        >
          <meshStandardMaterial color="#000000" />
        </Box>
        
        {/* Arms */}
        <Cylinder args={[0.08, 0.08, 0.6]} position={[-0.4, 0.8, 0]} rotation={[0, 0, Math.PI / 4]}>
          <meshStandardMaterial color="#FDBCB4" />
        </Cylinder>
        <Cylinder args={[0.08, 0.08, 0.6]} position={[0.4, 0.8, 0]} rotation={[0, 0, -Math.PI / 4]}>
          <meshStandardMaterial color="#FDBCB4" />
        </Cylinder>
        
        {/* Satisfaction Indicator */}
        <Sphere args={[0.1]} position={[0, 1.8, 0]}>
          <meshStandardMaterial 
            color={getSatisfactionColor()}
            emissive={getSatisfactionColor()}
            emissiveIntensity={0.3}
          />
        </Sphere>
        
        {/* Thought Bubble */}
        <Float speed={2} rotationIntensity={0.2} floatIntensity={0.3}>
          <group position={[0.5, 2, 0]}>
            <Sphere args={[0.3]}>
              <meshStandardMaterial color="white" transparent opacity={0.8} />
            </Sphere>
            <Text
              position={[0, 0, 0.31]}
              fontSize={0.2}
              color="#8B4513"
              anchorX="center"
              anchorY="middle"
            >
              {customerData.order.split(' ')[0]}
            </Text>
          </group>
        </Float>
        
        {/* Customer Name */}
        <Text
          position={[0, -0.3, 0]}
          fontSize={0.2}
          color="#8B4513"
          anchorX="center"
          anchorY="middle"
        >
          {customerData.name}
        </Text>
        
        {/* Hover UI */}
        {hovered && (
          <Html position={[0, 2.5, 0]} center>
            <div className="bg-black/80 text-white px-3 py-2 rounded-lg text-sm">
              <p className="font-bold">{customerData.name}</p>
              <p>Order: {customerData.order}</p>
              <p>Satisfaction: {customerData.satisfaction}%</p>
              <p>Waiting: {customerData.timeWaiting}s</p>
            </div>
          </Html>
        )}
      </group>
    </Float>
  )
}

// 3D Table Component
function Table3D({ 
  position, 
  occupied = false,
  customerData = null 
}: { 
  position: [number, number, number]
  occupied?: boolean
  customerData?: any
}) {
  const [hovered, setHovered] = useState(false)

  return (
    <group 
      position={position}
      onPointerOver={() => setHovered(true)}
      onPointerOut={() => setHovered(false)}
    >
      {/* Table Top */}
      <Cylinder args={[0.8, 0.8, 0.1]} position={[0, 0.8, 0]}>
        <meshStandardMaterial 
          color={hovered ? "#DEB887" : "#D2B48C"} 
          roughness={0.8}
          metalness={0.1}
        />
      </Cylinder>
      
      {/* Table Leg */}
      <Cylinder args={[0.1, 0.1, 0.8]} position={[0, 0.4, 0]}>
        <meshStandardMaterial color="#8B4513" />
      </Cylinder>
      
      {/* Table Setting */}
      <Cylinder args={[0.15, 0.15, 0.02]} position={[0.3, 0.86, 0.3]}>
        <meshStandardMaterial color="#F5F5DC" />
      </Cylinder>
      <Cylinder args={[0.05, 0.05, 0.15]} position={[0.3, 0.95, 0.3]}>
        <meshStandardMaterial color="#F5F5DC" />
      </Cylinder>
      
      {/* Cutlery */}
      <Box args={[0.02, 0.2, 0.01]} position={[0.1, 0.86, 0.3]}>
        <meshStandardMaterial color="#C0C0C0" metalness={0.9} roughness={0.1} />
      </Box>
      <Box args={[0.02, 0.15, 0.01]} position={[0.5, 0.86, 0.3]}>
        <meshStandardMaterial color="#C0C0C0" metalness={0.9} roughness={0.1} />
      </Box>
      
      {/* Chair */}
      <group position={[0, 0, 1.2]}>
        <Box args={[0.5, 0.05, 0.5]} position={[0, 0.5, 0]}>
          <meshStandardMaterial color="#8B4513" />
        </Box>
        <Box args={[0.5, 0.8, 0.05]} position={[0, 0.9, -0.225]}>
          <meshStandardMaterial color="#8B4513" />
        </Box>
        <Cylinder args={[0.03, 0.03, 0.5]} position={[-0.2, 0.25, -0.2]}>
          <meshStandardMaterial color="#8B4513" />
        </Cylinder>
        <Cylinder args={[0.03, 0.03, 0.5]} position={[0.2, 0.25, -0.2]}>
          <meshStandardMaterial color="#8B4513" />
        </Cylinder>
        <Cylinder args={[0.03, 0.03, 0.5]} position={[-0.2, 0.25, 0.2]}>
          <meshStandardMaterial color="#8B4513" />
        </Cylinder>
        <Cylinder args={[0.03, 0.03, 0.5]} position={[0.2, 0.25, 0.2]}>
          <meshStandardMaterial color="#8B4513" />
        </Cylinder>
      </group>
      
      {/* Occupied Indicator */}
      {occupied && (
        <Sphere args={[0.1]} position={[0, 1.2, 0]}>
          <meshStandardMaterial 
            color="#FF6B35"
            emissive="#FF6B35"
            emissiveIntensity={0.3}
          />
        </Sphere>
      )}
    </group>
  )
}

// Dining Room Environment
function DiningRoomEnvironment() {
  return (
    <>
      {/* Floor */}
      <Box args={[20, 0.1, 20]} position={[0, -0.05, 0]}>
        <meshStandardMaterial color="#F5DEB3" roughness={0.8} metalness={0.1} />
      </Box>
      
      {/* Floor pattern */}
      {Array.from({ length: 10 }).map((_, i) =>
        Array.from({ length: 10 }).map((_, j) => (
          <Box 
            key={`${i}-${j}`}
            args={[1.8, 0.05, 1.8]} 
            position={[-9 + i * 2, 0, -9 + j * 2]}
          >
            <meshStandardMaterial 
              color={(i + j) % 2 === 0 ? "#F5DEB3" : "#DEB887"} 
              roughness={0.9}
            />
          </Box>
        ))
      )}
      
      {/* Walls */}
      <Box args={[20, 4, 0.2]} position={[0, 2, -10]}>
        <meshStandardMaterial color="#FFEFD5" roughness={0.7} />
      </Box>
      <Box args={[0.2, 4, 20]} position={[-10, 2, 0]}>
        <meshStandardMaterial color="#FFEFD5" roughness={0.7} />
      </Box>
      <Box args={[0.2, 4, 20]} position={[10, 2, 0]}>
        <meshStandardMaterial color="#FFEFD5" roughness={0.7} />
      </Box>
      
      {/* Windows */}
      <Box args={[3, 2, 0.1]} position={[-5, 2.5, -9.9]}>
        <meshStandardMaterial color="#87CEEB" transparent opacity={0.7} />
      </Box>
      <Box args={[3, 2, 0.1]} position={[5, 2.5, -9.9]}>
        <meshStandardMaterial color="#87CEEB" transparent opacity={0.7} />
      </Box>
      
      {/* Ceiling */}
      <Box args={[20, 0.2, 20]} position={[0, 4, 0]}>
        <meshStandardMaterial color="#F0F8FF" roughness={0.5} />
      </Box>
      
      {/* Chandelier */}
      <Float speed={0.5} rotationIntensity={0.1} floatIntensity={0.1}>
        <group position={[0, 3.5, 0]}>
          <Cylinder args={[0.8, 0.8, 0.3]} position={[0, 0, 0]}>
            <meshStandardMaterial 
              color="#FFD700" 
              emissive="#FFD700" 
              emissiveIntensity={0.3}
              metalness={0.8}
              roughness={0.2}
            />
          </Cylinder>
          {Array.from({ length: 6 }).map((_, i) => (
            <Sphere key={i} args={[0.1]} position={[
              Math.cos(i * Math.PI / 3) * 0.6,
              -0.3,
              Math.sin(i * Math.PI / 3) * 0.6
            ]}>
              <meshStandardMaterial 
                color="#FFFACD" 
                emissive="#FFFACD" 
                emissiveIntensity={0.8}
              />
            </Sphere>
          ))}
        </group>
      </Float>
      
      {/* Counter/Bar */}
      <Box args={[6, 1.2, 1]} position={[7, 0.6, -8]}>
        <meshStandardMaterial color="#8B4513" />
      </Box>
      <Box args={[6.2, 0.2, 1.2]} position={[7, 1.3, -8]}>
        <meshStandardMaterial color="#D2B48C" />
      </Box>
      
      {/* Menu Board */}
      <Box args={[2, 3, 0.1]} position={[9, 2.5, -9.8]}>
        <meshStandardMaterial color="#2F4F4F" />
      </Box>
      <Text
        position={[9, 3, -9.7]}
        fontSize={0.3}
        color="#FFD700"
        anchorX="center"
        anchorY="middle"
      >
        📋 MENU
      </Text>
      <Text
        position={[9, 2.5, -9.7]}
        fontSize={0.2}
        color="white"
        anchorX="center"
        anchorY="middle"
      >
        🧁 Cupcakes
      </Text>
      <Text
        position={[9, 2.2, -9.7]}
        fontSize={0.2}
        color="white"
        anchorX="center"
        anchorY="middle"
      >
        🥖 Fresh Bread
      </Text>
      <Text
        position={[9, 1.9, -9.7]}
        fontSize={0.2}
        color="white"
        anchorX="center"
        anchorY="middle"
      >
        🍪 Cookies
      </Text>
    </>
  )
}

// Main Dining Room 3D Component
export function DiningRoom3D() {
  const { t } = useLanguage()
  const { customers, orders } = useGame()
  const [selectedCustomer, setSelectedCustomer] = useState<string | null>(null)
  const [showCustomerDetails, setShowCustomerDetails] = useState(false)

  // Mock customer data for demonstration
  const mockCustomers = [
    { id: '1', name: 'Alice', order: '🧁 Cupcake', satisfaction: 85, timeWaiting: 15 },
    { id: '2', name: 'Bob', order: '🥖 Bread', satisfaction: 60, timeWaiting: 25 },
    { id: '3', name: 'Carol', order: '🍪 Cookies', satisfaction: 40, timeWaiting: 45 },
    { id: '4', name: 'David', order: '🧁 Muffin', satisfaction: 90, timeWaiting: 5 },
  ]

  const handleCustomerClick = (customerId: string) => {
    setSelectedCustomer(customerId)
    setShowCustomerDetails(true)
  }

  return (
    <group>
      {/* Dining Room Environment */}
      <DiningRoomEnvironment />
      
      {/* Tables */}
      <Table3D position={[-4, 0, -4]} occupied={true} />
      <Table3D position={[0, 0, -4]} occupied={true} />
      <Table3D position={[4, 0, -4]} occupied={false} />
      <Table3D position={[-4, 0, 0]} occupied={true} />
      <Table3D position={[0, 0, 0]} occupied={false} />
      <Table3D position={[4, 0, 0]} occupied={true} />
      <Table3D position={[-4, 0, 4]} occupied={false} />
      <Table3D position={[0, 0, 4]} occupied={false} />
      <Table3D position={[4, 0, 4]} occupied={false} />
      
      {/* Customers */}
      <Customer3D 
        position={[-4, 0, -2.8]} 
        customerData={mockCustomers[0]}
        onClick={() => handleCustomerClick(mockCustomers[0].id)}
      />
      <Customer3D 
        position={[0, 0, -2.8]} 
        customerData={mockCustomers[1]}
        onClick={() => handleCustomerClick(mockCustomers[1].id)}
      />
      <Customer3D 
        position={[-4, 0, 1.2]} 
        customerData={mockCustomers[2]}
        onClick={() => handleCustomerClick(mockCustomers[2].id)}
      />
      <Customer3D 
        position={[4, 0, 1.2]} 
        customerData={mockCustomers[3]}
        onClick={() => handleCustomerClick(mockCustomers[3].id)}
      />
      
      {/* HUD Elements */}
      <HUD3D>
        {/* Customer Stats */}
        <Text
          position={[0, 4.5, 8]}
          fontSize={0.4}
          color="#8B4513"
          anchorX="center"
          anchorY="middle"
        >
          👥 Customers: {mockCustomers.length} | Avg Satisfaction: {Math.round(mockCustomers.reduce((acc, c) => acc + c.satisfaction, 0) / mockCustomers.length)}%
        </Text>
        
        {/* Customer Management Button */}
        <Button3D 
          position={[8, 3, 0]} 
          size={[2.5, 0.8, 0.3]}
          color="#2196F3"
          onClick={() => setShowCustomerDetails(!showCustomerDetails)}
        >
          👥 Manage
        </Button3D>
      </HUD3D>
      
      {/* Customer Details Panel */}
      {showCustomerDetails && (
        <FloatingPanel
          position={[6, 2, 3]}
          size={[4, 5]}
          title={t('customers.title', 'Customer Management')}
          onClose={() => setShowCustomerDetails(false)}
        >
          <div className="space-y-3">
            {mockCustomers.map((customer) => (
              <div 
                key={customer.id} 
                className={`p-3 rounded-lg border-2 ${
                  selectedCustomer === customer.id ? 'border-orange-500 bg-orange-50' : 'border-gray-200 bg-white'
                }`}
                onClick={() => setSelectedCustomer(customer.id)}
              >
                <div className="flex justify-between items-center">
                  <h4 className="font-bold">{customer.name}</h4>
                  <span className={`px-2 py-1 rounded text-xs ${
                    customer.satisfaction > 80 ? 'bg-green-100 text-green-800' :
                    customer.satisfaction > 50 ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {customer.satisfaction}%
                  </span>
                </div>
                <p className="text-sm text-gray-600">Order: {customer.order}</p>
                <p className="text-sm text-gray-600">Waiting: {customer.timeWaiting}s</p>
              </div>
            ))}
          </div>
        </FloatingPanel>
      )}
    </group>
  )
}
