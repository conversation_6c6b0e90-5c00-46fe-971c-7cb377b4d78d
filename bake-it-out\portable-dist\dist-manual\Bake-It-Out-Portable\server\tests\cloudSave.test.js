const request = require('supertest');
const app = require('../src/index');
const User = require('../src/models/User');
const CloudSave = require('../src/models/CloudSave');

describe('Cloud Save API', () => {
  let authToken;
  let userId;
  let saveId;

  const testUser = {
    username: 'testuser',
    email: '<EMAIL>',
    password: 'password123'
  };

  const testGameData = {
    player: {
      name: 'Test Player',
      level: 5,
      money: 1000,
      playTime: 3600
    },
    equipment: [
      { id: 'oven1', name: 'Basic Oven', level: 1 }
    ],
    inventory: [
      { name: 'Flour', quantity: 10, cost: 5 }
    ],
    orders: []
  };

  beforeAll(async () => {
    // Clean up test data
    await User.deleteMany({ email: testUser.email });
    await CloudSave.deleteMany({});
  });

  afterAll(async () => {
    // Clean up test data
    await User.deleteMany({ email: testUser.email });
    await CloudSave.deleteMany({});
  });

  describe('Authentication', () => {
    test('should register a new user', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send(testUser)
        .expect(201);

      expect(response.body.user).toBeDefined();
      expect(response.body.user.username).toBe(testUser.username);
      expect(response.body.tokens.accessToken).toBeDefined();
      
      authToken = response.body.tokens.accessToken;
      userId = response.body.user.id;
    });

    test('should login existing user', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          username: testUser.username,
          password: testUser.password
        })
        .expect(200);

      expect(response.body.user).toBeDefined();
      expect(response.body.tokens.accessToken).toBeDefined();
    });

    test('should verify token', async () => {
      const response = await request(app)
        .get('/api/auth/verify')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.valid).toBe(true);
      expect(response.body.user.id).toBe(userId);
    });
  });

  describe('Cloud Saves', () => {
    test('should create a new cloud save', async () => {
      const response = await request(app)
        .post('/api/saves')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          saveName: 'Test Save',
          description: 'A test save file',
          gameData: testGameData,
          saveType: 'manual'
        })
        .expect(201);

      expect(response.body.save).toBeDefined();
      expect(response.body.save.saveName).toBe('Test Save');
      expect(response.body.save.metadata.level).toBe(5);
      expect(response.body.save.metadata.money).toBe(1000);
      
      saveId = response.body.save.id;
    });

    test('should list user saves', async () => {
      const response = await request(app)
        .get('/api/saves')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.saves).toBeDefined();
      expect(response.body.saves.length).toBe(1);
      expect(response.body.saves[0].saveName).toBe('Test Save');
    });

    test('should get specific save with game data', async () => {
      const response = await request(app)
        .get(`/api/saves/${saveId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.save).toBeDefined();
      expect(response.body.save.gameData).toBeDefined();
      expect(response.body.save.gameData.player.name).toBe('Test Player');
    });

    test('should update existing save', async () => {
      const updatedGameData = {
        ...testGameData,
        player: {
          ...testGameData.player,
          level: 6,
          money: 1500
        }
      };

      const response = await request(app)
        .put(`/api/saves/${saveId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          saveName: 'Updated Test Save',
          gameData: updatedGameData
        })
        .expect(200);

      expect(response.body.save.saveName).toBe('Updated Test Save');
      expect(response.body.save.metadata.level).toBe(6);
      expect(response.body.save.metadata.money).toBe(1500);
    });

    test('should get save metadata only', async () => {
      const response = await request(app)
        .get(`/api/saves/${saveId}/metadata`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.metadata).toBeDefined();
      expect(response.body.checksum).toBeDefined();
      expect(response.body.size).toBeDefined();
      expect(response.body.updatedAt).toBeDefined();
    });

    test('should delete save', async () => {
      const response = await request(app)
        .delete(`/api/saves/${saveId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.message).toBe('Save deleted successfully');

      // Verify save is no longer accessible
      await request(app)
        .get(`/api/saves/${saveId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });
  });

  describe('User Management', () => {
    test('should get user profile', async () => {
      const response = await request(app)
        .get('/api/users/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.user).toBeDefined();
      expect(response.body.cloudStats).toBeDefined();
    });

    test('should update user profile', async () => {
      const response = await request(app)
        .put('/api/users/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          profile: {
            displayName: 'Updated Name',
            preferredLanguage: 'cs'
          }
        })
        .expect(200);

      expect(response.body.user.profile.displayName).toBe('Updated Name');
      expect(response.body.user.profile.preferredLanguage).toBe('cs');
    });

    test('should update user settings', async () => {
      const response = await request(app)
        .put('/api/users/settings')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          autoSave: false,
          syncFrequency: 600,
          maxSaves: 20
        })
        .expect(200);

      expect(response.body.settings.autoSave).toBe(false);
      expect(response.body.settings.syncFrequency).toBe(600);
      expect(response.body.settings.maxSaves).toBe(20);
    });

    test('should get user statistics', async () => {
      const response = await request(app)
        .get('/api/users/stats')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.gameStats).toBeDefined();
      expect(response.body.accountInfo).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    test('should reject unauthorized requests', async () => {
      await request(app)
        .get('/api/saves')
        .expect(401);
    });

    test('should reject invalid save data', async () => {
      await request(app)
        .post('/api/saves')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          saveName: '',
          gameData: { invalid: 'data' }
        })
        .expect(400);
    });

    test('should handle non-existent save', async () => {
      await request(app)
        .get('/api/saves/507f1f77bcf86cd799439011')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });
  });

  describe('Rate Limiting', () => {
    test('should enforce rate limits', async () => {
      // Make multiple rapid requests
      const promises = Array(15).fill().map(() =>
        request(app)
          .post('/api/auth/login')
          .send({
            username: 'nonexistent',
            password: 'wrong'
          })
      );

      const responses = await Promise.all(promises);
      
      // Some requests should be rate limited
      const rateLimited = responses.some(res => res.status === 429);
      expect(rateLimited).toBe(true);
    });
  });
});

describe('Health Check', () => {
  test('should return server health status', async () => {
    const response = await request(app)
      .get('/health')
      .expect(200);

    expect(response.body.status).toBe('OK');
    expect(response.body.timestamp).toBeDefined();
    expect(response.body.uptime).toBeDefined();
  });
});

describe('API Information', () => {
  test('should return API information', async () => {
    const response = await request(app)
      .get('/')
      .expect(200);

    expect(response.body.message).toBe('Bake It Out Cloud Save Server');
    expect(response.body.endpoints).toBeDefined();
  });
});
