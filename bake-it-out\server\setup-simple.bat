@echo off
title Bake It Out - Simple Setup

echo.
echo ===============================================
echo           BAKE IT OUT - Simple Setup
echo ===============================================
echo.

REM Check Node.js
echo Checking Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found!
    echo Please install Node.js from: https://nodejs.org/
    pause
    exit /b 1
)
echo SUCCESS: Node.js found!

echo.
echo Installing dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)
echo SUCCESS: Dependencies installed!

echo.
echo Running setup script...
call npm run quick-setup
if %errorlevel% neq 0 (
    echo ERROR: Setup script failed
    pause
    exit /b 1
)

echo.
echo ===============================================
echo                SETUP COMPLETE!
echo ===============================================
echo.
echo Your server is ready! To start it:
echo.
echo   npm start
echo.
echo Then visit:
echo   http://localhost:3001/dashboard
echo.
pause
