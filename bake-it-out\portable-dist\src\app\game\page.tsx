'use client'

import { useState, useEffect } from 'react'
import { useLanguage } from '@/contexts/LanguageContext'
import { GameProvider, useGame } from '@/contexts/GameContext'
import { Button } from '@/components/ui/Button'
import { Equipment } from '@/components/game/Equipment'
import { Order } from '@/components/game/Order'
import { RecipeModal } from '@/components/game/RecipeModal'
import { ShopModal } from '@/components/game/ShopModal'
import { BakingModal } from '@/components/game/BakingModal'
import { NotificationSystem, useNotifications } from '@/components/game/NotificationSystem'
import { LevelUpModal } from '@/components/game/LevelUpModal'
import { AchievementsModal } from '@/components/game/AchievementsModal'
import { SkillTreeModal } from '@/components/game/SkillTreeModal'
import { AutomationModal } from '@/components/game/AutomationModal'
import { EquipmentShopModal } from '@/components/game/EquipmentShopModal'
import { SettingsModal } from '@/components/game/SettingsModal'
import { BakeryManagerModal } from '@/components/game/BakeryManagerModal'
import { SaveLoadModal } from '@/components/game/SaveLoadModal'
import { BakeryLayout } from '@/components/game/BakeryLayout'
import { CustomerManager } from '@/components/game/CustomerManager'
import { DiningRoom } from '@/components/game/DiningRoom'
import { Bakery3D } from '@/components/game/Bakery3D'
import { ClientOnly } from '@/components/ui/ClientOnly'
import { GameToolbar } from '@/components/ui/GameToolbar'
import { GameMenu } from '@/components/menu/GameMenu'
import { useDiscordRPC } from '@/contexts/DiscordRPCContext'

function GameContent() {
  const { t } = useLanguage()
  const { setGameActivity, setBakingActivity } = useDiscordRPC()
  const {
    player,
    equipment,
    inventory,
    orders,
    achievements,
    skills,
    levelUpRewards,
    showLevelUp,
    updateEquipment,
    acceptOrder,
    completeOrder,
    declineOrder,
    generateNewOrder,
    upgradeSkill,
    checkAchievements,
    dismissLevelUp,
    spendMoney
  } = useGame()

  const [showRecipeModal, setShowRecipeModal] = useState(false)
  const [showShopModal, setShowShopModal] = useState(false)
  const [showBakingModal, setShowBakingModal] = useState(false)
  const [showAchievementsModal, setShowAchievementsModal] = useState(false)
  const [showSkillTreeModal, setShowSkillTreeModal] = useState(false)
  const [showAutomationModal, setShowAutomationModal] = useState(false)
  const [showEquipmentShopModal, setShowEquipmentShopModal] = useState(false)
  const [showSettingsModal, setShowSettingsModal] = useState(false)
  const [showBakeryManagerModal, setShowBakeryManagerModal] = useState(false)
  const [showGameMenu, setShowGameMenu] = useState(false)
  const [showSaveLoadModal, setShowSaveLoadModal] = useState(false)
  const [showCustomerManager, setShowCustomerManager] = useState(false)
  const [saveLoadMode, setSaveLoadMode] = useState<'save' | 'load'>('save')
  const [selectedEquipment, setSelectedEquipment] = useState<{id: string, name: string} | null>(null)
  const [gameView, setGameView] = useState<'traditional' | 'layout' | 'dining' | '3d'>('traditional')

  // Game settings state
  const [gameSettings, setGameSettings] = useState({
    language: 'en' as 'en' | 'cs',
    soundEnabled: true,
    musicEnabled: true,
    notificationsEnabled: true,
    autoSaveEnabled: true,
    graphicsQuality: 'medium' as 'low' | 'medium' | 'high',
    animationSpeed: 1,
    showTutorials: true
  })

  // Bakery management state
  const [bakeries, setBakeries] = useState([
    {
      id: 'main',
      name: 'Downtown Delights',
      location: 'City Center',
      specialization: 'general' as const,
      level: 1,
      equipment: [],
      inventory: [],
      orders: [],
      automationJobs: [],
      conveyorBelts: [],
      unlocked: true,
      purchaseCost: 0
    }
  ])
  const [currentBakeryId, setCurrentBakeryId] = useState('main')

  const { notifications, removeNotification, showSuccess, showError, showInfo } = useNotifications()

  const handleEquipmentClick = (equipmentId: string, equipmentName: string) => {
    setSelectedEquipment({ id: equipmentId, name: equipmentName })
    setShowBakingModal(true)
  }

  const handleOrderAccept = (orderId: string) => {
    acceptOrder(orderId)
    showInfo('Order Accepted', 'You have accepted a new order!')
  }

  const handleOrderComplete = (orderId: string) => {
    const order = orders.find(o => o.id === orderId)
    if (order) {
      completeOrder(orderId)
      checkAchievements()
      showSuccess('Order Completed!', `You earned $${order.reward} and gained experience!`)
    }
  }

  const handleOrderDecline = (orderId: string) => {
    declineOrder(orderId)
    showInfo('Order Declined', 'Order has been removed from your queue.')
  }

  const handleSettingsChange = (newSettings: Partial<typeof gameSettings>) => {
    setGameSettings(prev => ({ ...prev, ...newSettings }))
  }

  const handleSwitchBakery = (bakeryId: string) => {
    setCurrentBakeryId(bakeryId)
    showInfo('Bakery Switched', `Switched to ${bakeries.find(b => b.id === bakeryId)?.name}`)
  }

  const handlePurchaseBakery = (bakery: { name: string; purchaseCost: number }) => {
    if (spendMoney(bakery.purchaseCost)) {
      const newBakery = {
        id: Date.now().toString(),
        name: bakery.name,
        location: 'Downtown',
        specialization: 'general' as const,
        level: 1,
        equipment: [],
        inventory: [],
        orders: [],
        automationJobs: [],
        conveyorBelts: [],
        unlocked: true,
        purchaseCost: bakery.purchaseCost
      }
      setBakeries(prev => [...prev, newBakery])
      showSuccess('Bakery Purchased!', `You now own ${bakery.name}!`)
    }
  }

  // Game menu handlers
  const handleSaveGame = () => {
    setSaveLoadMode('save')
    setShowSaveLoadModal(true)
  }

  const handleLoadGame = () => {
    setSaveLoadMode('load')
    setShowSaveLoadModal(true)
  }

  const handleMainMenu = () => {
    if (typeof window !== 'undefined') {
      window.location.href = '/'
    }
  }

  const handleExit = () => {
    if (typeof window !== 'undefined' && window.electronAPI) {
      window.electronAPI.quit()
    }
  }

  // Keyboard shortcuts
  const handleKeyPress = (event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      setShowGameMenu(!showGameMenu)
    }
  }

  // Add keyboard event listener
  if (typeof window !== 'undefined') {
    window.addEventListener('keydown', handleKeyPress)
  }

  // Watch for achievement completions
  useEffect(() => {
    if (typeof window === 'undefined') return // Skip on server side

    const completedAchievements = achievements.filter(a => a.completed)
    const previousCount = parseInt(localStorage.getItem('completedAchievements') || '0')

    if (completedAchievements.length > previousCount) {
      const newAchievements = completedAchievements.slice(previousCount)
      newAchievements.forEach(achievement => {
        showSuccess('Achievement Unlocked!', `🏆 ${achievement.name}`)
      })
      localStorage.setItem('completedAchievements', completedAchievements.length.toString())
    }
  }, [achievements, showSuccess])

  // Update Discord RPC when game state changes
  useEffect(() => {
    const updateDiscordRPC = async () => {
      if (orders && orders.length > 0) {
        // Player is baking
        const currentOrder = orders[0]
        const orderItem = currentOrder.items[0]?.name || 'Unknown item'
        await setBakingActivity(player.level, orderItem)
      } else {
        // Player is managing bakery
        await setGameActivity(player.level, player.money, 'Managing bakery')
      }
    }

    updateDiscordRPC()
  }, [player.level, player.money, orders, setGameActivity, setBakingActivity])

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-yellow-50">
      {/* Game Toolbar */}
      <GameToolbar
        player={player}
        onOpenMenu={() => setShowGameMenu(true)}
        onOpenAchievements={() => setShowAchievementsModal(true)}
        onOpenSkills={() => setShowSkillTreeModal(true)}
        onOpenBakeries={() => setShowBakeryManagerModal(true)}
        onOpenSettings={() => setShowSettingsModal(true)}
      />

      {/* View Selector */}
      <div className="max-w-7xl mx-auto px-6 pb-4">
        <div className="flex justify-center space-x-2">
          <button
            onClick={() => setGameView('traditional')}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              gameView === 'traditional'
                ? 'bg-orange-500 text-white'
                : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'
            }`}
          >
            📊 {t('game.view.traditional', 'Traditional View')}
          </button>
          <button
            onClick={() => setGameView('layout')}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              gameView === 'layout'
                ? 'bg-orange-500 text-white'
                : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'
            }`}
          >
            🏪 {t('game.view.layout', 'Bakery Layout')}
          </button>
          <button
            onClick={() => setGameView('dining')}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              gameView === 'dining'
                ? 'bg-orange-500 text-white'
                : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'
            }`}
          >
            🍽️ {t('game.view.dining', 'Dining Room')}
          </button>
          <button
            onClick={() => setGameView('3d')}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              gameView === '3d'
                ? 'bg-orange-500 text-white'
                : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'
            }`}
          >
            🎮 {t('game.view.3d', '3D Bakery')}
          </button>
          <button
            onClick={() => setShowCustomerManager(true)}
            className="px-4 py-2 rounded-lg font-medium bg-blue-500 text-white hover:bg-blue-600 transition-colors"
          >
            👥 {t('game.view.customers', 'Customer Manager')}
          </button>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-6 grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Main Game Area */}
        <div className="lg:col-span-3 space-y-6">
          {gameView === 'layout' && (
            <ClientOnly fallback={<div className="bg-white rounded-lg shadow-md p-6 text-center">Loading bakery layout...</div>}>
              <BakeryLayout
                equipment={equipment}
                onEquipmentClick={handleEquipmentClick}
              />
            </ClientOnly>
          )}

          {gameView === 'dining' && (
            <ClientOnly fallback={<div className="bg-white rounded-lg shadow-md p-6 text-center">Loading dining room...</div>}>
              <DiningRoom
                onCustomerClick={(customerId) => {
                  setShowCustomerManager(true)
                }}
              />
            </ClientOnly>
          )}

          {gameView === '3d' && (
            <ClientOnly fallback={<div className="bg-white rounded-lg shadow-md p-6 text-center">Loading 3D bakery...</div>}>
              <Bakery3D />
            </ClientOnly>
          )}

          {gameView === 'traditional' && (
            <>
              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-semibold text-orange-800">{t('kitchen.title')}</h2>
                  <div className="text-sm text-gray-600">
                    Current: {bakeries.find(b => b.id === currentBakeryId)?.name}
                  </div>
                </div>

                {/* Equipment Grid */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {equipment.map((eq) => (
                    <Equipment
                      key={eq.id}
                      equipment={eq}
                      onClick={handleEquipmentClick}
                    />
                  ))}
                </div>
              </div>

              {/* Inventory */}
              <ClientOnly fallback={<div className="bg-white rounded-lg shadow-md p-6 text-center">Loading inventory...</div>}>
                <div className="bg-white rounded-lg shadow-md p-6">
                  <h2 className="text-xl font-semibold text-orange-800 mb-4">{t('inventory.title')}</h2>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {inventory.map((ingredient) => (
                      <div key={ingredient.name} className="bg-gray-50 p-3 rounded-lg text-center">
                        <div className="text-2xl mb-1">{ingredient.icon}</div>
                        <div className="font-medium text-gray-800">{t(`ingredient.${ingredient.name.toLowerCase().replace(/\s+/g, '_')}`, ingredient.name)}</div>
                        <div className="text-sm text-gray-600">{t('inventory.quantity', { qty: ingredient.quantity.toString() })}</div>
                        <div className="text-xs text-green-600">{ingredient.cost} {t('currency.czk', 'Kč')} {t('inventory.price_per_unit') || 'per unit'}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </ClientOnly>
            </>
          )}
        </div>

        {/* Orders Panel */}
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-orange-800">{t('orders.title')}</h2>
              <Button
                size="sm"
                variant="primary"
                onClick={generateNewOrder}
              >
                {t('orders.new_order', '+ New Order')}
              </Button>
            </div>
            <div className="space-y-4">
              {orders.map((order) => (
                <Order
                  key={order.id}
                  order={order}
                  onAccept={handleOrderAccept}
                  onDecline={handleOrderDecline}
                  onComplete={handleOrderComplete}
                />
              ))}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-orange-800 mb-4">{t('quick_actions.title', 'Quick Actions')}</h2>
            <div className="space-y-2">
              <Button
                variant="secondary"
                size="sm"
                className="w-full"
                onClick={() => setShowShopModal(true)}
              >
                {t('quick_actions.buy_ingredients', '🛒 Buy Ingredients')}
              </Button>
              <Button
                variant="secondary"
                size="sm"
                className="w-full"
                onClick={() => setShowRecipeModal(true)}
              >
                {t('quick_actions.view_recipes', '📖 View Recipes')}
              </Button>
              <Button
                variant="secondary"
                size="sm"
                className="w-full"
                onClick={() => setShowEquipmentShopModal(true)}
              >
                {t('quick_actions.equipment_shop', '🔧 Equipment Shop')}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Modals */}
      <RecipeModal
        isOpen={showRecipeModal}
        onClose={() => setShowRecipeModal(false)}
      />
      <ShopModal
        isOpen={showShopModal}
        onClose={() => setShowShopModal(false)}
      />
      <BakingModal
        isOpen={showBakingModal}
        onClose={() => setShowBakingModal(false)}
        equipmentId={selectedEquipment?.id || ''}
        equipmentName={selectedEquipment?.name || ''}
      />
      <AchievementsModal
        isOpen={showAchievementsModal}
        onClose={() => setShowAchievementsModal(false)}
        achievements={achievements}
      />
      <SkillTreeModal
        isOpen={showSkillTreeModal}
        onClose={() => setShowSkillTreeModal(false)}
        skills={skills}
        skillPoints={player.skillPoints}
        playerLevel={player.level}
        onUpgradeSkill={upgradeSkill}
      />
      <LevelUpModal
        isOpen={showLevelUp}
        onClose={dismissLevelUp}
        newLevel={player.level}
        rewards={levelUpRewards}
      />
      <AutomationModal
        isOpen={showAutomationModal}
        onClose={() => setShowAutomationModal(false)}
      />
      <EquipmentShopModal
        isOpen={showEquipmentShopModal}
        onClose={() => setShowEquipmentShopModal(false)}
        onShowSuccess={showSuccess}
      />
      <SettingsModal
        isOpen={showSettingsModal}
        onClose={() => setShowSettingsModal(false)}
        settings={gameSettings}
        onSettingsChange={handleSettingsChange}
      />
      <BakeryManagerModal
        isOpen={showBakeryManagerModal}
        onClose={() => setShowBakeryManagerModal(false)}
        bakeries={bakeries}
        currentBakeryId={currentBakeryId}
        onSwitchBakery={handleSwitchBakery}
        onPurchaseBakery={handlePurchaseBakery}
        playerMoney={player.money}
      />
      <ClientOnly>
        <CustomerManager
          isOpen={showCustomerManager}
          onClose={() => setShowCustomerManager(false)}
        />
      </ClientOnly>
      <GameMenu
        isOpen={showGameMenu}
        onClose={() => setShowGameMenu(false)}
        onSaveGame={handleSaveGame}
        onLoadGame={handleLoadGame}
        onSettings={() => setShowSettingsModal(true)}
        onMainMenu={handleMainMenu}
        onExit={typeof window !== 'undefined' && window.electronAPI ? handleExit : undefined}
      />
      <SaveLoadModal
        isOpen={showSaveLoadModal}
        onClose={() => setShowSaveLoadModal(false)}
        mode={saveLoadMode}
        onSaveSuccess={() => {
          showSuccess('Game Saved!', 'Your progress has been saved successfully.')
          setShowSaveLoadModal(false)
        }}
        onLoadSuccess={() => {
          showSuccess('Game Loaded!', 'Your saved progress has been loaded.')
          setShowSaveLoadModal(false)
        }}
      />

      {/* Notification System */}
      <NotificationSystem
        notifications={notifications}
        onRemove={removeNotification}
      />
    </div>
  )
}

export default function GamePage() {
  return (
    <GameProvider>
      <GameContent />
    </GameProvider>
  )
}
