directories:
  output: dist
  buildResources: build
appId: com.bakeitout.app
productName: Bake It Out
files:
  - filter:
      - out/**/*
      - electron/**/*
      - package.json
extraResources:
  - from: server
    to: server
    filter:
      - '**/*'
      - '!node_modules'
win:
  target:
    - target: nsis
      arch:
        - x64
        - ia32
    - target: portable
      arch:
        - x64
        - ia32
  icon: electron/assets/icon.ico
  verifyUpdateCodeSignature: false
mac:
  target:
    - target: dmg
      arch:
        - x64
        - arm64
    - target: zip
      arch:
        - x64
        - arm64
  icon: electron/assets/icon.icns
  category: public.app-category.games
linux:
  target:
    - target: AppImage
      arch:
        - x64
    - target: deb
      arch:
        - x64
    - target: rpm
      arch:
        - x64
  icon: electron/assets/icon.png
  category: Game
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: Bake It Out
dmg:
  title: Bake It Out
  contents:
    - x: 130
      'y': 220
    - x: 410
      'y': 220
      type: link
      path: /Applications
electronVersion: 37.2.3
