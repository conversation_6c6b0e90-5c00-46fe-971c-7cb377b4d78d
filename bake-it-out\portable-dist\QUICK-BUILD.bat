@echo off
echo 🚀 Quick Build - Bake It Out Executable
echo ========================================

echo.
echo 📦 Building Next.js application...
call npm run build
if %errorlevel% neq 0 (
    echo ❌ Failed to build Next.js application
    pause
    exit /b 1
)

echo.
echo ✅ Next.js build completed!

echo.
echo 🔧 Using global electron-builder...
call npx --yes electron-builder@26.0.12 --win --publish=never
if %errorlevel% neq 0 (
    echo ❌ Failed to build executable with global electron-builder
    echo.
    echo 💡 Trying alternative approach...
    echo.
    
    echo 📥 Downloading electron-builder...
    call npx --yes @electron/rebuild
    call npx --yes electron-builder --win --publish=never
    
    if %errorlevel% neq 0 (
        echo ❌ Alternative approach also failed
        echo.
        echo 🛠️ Manual steps required:
        echo 1. Close all Electron processes
        echo 2. Delete node_modules folder
        echo 3. Run: npm install
        echo 4. Run: npm run dist-win
        pause
        exit /b 1
    )
)

echo.
echo 🎉 Build completed successfully!
echo.
echo 📁 Check the 'dist' folder for your executable files
echo.

pause
