#!/bin/bash

# Bake It Out Server Setup Script
# This script sets up the server environment for development or production

set -e

echo "🚀 Setting up Bake It Out Server..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
check_node() {
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_success "Node.js is installed: $NODE_VERSION"
        
        # Check if version is 18 or higher
        MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
        if [ "$MAJOR_VERSION" -lt 18 ]; then
            print_error "Node.js version 18 or higher is required. Current version: $NODE_VERSION"
            exit 1
        fi
    else
        print_error "Node.js is not installed. Please install Node.js 18+ first."
        exit 1
    fi
}

# Check if npm is installed
check_npm() {
    if command -v npm &> /dev/null; then
        NPM_VERSION=$(npm --version)
        print_success "npm is installed: $NPM_VERSION"
    else
        print_error "npm is not installed. Please install npm first."
        exit 1
    fi
}

# Check if MongoDB is available
check_mongodb() {
    if command -v mongod &> /dev/null; then
        print_success "MongoDB is installed locally"
    elif command -v docker &> /dev/null; then
        print_warning "MongoDB not found locally, but Docker is available"
        print_status "You can use Docker to run MongoDB: docker run -d -p 27017:27017 mongo:6.0"
    else
        print_warning "MongoDB not found. Please install MongoDB or use a cloud service"
    fi
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    if [ "$NODE_ENV" = "production" ]; then
        npm ci --only=production
    else
        npm install
    fi
    
    print_success "Dependencies installed successfully"
}

# Setup environment file
setup_environment() {
    if [ ! -f ".env" ]; then
        print_status "Creating environment file..."
        cp .env.example .env
        
        # Generate a random JWT secret
        JWT_SECRET=$(openssl rand -base64 32 2>/dev/null || head -c 32 /dev/urandom | base64)
        
        # Update the .env file with generated secret
        if command -v sed &> /dev/null; then
            sed -i.bak "s/your-super-secret-jwt-key-change-in-production-make-it-very-long-and-random/$JWT_SECRET/" .env
            rm .env.bak 2>/dev/null || true
        fi
        
        print_success "Environment file created: .env"
        print_warning "Please review and update the .env file with your specific configuration"
    else
        print_success "Environment file already exists: .env"
    fi
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    mkdir -p logs
    mkdir -p uploads
    mkdir -p backups
    
    print_success "Directories created"
}

# Setup MongoDB with Docker (optional)
setup_mongodb_docker() {
    if command -v docker &> /dev/null; then
        read -p "Would you like to start MongoDB with Docker? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            print_status "Starting MongoDB with Docker..."
            
            # Check if MongoDB container already exists
            if docker ps -a | grep -q "bake-it-out-mongo"; then
                print_status "MongoDB container already exists, starting it..."
                docker start bake-it-out-mongo
            else
                print_status "Creating new MongoDB container..."
                docker run -d \
                    --name bake-it-out-mongo \
                    -p 27017:27017 \
                    -v bake-it-out-mongo-data:/data/db \
                    mongo:6.0
            fi
            
            print_success "MongoDB is running on port 27017"
        fi
    fi
}

# Test database connection
test_connection() {
    print_status "Testing database connection..."
    
    # Start the server in test mode
    timeout 10s npm start &
    SERVER_PID=$!
    
    sleep 5
    
    # Test health endpoint
    if curl -s http://localhost:3001/health > /dev/null; then
        print_success "Server is responding on port 3001"
    else
        print_warning "Server health check failed"
    fi
    
    # Kill the test server
    kill $SERVER_PID 2>/dev/null || true
    wait $SERVER_PID 2>/dev/null || true
}

# Setup PM2 for production
setup_pm2() {
    if [ "$NODE_ENV" = "production" ]; then
        if command -v pm2 &> /dev/null; then
            print_status "Setting up PM2 configuration..."
            
            cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'bake-it-out-server',
    script: 'src/index.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
EOF
            
            print_success "PM2 configuration created: ecosystem.config.js"
            print_status "To start with PM2: pm2 start ecosystem.config.js"
        else
            print_warning "PM2 not installed. Install with: npm install -g pm2"
        fi
    fi
}

# Main setup function
main() {
    echo "🧁 Bake It Out Server Setup"
    echo "=========================="
    echo
    
    # Check prerequisites
    check_node
    check_npm
    check_mongodb
    
    echo
    
    # Setup
    install_dependencies
    setup_environment
    create_directories
    
    echo
    
    # Optional setups
    setup_mongodb_docker
    setup_pm2
    
    echo
    
    # Test
    if [ "$1" != "--skip-test" ]; then
        test_connection
    fi
    
    echo
    print_success "🎉 Setup completed successfully!"
    echo
    echo "Next steps:"
    echo "1. Review and update the .env file"
    echo "2. Start the server: npm run dev (development) or npm start (production)"
    echo "3. Test the API: curl http://localhost:3001/health"
    echo
    echo "For more information, see README.md"
}

# Run main function
main "$@"
