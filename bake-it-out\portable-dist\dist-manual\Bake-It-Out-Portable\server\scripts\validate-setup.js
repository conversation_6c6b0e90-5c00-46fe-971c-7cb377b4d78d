#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bright: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

async function validateSetup() {
  console.clear();
  
  log('╔══════════════════════════════════════════════════════════════╗', 'bright');
  log('║                🧁 BAKE IT OUT - SETUP VALIDATOR              ║', 'bright');
  log('║                                                              ║', 'bright');
  log('║  Checking if your server is properly configured...          ║', 'bright');
  log('╚══════════════════════════════════════════════════════════════╝', 'bright');
  log('');
  
  let allGood = true;
  let warnings = 0;
  
  // Check Node.js version
  log('🔍 Checking Node.js...', 'blue');
  try {
    const version = execSync('node --version', { encoding: 'utf8' }).trim();
    const majorVersion = parseInt(version.slice(1).split('.')[0]);
    
    if (majorVersion >= 18) {
      logSuccess(`Node.js ${version} (✓ Compatible)`);
    } else {
      logError(`Node.js ${version} (✗ Requires 18+)`);
      allGood = false;
    }
  } catch (error) {
    logError('Node.js not found');
    allGood = false;
  }
  
  // Check package.json
  log('📦 Checking package.json...', 'blue');
  if (fs.existsSync('package.json')) {
    logSuccess('package.json found');
    
    try {
      const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      if (pkg.scripts && pkg.scripts.start) {
        logSuccess('Start script configured');
      } else {
        logError('Start script missing');
        allGood = false;
      }
    } catch (error) {
      logError('Invalid package.json');
      allGood = false;
    }
  } else {
    logError('package.json not found');
    allGood = false;
  }
  
  // Check node_modules
  log('📚 Checking dependencies...', 'blue');
  if (fs.existsSync('node_modules')) {
    logSuccess('Dependencies installed');
    
    // Check key dependencies
    const keyDeps = ['express', 'mongoose', 'socket.io', 'jsonwebtoken', 'bcryptjs'];
    keyDeps.forEach(dep => {
      if (fs.existsSync(`node_modules/${dep}`)) {
        logSuccess(`${dep} installed`);
      } else {
        logError(`${dep} missing`);
        allGood = false;
      }
    });
  } else {
    logError('Dependencies not installed (run: npm install)');
    allGood = false;
  }
  
  // Check .env file
  log('⚙️ Checking configuration...', 'blue');
  if (fs.existsSync('.env')) {
    logSuccess('.env file found');
    
    const envContent = fs.readFileSync('.env', 'utf8');
    
    // Check required variables
    const requiredVars = ['PORT', 'MONGODB_URI', 'JWT_SECRET'];
    requiredVars.forEach(varName => {
      if (envContent.includes(`${varName}=`)) {
        logSuccess(`${varName} configured`);
      } else {
        logError(`${varName} missing in .env`);
        allGood = false;
      }
    });
    
    // Check for default values that should be changed
    if (envContent.includes('your-super-secret-jwt-key-change-in-production')) {
      logWarning('JWT_SECRET still using default value (should be changed for production)');
      warnings++;
    }
  } else {
    logError('.env file not found (copy from .env.example)');
    allGood = false;
  }
  
  // Check directories
  log('📁 Checking directories...', 'blue');
  const requiredDirs = ['src', 'logs', 'uploads', 'backups'];
  requiredDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
      logSuccess(`${dir}/ directory exists`);
    } else {
      if (dir === 'src') {
        logError(`${dir}/ directory missing`);
        allGood = false;
      } else {
        logWarning(`${dir}/ directory missing (will be created automatically)`);
        warnings++;
      }
    }
  });
  
  // Check source files
  log('📄 Checking source files...', 'blue');
  const requiredFiles = [
    'src/index.js',
    'src/routes/auth.js',
    'src/routes/saves.js',
    'src/routes/multiplayer.js',
    'src/models/User.js',
    'src/models/CloudSave.js'
  ];
  
  requiredFiles.forEach(file => {
    if (fs.existsSync(file)) {
      logSuccess(`${file} found`);
    } else {
      logError(`${file} missing`);
      allGood = false;
    }
  });
  
  // Check MongoDB connection
  log('🗄️ Checking database...', 'blue');
  try {
    // Check if MongoDB is running on default port
    const netstat = execSync('netstat -an', { encoding: 'utf8' });
    if (netstat.includes('27017')) {
      logSuccess('MongoDB appears to be running (port 27017)');
    } else {
      logWarning('MongoDB not detected on port 27017');
      logInfo('Options: Docker, local install, or MongoDB Atlas');
      warnings++;
    }
  } catch (error) {
    logWarning('Could not check MongoDB status');
    warnings++;
  }
  
  // Check Docker (optional)
  log('🐳 Checking Docker (optional)...', 'blue');
  try {
    execSync('docker --version', { stdio: 'ignore' });
    logSuccess('Docker available (can use for MongoDB)');
  } catch (error) {
    logInfo('Docker not found (optional - can install MongoDB locally)');
  }
  
  // Summary
  log('');
  log('╔══════════════════════════════════════════════════════════════╗', 'bright');
  log('║                        VALIDATION SUMMARY                    ║', 'bright');
  log('╚══════════════════════════════════════════════════════════════╝', 'bright');
  log('');
  
  if (allGood && warnings === 0) {
    logSuccess('🎉 Perfect! Your setup is complete and ready to go!');
    log('');
    log('Next steps:', 'green');
    log('  1. Start the server: npm start', 'green');
    log('  2. Open dashboard: http://localhost:3001/dashboard', 'green');
    log('  3. Test health check: http://localhost:3001/health', 'green');
  } else if (allGood && warnings > 0) {
    logSuccess('✅ Your setup is functional with minor warnings');
    log(`⚠️  ${warnings} warning(s) found - see above for details`, 'yellow');
    log('');
    log('You can start the server, but consider addressing the warnings:', 'yellow');
    log('  • npm start (to start the server)', 'yellow');
    log('  • Check the warnings above for improvements', 'yellow');
  } else {
    logError('❌ Setup validation failed');
    log('');
    log('Please fix the errors above before starting the server:', 'red');
    log('  • Run: npm install (if dependencies missing)', 'red');
    log('  • Copy: .env.example to .env (if config missing)', 'red');
    log('  • Install: MongoDB or Docker (for database)', 'red');
    log('  • Run: npm run setup-easy (for automatic setup)', 'red');
  }
  
  log('');
  log('💡 Need help? Check the README.md file or run: npm run setup-easy', 'blue');
  log('');
}

if (require.main === module) {
  validateSetup();
}

module.exports = { validateSetup };
