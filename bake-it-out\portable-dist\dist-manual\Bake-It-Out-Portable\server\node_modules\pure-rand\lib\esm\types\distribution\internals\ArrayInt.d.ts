export type ArrayInt = {
    sign: -1 | 1;
    data: number[];
};
export declare function addArrayIntToNew(arrayIntA: ArrayInt, arrayIntB: ArrayInt): ArrayInt;
export declare function addOneToPositiveArrayInt(arrayInt: ArrayInt): ArrayInt;
export declare function substractArrayIntToNew(arrayIntA: ArrayInt, arrayIntB: ArrayInt): ArrayInt;
export declare function trimArrayIntInplace(arrayInt: ArrayInt): ArrayInt;
export type ArrayInt64 = Required<ArrayInt> & {
    data: [number, number];
};
export declare function fromNumberToArrayInt64(out: ArrayInt64, n: number): ArrayInt64;
export declare function substractArrayInt64(out: ArrayInt64, arrayIntA: ArrayInt64, arrayIntB: ArrayInt64): ArrayInt64;
