#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync, spawn } = require('child_process');
const crypto = require('crypto');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bright: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logStep(step, total, message) {
  log(`[${step}/${total}] ${message}`, 'blue');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

async function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function checkNodeVersion() {
  try {
    const version = execSync('node --version', { encoding: 'utf8' }).trim();
    const majorVersion = parseInt(version.slice(1).split('.')[0]);
    
    if (majorVersion < 18) {
      logError(`Node.js ${version} found, but version 18+ is required`);
      log('Please update Node.js: https://nodejs.org/', 'yellow');
      process.exit(1);
    }
    
    logSuccess(`Node.js ${version} found!`);
    return true;
  } catch (error) {
    logError('Node.js not found!');
    log('Please install Node.js 18+: https://nodejs.org/', 'yellow');
    process.exit(1);
  }
}

async function installDependencies() {
  try {
    log('Installing dependencies...', 'cyan');
    execSync('npm install', { stdio: 'pipe' });
    logSuccess('Dependencies installed!');
    return true;
  } catch (error) {
    logError('Failed to install dependencies');
    log('Error details:', 'red');
    log(error.message, 'red');
    log('Try running: npm install', 'yellow');
    return false;
  }
}

async function setupEnvironment() {
  const envPath = '.env';
  const envExamplePath = '.env.example';
  
  if (!fs.existsSync(envPath)) {
    if (fs.existsSync(envExamplePath)) {
      fs.copyFileSync(envExamplePath, envPath);
      logSuccess('Environment file created from template!');
    } else {
      // Create basic .env file
      const basicEnv = `NODE_ENV=development
PORT=3001
MONGODB_URI=mongodb://localhost:27017/bake-it-out
JWT_SECRET=${crypto.randomBytes(32).toString('hex')}
JWT_EXPIRES_IN=7d
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3002
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
AUTH_RATE_LIMIT_MAX=10
`;
      fs.writeFileSync(envPath, basicEnv);
      logSuccess('Basic environment file created!');
    }
  } else {
    logSuccess('Environment file already exists!');
  }
  
  // Generate secure JWT secret if using default
  let envContent = fs.readFileSync(envPath, 'utf8');
  if (envContent.includes('your-super-secret-jwt-key-change-in-production')) {
    const newSecret = crypto.randomBytes(32).toString('hex');
    envContent = envContent.replace(
      'your-super-secret-jwt-key-change-in-production',
      newSecret
    );
    fs.writeFileSync(envPath, envContent);
    logSuccess('Secure JWT secret generated!');
  }
}

async function createDirectories() {
  const dirs = ['logs', 'uploads', 'backups'];
  
  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  });
  
  logSuccess('Directories created!');
}

async function checkDocker() {
  try {
    execSync('docker --version', { stdio: 'ignore' });
    return true;
  } catch (error) {
    return false;
  }
}

async function setupMongoDB() {
  const hasDocker = await checkDocker();
  
  if (hasDocker) {
    log('🐳 Docker found! Setting up MongoDB with Docker...', 'cyan');
    
    try {
      // Check if container exists
      try {
        execSync('docker ps -a | grep bake-it-out-mongo', { stdio: 'ignore' });
        log('Starting existing MongoDB container...', 'cyan');
        execSync('docker start bake-it-out-mongo', { stdio: 'ignore' });
      } catch (error) {
        log('Creating new MongoDB container...', 'cyan');
        execSync('docker run -d --name bake-it-out-mongo -p 27017:27017 mongo:6.0', { stdio: 'ignore' });
      }
      
      log('Waiting for MongoDB to start...', 'cyan');
      await sleep(5000);
      logSuccess('MongoDB is running with Docker!');
      return true;
    } catch (error) {
      logWarning('Failed to start MongoDB with Docker');
    }
  }
  
  // Check if MongoDB is running locally
  try {
    const netstat = execSync('netstat -an', { encoding: 'utf8' });
    if (netstat.includes('27017')) {
      logSuccess('MongoDB is already running locally!');
      return true;
    }
  } catch (error) {
    // netstat might not be available
  }
  
  logWarning('MongoDB not detected');
  log('Options:', 'yellow');
  log('  1. Install Docker (recommended): https://docker.com/', 'yellow');
  log('  2. Install MongoDB locally: https://mongodb.com/', 'yellow');
  log('  3. Use MongoDB Atlas (cloud): https://cloud.mongodb.com/', 'yellow');
  log('  4. Continue anyway (you can set up database later)', 'yellow');
  log('', 'reset');
  log('For now, continuing with default settings...', 'cyan');
  log('You can change the database URL in .env file later', 'cyan');
  
  return false;
}

async function testServer() {
  log('Testing server startup...', 'cyan');
  
  return new Promise((resolve) => {
    const server = spawn('node', ['src/index.js'], {
      stdio: 'pipe',
      detached: false
    });
    
    let serverStarted = false;
    
    server.stdout.on('data', (data) => {
      const output = data.toString();
      if (output.includes('running on port') || output.includes('Server running')) {
        serverStarted = true;
        logSuccess('Server started successfully!');
        server.kill();
        resolve(true);
      }
    });
    
    server.stderr.on('data', (data) => {
      const error = data.toString();
      if (error.includes('EADDRINUSE')) {
        logWarning('Port 3001 is already in use');
        server.kill();
        resolve(false);
      }
    });
    
    // Timeout after 10 seconds
    setTimeout(() => {
      if (!serverStarted) {
        logWarning('Server test timed out (this might be normal)');
        server.kill();
        resolve(false);
      }
    }, 10000);
  });
}

async function main() {
  console.clear();
  
  log('╔══════════════════════════════════════════════════════════════╗', 'bright');
  log('║                    🧁 BAKE IT OUT 🧁                         ║', 'bright');
  log('║                   Easy Server Setup                          ║', 'bright');
  log('║                                                              ║', 'bright');
  log('║  This will set up everything you need in under 30 seconds!  ║', 'bright');
  log('╚══════════════════════════════════════════════════════════════╝', 'bright');
  log('');
  
  const totalSteps = 7;
  let currentStep = 1;
  
  try {
    logStep(currentStep++, totalSteps, '🔍 Checking Node.js...');
    await checkNodeVersion();
    
    logStep(currentStep++, totalSteps, '📦 Installing dependencies...');
    await installDependencies();
    
    logStep(currentStep++, totalSteps, '⚙️ Setting up configuration...');
    await setupEnvironment();
    
    logStep(currentStep++, totalSteps, '🗂️ Creating directories...');
    await createDirectories();
    
    logStep(currentStep++, totalSteps, '🐳 Setting up MongoDB...');
    await setupMongoDB();
    
    logStep(currentStep++, totalSteps, '🧪 Testing server...');
    await testServer();
    
    logStep(currentStep++, totalSteps, '🚀 Setup complete!');
    
    log('');
    log('╔══════════════════════════════════════════════════════════════╗', 'green');
    log('║                        🎉 SUCCESS! 🎉                       ║', 'green');
    log('║                                                              ║', 'green');
    log('║  Your Bake It Out server is ready to launch!                ║', 'green');
    log('║                                                              ║', 'green');
    log('║  🌐 Server: http://localhost:3001                           ║', 'green');
    log('║  📊 Dashboard: http://localhost:3001/dashboard              ║', 'green');
    log('║  🔍 Health: http://localhost:3001/health                    ║', 'green');
    log('║                                                              ║', 'green');
    log('║  To start your server, run: npm start                       ║', 'green');
    log('╚══════════════════════════════════════════════════════════════╝', 'green');
    log('');
    
    log('💡 Quick tips:', 'cyan');
    log('  • Use any game account to login to the dashboard', 'cyan');
    log('  • Check README.md for more configuration options', 'cyan');
    log('  • Need help? Visit the documentation', 'cyan');
    log('');
    
  } catch (error) {
    logError('Setup failed!');
    log(error.message, 'red');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { main };
