{"name": "sparse-bitfield", "version": "3.0.3", "description": "Bitfield that allocates a series of small buffers to support sparse bits without allocating a massive buffer", "main": "index.js", "dependencies": {"memory-pager": "^1.0.2"}, "devDependencies": {"buffer-alloc": "^1.1.0", "standard": "^9.0.0", "tape": "^4.6.3"}, "scripts": {"test": "standard && tape test.js"}, "repository": {"type": "git", "url": "https://github.com/mafintosh/sparse-bitfield.git"}, "author": "<PERSON> (@mafintosh)", "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/sparse-bitfield/issues"}, "homepage": "https://github.com/mafintosh/sparse-bitfield"}