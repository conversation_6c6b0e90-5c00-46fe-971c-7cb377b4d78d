'use client'

import { Canvas, useFrame } from '@react-three/fiber'
import { OrbitControls, Environment, ContactShadows, Text, Box, Sphere, Cylinder, Float, Html, useGLTF, Stars } from '@react-three/drei'
import { Physics, useBox, useSphere } from '@react-three/cannon'
import { Suspense, useRef, useState, useEffect } from 'react'
import { useGame } from '@/contexts/GameContext'
import { useLanguage } from '@/contexts/LanguageContext'
import * as THREE from 'three'

// Animated Fire Effect
function FireEffect({ position }: { position: [number, number, number] }) {
  const fireRef = useRef<THREE.Mesh>(null)

  useFrame((state) => {
    if (fireRef.current) {
      fireRef.current.scale.y = 1 + Math.sin(state.clock.elapsedTime * 8) * 0.2
      fireRef.current.scale.x = 1 + Math.sin(state.clock.elapsedTime * 6) * 0.1
    }
  })

  return (
    <Sphere ref={fireRef} args={[0.3]} position={position}>
      <meshStandardMaterial color="#ff4500" emissive="#ff4500" emissiveIntensity={0.8} />
    </Sphere>
  )
}

// 3D Equipment Components
function Oven({ position, onClick, isActive }: { position: [number, number, number], onClick: () => void, isActive: boolean }) {
  const [ref] = useBox(() => ({ position, mass: 0 }))
  const [hovered, setHovered] = useState(false)

  return (
    <Float speed={hovered ? 2 : 0.5} rotationIntensity={hovered ? 0.2 : 0} floatIntensity={hovered ? 0.2 : 0}>
      <group
        ref={ref}
        onClick={onClick}
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
      >
        {/* Oven Base */}
        <Box args={[2, 1.5, 1.5]} position={[0, 0.75, 0]}>
          <meshStandardMaterial
            color={isActive ? "#ff6b35" : hovered ? "#A0522D" : "#8B4513"}
            emissive={isActive ? "#ff2200" : "#000000"}
            emissiveIntensity={isActive ? 0.2 : 0}
          />
        </Box>
        {/* Oven Door */}
        <Box args={[1.8, 1.2, 0.1]} position={[0, 0.75, 0.8]}>
          <meshStandardMaterial color="#2C1810" transparent opacity={0.8} />
        </Box>
        {/* Oven Handle */}
        <Cylinder args={[0.05, 0.05, 0.8]} position={[0.7, 0.75, 0.85]} rotation={[0, 0, Math.PI / 2]}>
          <meshStandardMaterial color="#C0C0C0" metalness={0.8} roughness={0.2} />
        </Cylinder>
        {/* Fire Effect when active */}
        {isActive && <FireEffect position={[0, 1, 0]} />}
        {/* Equipment Label */}
        <Text
          position={[0, 2.2, 0]}
          fontSize={0.3}
          color={hovered ? "#FF6B35" : "#8B4513"}
          anchorX="center"
          anchorY="middle"
        >
          🔥 Oven
        </Text>
        {/* Hover indicator */}
        {hovered && (
          <Html position={[0, 2.8, 0]} center>
            <div className="bg-black/80 text-white px-2 py-1 rounded text-sm">
              Click to use
            </div>
          </Html>
        )}
      </group>
    </Float>
  )
}

// Rotating Mixer Arm
function MixerArm({ isActive }: { isActive: boolean }) {
  const armRef = useRef<THREE.Group>(null)

  useFrame((state) => {
    if (armRef.current && isActive) {
      armRef.current.rotation.y = state.clock.elapsedTime * 4
    }
  })

  return (
    <group ref={armRef}>
      <Cylinder args={[0.1, 0.1, 1]} position={[0, 1.8, 0]}>
        <meshStandardMaterial color="#C0C0C0" metalness={0.8} roughness={0.2} />
      </Cylinder>
      <Cylinder args={[0.05, 0.05, 0.3]} position={[0.2, 1.3, 0]}>
        <meshStandardMaterial color="#C0C0C0" metalness={0.8} roughness={0.2} />
      </Cylinder>
    </group>
  )
}

function Mixer({ position, onClick, isActive }: { position: [number, number, number], onClick: () => void, isActive: boolean }) {
  const [ref] = useBox(() => ({ position, mass: 0 }))
  const [hovered, setHovered] = useState(false)

  return (
    <Float speed={hovered ? 2 : 0.5} rotationIntensity={hovered ? 0.1 : 0} floatIntensity={hovered ? 0.1 : 0}>
      <group
        ref={ref}
        onClick={onClick}
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
      >
        {/* Mixer Base */}
        <Box args={[1.5, 1, 1.5]} position={[0, 0.5, 0]}>
          <meshStandardMaterial color={hovered ? "#DDA0DD" : "#E6E6FA"} />
        </Box>
        {/* Mixer Bowl */}
        <Sphere args={[0.6]} position={[0, 1.2, 0]}>
          <meshStandardMaterial color="#F5F5DC" metalness={0.1} roughness={0.8} />
        </Sphere>
        {/* Mixer Arm */}
        <MixerArm isActive={isActive} />
        {/* Mixing particles when active */}
        {isActive && (
          <>
            <Sphere args={[0.05]} position={[0.2, 1.2, 0.1]}>
              <meshStandardMaterial color="#FFD700" emissive="#FFD700" emissiveIntensity={0.5} />
            </Sphere>
            <Sphere args={[0.05]} position={[-0.1, 1.2, 0.2]}>
              <meshStandardMaterial color="#FFA500" emissive="#FFA500" emissiveIntensity={0.5} />
            </Sphere>
          </>
        )}
        {/* Equipment Label */}
        <Text
          position={[0, 2.5, 0]}
          fontSize={0.3}
          color={hovered ? "#FF6B35" : "#8B4513"}
          anchorX="center"
          anchorY="middle"
        >
          🥄 Mixer
        </Text>
        {/* Hover indicator */}
        {hovered && (
          <Html position={[0, 3, 0]} center>
            <div className="bg-black/80 text-white px-2 py-1 rounded text-sm">
              Click to use
            </div>
          </Html>
        )}
      </group>
    </Float>
  )
}

function WorkCounter({ position, onClick }: { position: [number, number, number], onClick: () => void }) {
  const [ref] = useBox(() => ({ position, mass: 0 }))
  const [hovered, setHovered] = useState(false)

  return (
    <Float speed={hovered ? 1 : 0.3} rotationIntensity={0} floatIntensity={hovered ? 0.05 : 0}>
      <group
        ref={ref}
        onClick={onClick}
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
      >
        {/* Counter Surface */}
        <Box args={[3, 0.2, 1.5]} position={[0, 1, 0]}>
          <meshStandardMaterial
            color={hovered ? "#DEB887" : "#D2B48C"}
            roughness={0.8}
            metalness={0.1}
          />
        </Box>
        {/* Counter Base */}
        <Box args={[2.8, 0.8, 1.3]} position={[0, 0.4, 0]}>
          <meshStandardMaterial color={hovered ? "#A0522D" : "#8B4513"} />
        </Box>
        {/* Counter Tools */}
        <Cylinder args={[0.02, 0.02, 0.3]} position={[1, 1.15, 0.3]} rotation={[Math.PI / 4, 0, 0]}>
          <meshStandardMaterial color="#C0C0C0" metalness={0.9} roughness={0.1} />
        </Cylinder>
        <Cylinder args={[0.02, 0.02, 0.25]} position={[0.7, 1.15, 0.2]} rotation={[Math.PI / 6, 0, Math.PI / 4]}>
          <meshStandardMaterial color="#8B4513" />
        </Cylinder>
        {/* Equipment Label */}
        <Text
          position={[0, 1.8, 0]}
          fontSize={0.3}
          color={hovered ? "#FF6B35" : "#8B4513"}
          anchorX="center"
          anchorY="middle"
        >
          🏪 Work Counter
        </Text>
        {/* Hover indicator */}
        {hovered && (
          <Html position={[0, 2.2, 0]} center>
            <div className="bg-black/80 text-white px-2 py-1 rounded text-sm">
              Click to use
            </div>
          </Html>
        )}
      </group>
    </Float>
  )
}

// Floating Particles
function FloatingParticles() {
  const particlesRef = useRef<THREE.Group>(null)

  useFrame((state) => {
    if (particlesRef.current) {
      particlesRef.current.rotation.y = state.clock.elapsedTime * 0.1
    }
  })

  return (
    <group ref={particlesRef}>
      {Array.from({ length: 20 }).map((_, i) => (
        <Float key={i} speed={1 + Math.random()} rotationIntensity={0.5} floatIntensity={0.5}>
          <Sphere
            args={[0.02]}
            position={[
              (Math.random() - 0.5) * 15,
              2 + Math.random() * 2,
              (Math.random() - 0.5) * 15
            ]}
          >
            <meshStandardMaterial
              color="#FFD700"
              emissive="#FFD700"
              emissiveIntensity={0.3}
              transparent
              opacity={0.6}
            />
          </Sphere>
        </Float>
      ))}
    </group>
  )
}

// 3D Bakery Environment
function BakeryEnvironment() {
  return (
    <>
      {/* Floor with texture pattern */}
      <Box args={[20, 0.1, 20]} position={[0, -0.05, 0]}>
        <meshStandardMaterial
          color="#F5DEB3"
          roughness={0.8}
          metalness={0.1}
        />
      </Box>

      {/* Floor tiles pattern */}
      {Array.from({ length: 10 }).map((_, i) =>
        Array.from({ length: 10 }).map((_, j) => (
          <Box
            key={`${i}-${j}`}
            args={[1.8, 0.05, 1.8]}
            position={[-9 + i * 2, 0, -9 + j * 2]}
          >
            <meshStandardMaterial
              color={(i + j) % 2 === 0 ? "#F5DEB3" : "#DEB887"}
              roughness={0.9}
            />
          </Box>
        ))
      )}

      {/* Walls */}
      <Box args={[20, 4, 0.2]} position={[0, 2, -10]}>
        <meshStandardMaterial color="#FFEFD5" roughness={0.7} />
      </Box>
      <Box args={[0.2, 4, 20]} position={[-10, 2, 0]}>
        <meshStandardMaterial color="#FFEFD5" roughness={0.7} />
      </Box>
      <Box args={[0.2, 4, 20]} position={[10, 2, 0]}>
        <meshStandardMaterial color="#FFEFD5" roughness={0.7} />
      </Box>

      {/* Ceiling */}
      <Box args={[20, 0.2, 20]} position={[0, 4, 0]}>
        <meshStandardMaterial color="#F0F8FF" roughness={0.5} />
      </Box>

      {/* Windows with frames */}
      <Box args={[4.2, 2.2, 0.15]} position={[-5, 2.5, -9.95]}>
        <meshStandardMaterial color="#8B4513" />
      </Box>
      <Box args={[4, 2, 0.1]} position={[-5, 2.5, -9.9]}>
        <meshStandardMaterial
          color="#87CEEB"
          transparent
          opacity={0.7}
          roughness={0.1}
          metalness={0.1}
        />
      </Box>
      <Box args={[4.2, 2.2, 0.15]} position={[5, 2.5, -9.95]}>
        <meshStandardMaterial color="#8B4513" />
      </Box>
      <Box args={[4, 2, 0.1]} position={[5, 2.5, -9.9]}>
        <meshStandardMaterial
          color="#87CEEB"
          transparent
          opacity={0.7}
          roughness={0.1}
          metalness={0.1}
        />
      </Box>

      {/* Ceiling lights */}
      <Cylinder args={[0.3, 0.3, 0.1]} position={[-3, 3.9, -3]}>
        <meshStandardMaterial color="#FFFACD" emissive="#FFFACD" emissiveIntensity={0.5} />
      </Cylinder>
      <Cylinder args={[0.3, 0.3, 0.1]} position={[3, 3.9, -3]}>
        <meshStandardMaterial color="#FFFACD" emissive="#FFFACD" emissiveIntensity={0.5} />
      </Cylinder>
      <Cylinder args={[0.3, 0.3, 0.1]} position={[0, 3.9, 3]}>
        <meshStandardMaterial color="#FFFACD" emissive="#FFFACD" emissiveIntensity={0.5} />
      </Cylinder>

      {/* Floating particles for atmosphere */}
      <FloatingParticles />
    </>
  )
}

// Ingredient Display
function IngredientDisplay({ position, ingredient }: {
  position: [number, number, number],
  ingredient: { name: string, quantity: number, icon: string }
}) {
  return (
    <group position={position}>
      {/* Ingredient Container */}
      <Cylinder args={[0.3, 0.3, 0.6]} position={[0, 0.3, 0]}>
        <meshStandardMaterial color="#F5F5DC" />
      </Cylinder>
      {/* Ingredient Label */}
      <Text
        position={[0, 0.8, 0]}
        fontSize={0.2}
        color="#8B4513"
        anchorX="center"
        anchorY="middle"
      >
        {ingredient.icon} {ingredient.quantity}
      </Text>
    </group>
  )
}

// Main 3D Bakery Component
export function Bakery3D() {
  const { t } = useLanguage()
  const { equipment, inventory, player } = useGame()
  const [selectedEquipment, setSelectedEquipment] = useState<string | null>(null)

  const handleEquipmentClick = (equipmentId: string) => {
    setSelectedEquipment(equipmentId)
    // Add equipment interaction logic here
  }

  return (
    <div className="w-full h-[600px] bg-gradient-to-b from-blue-200 to-blue-100 rounded-lg overflow-hidden relative">
      <Canvas
        camera={{ position: [8, 6, 8], fov: 60 }}
        shadows
        dpr={[1, 2]}
        gl={{ antialias: true, alpha: false }}
      >
        <Suspense fallback={null}>
          <Physics gravity={[0, -9.8, 0]}>
            {/* Enhanced Lighting */}
            <ambientLight intensity={0.3} color="#FFF8DC" />
            <directionalLight
              position={[10, 10, 5]}
              intensity={1.2}
              castShadow
              shadow-mapSize-width={2048}
              shadow-mapSize-height={2048}
              shadow-camera-far={50}
              shadow-camera-left={-20}
              shadow-camera-right={20}
              shadow-camera-top={20}
              shadow-camera-bottom={-20}
              color="#FFFACD"
            />
            <pointLight position={[-3, 4, -3]} intensity={0.8} color="#FFE4B5" distance={10} />
            <pointLight position={[3, 4, -3]} intensity={0.8} color="#FFE4B5" distance={10} />
            <pointLight position={[0, 4, 3]} intensity={0.8} color="#FFE4B5" distance={10} />
            <spotLight
              position={[0, 8, 0]}
              angle={Math.PI / 4}
              penumbra={0.5}
              intensity={0.5}
              castShadow
              color="#FFFACD"
            />

            {/* Environment */}
            <BakeryEnvironment />

            {/* Equipment */}
            <Oven
              position={[-6, 0, -6]}
              onClick={() => handleEquipmentClick('oven')}
              isActive={equipment.find(e => e.name === 'Oven')?.isActive || false}
            />
            <Mixer
              position={[-2, 0, -6]}
              onClick={() => handleEquipmentClick('mixer')}
              isActive={equipment.find(e => e.name === 'Mixer')?.isActive || false}
            />
            <WorkCounter
              position={[2, 0, -6]}
              onClick={() => handleEquipmentClick('counter')}
            />

            {/* Inventory Display */}
            {inventory.slice(0, 6).map((ingredient, index) => (
              <IngredientDisplay
                key={ingredient.name}
                position={[
                  -7 + (index % 3) * 2,
                  0,
                  4 + Math.floor(index / 3) * 2
                ]}
                ingredient={ingredient}
              />
            ))}

            {/* Player Stats Display */}
            <Text
              position={[0, 3.5, 8]}
              fontSize={0.4}
              color="#8B4513"
              anchorX="center"
              anchorY="middle"
            >
              {t('ui.level', { level: player.level.toString() })} | {t('ui.money', { amount: player.money.toString() })}
            </Text>

            {/* Ground Shadows */}
            <ContactShadows
              position={[0, 0, 0]}
              opacity={0.4}
              scale={20}
              blur={1}
              far={10}
              resolution={256}
              color="#000000"
            />

            {/* Environment Lighting */}
            <Environment preset="apartment" background={false} />
            <Stars radius={100} depth={50} count={1000} factor={4} saturation={0} fade speed={1} />

            {/* Camera Controls */}
            <OrbitControls
              enablePan={true}
              enableZoom={true}
              enableRotate={true}
              minDistance={5}
              maxDistance={25}
              minPolarAngle={Math.PI / 6}
              maxPolarAngle={Math.PI / 2.2}
              enableDamping={true}
              dampingFactor={0.05}
              rotateSpeed={0.5}
              zoomSpeed={0.8}
              panSpeed={0.8}
              target={[0, 1, 0]}
            />
          </Physics>
        </Suspense>
      </Canvas>

      {/* 3D UI Overlay */}
      <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg p-4 shadow-lg">
        <h3 className="text-lg font-bold text-orange-800 mb-2">
          🏪 {t('game.view.3d', '3D Bakery')}
        </h3>
        <p className="text-sm text-gray-600">
          {t('game.3d.instructions', 'Click equipment to interact • Drag to rotate • Scroll to zoom')}
        </p>
        {selectedEquipment && (
          <div className="mt-2 p-2 bg-orange-100 rounded">
            <p className="text-sm font-medium text-orange-800">
              {t('game.selected', 'Selected')}: {selectedEquipment}
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
