{"game": {"title": "Bake It Out", "subtitle": "Master the art of bakery management in this engaging multiplayer game. Complete orders, unlock recipes, automate your processes, and compete with friends!", "play": "🎮 Start Playing", "singlePlayer": "🎮 Single Player", "singlePlayerDesc": "Play solo and master your bakery skills", "multiplayer": "👥 Multiplayer", "multiplayerDesc": "Play with friends in cooperative or competitive modes", "english": "🇺🇸 English", "czech": "🇨🇿 <PERSON><PERSON><PERSON><PERSON>", "home": "🏠 Home", "close": "✕ Close", "continue": "🚀 Continue Playing"}, "menu": {"singlePlayer": "Single Player", "multiplayer": "Multiplayer", "settings": "Settings", "credits": "Credits", "exit": "Exit", "newGame": "New Game", "continueGame": "Continue Game", "loadGame": "Load Game", "selectLanguage": "Select Language", "about": "About", "help": "Help", "quit": "Quit"}, "credits": {"title": "About Bake It Out", "subtitle": "Game Information & Credits", "description": "A multiplayer bakery management game with real-time collaboration and localization support.", "version": "Version", "release": "Release Date", "releaseDate": "January 2025", "platform": "Platform", "platforms": "Windows, macOS, Linux", "contact": "Contact", "facebook": "Facebook", "close": "Close", "features": "Features", "multiplayer": "Multiplayer Support", "multiplayerDesc": "Real-time collaboration with friends in cooperative or competitive modes.", "localization": "Localization", "localizationDesc": "Full support for English and Czech languages with easy switching.", "progression": "Progression System", "progressionDesc": "Level up your bakery, unlock recipes, and master automation.", "automation": "Automation", "automationDesc": "Advanced automation systems to optimize your bakery operations.", "technology": "Technology Stack", "team": "Development Team", "developedBy": "Developed by", "teamDesc": "Created with passion by the Bake It Out development team.", "thanks": "Special Thanks", "thanksPlayers": "All our amazing players and beta testers", "thanksTranslators": "Community translators for localization support", "thanksOpenSource": "Open source community for incredible tools and libraries", "thanksBakers": "Real bakers who inspired this game", "website": "Website", "support": "Support", "github": "GitHub", "discord": "Discord"}, "gameViews": {"traditional": "Traditional View", "layout": "<PERSON><PERSON> Layout", "dining": "Dining Room", "3d": "3D Kitchen", "3dDining": "3D Dining Room", "customers": "Customer Manager", "switchView": "Switch View"}, "bakery": {"layout": {"title": "<PERSON><PERSON> Layout", "kitchen": "Kitchen", "dining": "Dining Area", "counter": "Service Counter", "bakingArea": "Baking Area", "prepArea": "Prep Area", "automationArea": "Automation", "kitchenStats": "Kitchen Stats", "activeEquipment": "Active Equipment", "automatedEquipment": "Automated Equipment", "efficiency": "Efficiency", "diningStats": "Dining Stats", "currentCustomers": "Current Customers", "waitingCustomers": "Waiting", "eatingCustomers": "Eating", "avgSatisfaction": "Avg Satisfaction", "serviceCounter": "Service Counter", "displayCase": "Display Case", "orderQueue": "Order Queue", "lastUpdated": "Last updated"}}, "dining": {"room": {"title": "Dining Room", "subtitle": "Watch your customers enjoy their meals", "occupiedTables": "Occupied Tables", "ambientSounds": "Toggle ambient sounds", "serviceCounter": "Service", "customerStatus": "Customer Status", "enjoying": "Enjoying", "noCustomers": "No customers dining currently", "waitingForCustomers": "Complete orders to see customers dining", "ambientPlaying": "Ambient sounds playing"}}, "customers": {"manager": {"title": "Customer Manager", "subtitle": "Monitor and serve your customers", "currentList": "Current Customers", "table": "Table", "patience": "Patience", "noCustomers": "No customers currently", "waitingForOrders": "Waiting for new orders...", "orderDetails": "Order Details", "total": "Total", "info": "Customer Info", "mood": "<PERSON><PERSON>", "status": "Status", "preferences": "Preferences", "selectCustomer": "Select a customer", "selectToViewDetails": "Select a customer to view details", "serveOrder": "Serve Order"}}, "features": {"manage": {"title": "Manage Your Bakery", "description": "Take orders, bake delicious goods, and serve happy customers"}, "levelup": {"title": "Level Up & Automate", "description": "Unlock new recipes, buy equipment, and automate your processes"}, "multiplayer": {"title": "Play Together", "description": "Cooperative and competitive multiplayer modes with friends"}}, "ui": {"level": "Level {{level}}", "money": "${{amount}}", "experience": "XP: {{current}}/{{max}}", "skillPoints": "SP: {{points}}", "achievements": "🏆 Achievements", "skills": "🌟 Skills", "automation": "🤖 Automation", "placeholder": "Enter text...", "search": "Search", "filter": "Filter", "sort": "Sort", "ascending": "Ascending", "descending": "Descending", "selectAll": "Select All", "deselectAll": "Deselect All", "noResults": "No results found", "noData": "No data available", "loading": "Loading...", "saving": "Saving...", "saved": "Saved!", "failed": "Failed", "retry": "Retry", "back": "Back", "forward": "Forward", "home": "Home", "menu": "<PERSON><PERSON>", "options": "Options", "preferences": "Preferences"}, "kitchen": {"title": "🏪 Kitchen", "clickToUse": "Click to use", "making": "Making: {{recipe}}", "timeRemaining": "Time: {{time}}"}, "inventory": {"title": "📦 Inventory", "quantity": "Qty: {{qty}}", "cost": "${{cost}} each", "pricePerUnit": "per unit"}, "orders": {"title": "📋 Orders", "newOrder": "+ New Order", "accept": "Accept", "decline": "Decline", "complete": "Complete", "inProgress": "In Progress", "timeLimit": "Time: {{time}}", "reward": "${{amount}}", "customer": "Customer: {{name}}", "accepted": "Order Accepted"}, "actions": {"title": "⚡ Quick Actions", "buyIngredients": "🛒 Buy Ingredients", "viewRecipes": "📖 View Recipes", "equipmentShop": "🔧 Equipment Shop"}, "modals": {"recipes": {"title": "📖 Recipe Book", "all": "All", "cookies": "Cookies", "cakes": "Cakes", "bread": "Bread", "pastries": "Pastries", "ingredients": "Ingredients:", "difficulty": "Difficulty:", "time": "Time:", "canCraft": "✅ Can Craft", "unlockLevel": "Unlocked at Level {{level}}", "noRecipes": "No recipes available in this category.", "levelUpToUnlock": "Level up to unlock more recipes!"}, "shop": {"title": "🛒 Ingredient Shop", "currentStock": "Current stock: {{quantity}}", "buy": "Buy", "tooExpensive": "Too Expensive", "tips": {"title": "💡 Shopping Tips", "bulk": "• Buy ingredients in bulk to save time", "stock": "• Keep an eye on your stock levels", "rare": "• Some recipes require rare ingredients", "prices": "• Prices may vary based on availability"}}, "baking": {"title": "🔥 {{equipment}} - Select Recipe", "selectRecipe": "Select Recipe", "noRecipes": "No recipes available", "noIngredients": "You don't have enough ingredients to craft any recipes.", "buyIngredients": "Buy Ingredients", "startBaking": "🔥 Start Baking", "instructions": "📋 Baking Instructions for {{recipe}}", "expectedReward": "Expected reward: ${{amount}}", "makesSure": "Make sure you have all ingredients before starting!", "inProgress": "Baking in progress...", "completed": "Baking completed!", "cancelled": "Baking cancelled", "timeRemaining": "Time remaining: {{time}}", "clickToCollect": "Click to collect"}, "achievements": {"title": "🏆 Achievements", "completed": "{{completed}} of {{total}} achievements completed", "overallProgress": "Overall Progress", "progress": "Progress", "reward": "Reward:", "noAchievements": "No achievements in this category."}, "skills": {"title": "🌟 Skill Tree", "availablePoints": "Available Skill Points: {{points}}", "efficiency": "Efficiency", "automation": "Automation", "quality": "Quality", "business": "Business", "effects": "Effects:", "requires": "Requires: {{requirements}}", "requiresLevel": "Requires Level {{level}}", "maxed": "✅ Maxed", "upgrade": "⬆️ Upgrade ({{cost}} SP)", "locked": "🔒 Locked", "noSkills": "No skills in this category.", "tips": {"title": "💡 Skill Tips", "earnPoints": "• Earn skill points by leveling up (1 point every 2 levels)", "prerequisites": "• Some skills require other skills to be unlocked first", "playstyle": "• Focus on skills that match your playstyle", "reset": "• You can reset your skills for a fee"}}, "automation": {"title": "🤖 Automation Manager", "subtitle": "Manage your automated bakery systems", "currentSystems": "Current Systems", "availableSystems": "Available Systems", "active": "Active", "inactive": "Inactive", "efficiency": "Efficiency", "maintenance": "Maintenance", "upgrade": "Upgrade", "repair": "Repair", "sell": "<PERSON>ll", "buy": "Buy", "noSystems": "No automation systems available", "unlockMore": "Unlock more systems by leveling up", "tips": {"title": "💡 Automation Tips", "balance": "• Balance automation with manual control", "maintenance": "• Keep systems maintained for peak efficiency", "upgrade": "• Upgrade systems to increase output", "strategy": "• Plan your automation strategy carefully"}}, "equipment": {"title": "🔧 Equipment Shop", "subtitle": "Buy new equipment to expand your bakery", "categories": {"all": "All", "ovens": "<PERSON><PERSON><PERSON>", "mixers": "Mixers", "decorating": "Decorating", "storage": "Storage", "automation": "Automation"}, "owned": "Owned: {{count}}", "buy": "Buy", "sell": "<PERSON>ll", "upgrade": "Upgrade", "repair": "Repair", "tooExpensive": "Too Expensive", "noEquipment": "No equipment available in this category.", "tips": {"title": "💡 Equipment Tips", "upgrade": "• Upgrade equipment to increase efficiency", "balance": "• Balance different types of equipment", "maintenance": "• Keep equipment maintained for best performance"}}}, "multiplayer": {"lobby": {"title": "👥 Multiplayer Lobby", "subtitle": "Join or create a multiplayer game", "createRoom": "Create Room", "joinRoom": "Join Room", "roomCode": "Room Code", "enterCode": "Enter room code", "players": "Players", "startGame": "Start Game", "leaveRoom": "Leave Room", "waitingForPlayers": "Waiting for players...", "roomFull": "Room is full", "invalidCode": "Invalid room code", "playerJoined": "{{name}} joined the room", "playerLeft": "{{name}} left the room", "host": "Host", "ready": "Ready", "notReady": "Not Ready", "toggleReady": "Toggle Ready"}, "game": {"title": "Multiplayer Game", "players": "Players", "chat": "Cha<PERSON>", "sendMessage": "Send Message", "typeMessage": "Type a message...", "playerJoined": "{{name}} joined the game", "playerLeft": "{{name}} left the game", "cooperative": "Cooperative Mode", "competitive": "Competitive Mode", "sharedResources": "Shared Resources", "individualResources": "Individual Resources", "teamWork": "Work together to build the best bakery!", "compete": "Compete to be the best baker!"}}}