"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[135],{9745:(e,t,r)=>{r.d(t,{BN:()=>iZ,GG:()=>iJ,H9:()=>is,My:()=>i$,O5:()=>i2,P:()=>iM,_M:()=>iP,aU:()=>iu,kd:()=>i0,rJ:()=>ir,x7:()=>iY});var n,s,i,a,o=r(1055),l=r(2881),u=r(6702),h=r(1280),c=r(2107),d=r(927);r(9509);var f=r(4134).hp;let m="@firebase/firestore",g="4.9.0";class p{constructor(e){this.uid=e}isAuthenticated(){return null!=this.uid}toKey(){return this.isAuthenticated()?"uid:"+this.uid:"anonymous-user"}isEqual(e){return e.uid===this.uid}}p.UNAUTHENTICATED=new p(null),p.GOOGLE_CREDENTIALS=new p("google-credentials-uid"),p.FIRST_PARTY=new p("first-party-uid"),p.MOCK_USER=new p("mock-user");let y="12.0.0",w=new u.Vy("@firebase/firestore");function v(){return w.logLevel}function _(e,...t){if(w.logLevel<=u.$b.DEBUG){let r=t.map(I);w.debug(`Firestore (${y}): ${e}`,...r)}}function E(e,...t){if(w.logLevel<=u.$b.ERROR){let r=t.map(I);w.error(`Firestore (${y}): ${e}`,...r)}}function T(e,...t){if(w.logLevel<=u.$b.WARN){let r=t.map(I);w.warn(`Firestore (${y}): ${e}`,...r)}}function I(e){if("string"==typeof e)return e;try{return JSON.stringify(e)}catch(t){return e}}function S(e,t,r){let n="Unexpected state";"string"==typeof t?n=t:r=t,C(e,n,r)}function C(e,t,r){let n=`FIRESTORE (${y}) INTERNAL ASSERTION FAILED: ${t} (ID: ${e.toString(16)})`;if(void 0!==r)try{n+=" CONTEXT: "+JSON.stringify(r)}catch(e){n+=" CONTEXT: "+r}throw E(n),Error(n)}function N(e,t,r,n){let s="Unexpected state";"string"==typeof r?s=r:n=r,e||C(t,s,n)}let A={OK:"ok",CANCELLED:"cancelled",UNKNOWN:"unknown",INVALID_ARGUMENT:"invalid-argument",DEADLINE_EXCEEDED:"deadline-exceeded",NOT_FOUND:"not-found",ALREADY_EXISTS:"already-exists",PERMISSION_DENIED:"permission-denied",UNAUTHENTICATED:"unauthenticated",RESOURCE_EXHAUSTED:"resource-exhausted",FAILED_PRECONDITION:"failed-precondition",ABORTED:"aborted",OUT_OF_RANGE:"out-of-range",UNIMPLEMENTED:"unimplemented",INTERNAL:"internal",UNAVAILABLE:"unavailable",DATA_LOSS:"data-loss"};class b extends h.g{constructor(e,t){super(e,t),this.code=e,this.message=t,this.toString=()=>`${this.name}: [code=${this.code}]: ${this.message}`}}class D{constructor(){this.promise=new Promise((e,t)=>{this.resolve=e,this.reject=t})}}class k{constructor(e,t){this.user=t,this.type="OAuth",this.headers=new Map,this.headers.set("Authorization",`Bearer ${e}`)}}class x{getToken(){return Promise.resolve(null)}invalidateToken(){}start(e,t){e.enqueueRetryable(()=>t(p.UNAUTHENTICATED))}shutdown(){}}class R{constructor(e){this.token=e,this.changeListener=null}getToken(){return Promise.resolve(this.token)}invalidateToken(){}start(e,t){this.changeListener=t,e.enqueueRetryable(()=>t(this.token.user))}shutdown(){this.changeListener=null}}class V{constructor(e){this.t=e,this.currentUser=p.UNAUTHENTICATED,this.i=0,this.forceRefresh=!1,this.auth=null}start(e,t){N(void 0===this.o,42304);let r=this.i,n=e=>this.i!==r?(r=this.i,t(e)):Promise.resolve(),s=new D;this.o=()=>{this.i++,this.currentUser=this.u(),s.resolve(),s=new D,e.enqueueRetryable(()=>n(this.currentUser))};let i=()=>{let t=s;e.enqueueRetryable(async()=>{await t.promise,await n(this.currentUser)})},a=e=>{_("FirebaseAuthCredentialsProvider","Auth detected"),this.auth=e,this.o&&(this.auth.addAuthTokenListener(this.o),i())};this.t.onInit(e=>a(e)),setTimeout(()=>{if(!this.auth){let e=this.t.getImmediate({optional:!0});e?a(e):(_("FirebaseAuthCredentialsProvider","Auth not yet detected"),s.resolve(),s=new D)}},0),i()}getToken(){let e=this.i,t=this.forceRefresh;return this.forceRefresh=!1,this.auth?this.auth.getToken(t).then(t=>this.i!==e?(_("FirebaseAuthCredentialsProvider","getToken aborted due to token change."),this.getToken()):t?(N("string"==typeof t.accessToken,31837,{l:t}),new k(t.accessToken,this.currentUser)):null):Promise.resolve(null)}invalidateToken(){this.forceRefresh=!0}shutdown(){this.auth&&this.o&&this.auth.removeAuthTokenListener(this.o),this.o=void 0}u(){let e=this.auth&&this.auth.getUid();return N(null===e||"string"==typeof e,2055,{h:e}),new p(e)}}class L{constructor(e,t,r){this.P=e,this.T=t,this.I=r,this.type="FirstParty",this.user=p.FIRST_PARTY,this.A=new Map}R(){return this.I?this.I():null}get headers(){this.A.set("X-Goog-AuthUser",this.P);let e=this.R();return e&&this.A.set("Authorization",e),this.T&&this.A.set("X-Goog-Iam-Authorization-Token",this.T),this.A}}class O{constructor(e,t,r){this.P=e,this.T=t,this.I=r}getToken(){return Promise.resolve(new L(this.P,this.T,this.I))}start(e,t){e.enqueueRetryable(()=>t(p.FIRST_PARTY))}shutdown(){}invalidateToken(){}}class M{constructor(e){this.value=e,this.type="AppCheck",this.headers=new Map,e&&e.length>0&&this.headers.set("x-firebase-appcheck",this.value)}}class F{constructor(e,t){this.V=t,this.forceRefresh=!1,this.appCheck=null,this.m=null,this.p=null,(0,o.xZ)(e)&&e.settings.appCheckToken&&(this.p=e.settings.appCheckToken)}start(e,t){N(void 0===this.o,3512),this.o=r=>{e.enqueueRetryable(()=>(e=>{null!=e.error&&_("FirebaseAppCheckTokenProvider",`Error getting App Check token; using placeholder token instead. Error: ${e.error.message}`);let r=e.token!==this.m;return this.m=e.token,_("FirebaseAppCheckTokenProvider",`Received ${r?"new":"existing"} token.`),r?t(e.token):Promise.resolve()})(r))};let r=e=>{_("FirebaseAppCheckTokenProvider","AppCheck detected"),this.appCheck=e,this.o&&this.appCheck.addTokenListener(this.o)};this.V.onInit(e=>r(e)),setTimeout(()=>{if(!this.appCheck){let e=this.V.getImmediate({optional:!0});e?r(e):_("FirebaseAppCheckTokenProvider","AppCheck not yet detected")}},0)}getToken(){if(this.p)return Promise.resolve(new M(this.p));let e=this.forceRefresh;return this.forceRefresh=!1,this.appCheck?this.appCheck.getToken(e).then(e=>e?(N("string"==typeof e.token,44558,{tokenResult:e}),this.m=e.token,new M(e.token)):null):Promise.resolve(null)}invalidateToken(){this.forceRefresh=!0}shutdown(){this.appCheck&&this.o&&this.appCheck.removeTokenListener(this.o),this.o=void 0}}class P{static newId(){let e=62*Math.floor(256/62),t="";for(;t.length<20;){let r=function(e){let t="undefined"!=typeof self&&(self.crypto||self.msCrypto),r=new Uint8Array(40);if(t&&"function"==typeof t.getRandomValues)t.getRandomValues(r);else for(let e=0;e<40;e++)r[e]=Math.floor(256*Math.random());return r}(40);for(let n=0;n<r.length;++n)t.length<20&&r[n]<e&&(t+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".charAt(r[n]%62))}return t}}function U(e,t){return e<t?-1:+(e>t)}function q(e,t){let r=Math.min(e.length,t.length);for(let n=0;n<r;n++){let r=e.charAt(n),s=t.charAt(n);if(r!==s)return $(r)===$(s)?U(r,s):$(r)?1:-1}return U(e.length,t.length)}function $(e){let t=e.charCodeAt(0);return t>=55296&&t<=57343}function B(e,t,r){return e.length===t.length&&e.every((e,n)=>r(e,t[n]))}let z="__name__";class j{constructor(e,t,r){void 0===t?t=0:t>e.length&&S(637,{offset:t,range:e.length}),void 0===r?r=e.length-t:r>e.length-t&&S(1746,{length:r,range:e.length-t}),this.segments=e,this.offset=t,this.len=r}get length(){return this.len}isEqual(e){return 0===j.comparator(this,e)}child(e){let t=this.segments.slice(this.offset,this.limit());return e instanceof j?e.forEach(e=>{t.push(e)}):t.push(e),this.construct(t)}limit(){return this.offset+this.length}popFirst(e){return e=void 0===e?1:e,this.construct(this.segments,this.offset+e,this.length-e)}popLast(){return this.construct(this.segments,this.offset,this.length-1)}firstSegment(){return this.segments[this.offset]}lastSegment(){return this.get(this.length-1)}get(e){return this.segments[this.offset+e]}isEmpty(){return 0===this.length}isPrefixOf(e){if(e.length<this.length)return!1;for(let t=0;t<this.length;t++)if(this.get(t)!==e.get(t))return!1;return!0}isImmediateParentOf(e){if(this.length+1!==e.length)return!1;for(let t=0;t<this.length;t++)if(this.get(t)!==e.get(t))return!1;return!0}forEach(e){for(let t=this.offset,r=this.limit();t<r;t++)e(this.segments[t])}toArray(){return this.segments.slice(this.offset,this.limit())}static comparator(e,t){let r=Math.min(e.length,t.length);for(let n=0;n<r;n++){let r=j.compareSegments(e.get(n),t.get(n));if(0!==r)return r}return U(e.length,t.length)}static compareSegments(e,t){let r=j.isNumericId(e),n=j.isNumericId(t);return r&&!n?-1:!r&&n?1:r&&n?j.extractNumericId(e).compare(j.extractNumericId(t)):q(e,t)}static isNumericId(e){return e.startsWith("__id")&&e.endsWith("__")}static extractNumericId(e){return c.jz.fromString(e.substring(4,e.length-2))}}class G extends j{construct(e,t,r){return new G(e,t,r)}canonicalString(){return this.toArray().join("/")}toString(){return this.canonicalString()}toUriEncodedString(){return this.toArray().map(encodeURIComponent).join("/")}static fromString(...e){let t=[];for(let r of e){if(r.indexOf("//")>=0)throw new b(A.INVALID_ARGUMENT,`Invalid segment (${r}). Paths must not contain // in them.`);t.push(...r.split("/").filter(e=>e.length>0))}return new G(t)}static emptyPath(){return new G([])}}let K=/^[_a-zA-Z][_a-zA-Z0-9]*$/;class Q extends j{construct(e,t,r){return new Q(e,t,r)}static isValidIdentifier(e){return K.test(e)}canonicalString(){return this.toArray().map(e=>(e=e.replace(/\\/g,"\\\\").replace(/`/g,"\\`"),Q.isValidIdentifier(e)||(e="`"+e+"`"),e)).join(".")}toString(){return this.canonicalString()}isKeyField(){return 1===this.length&&this.get(0)===z}static keyField(){return new Q([z])}static fromServerFormat(e){let t=[],r="",n=0,s=()=>{if(0===r.length)throw new b(A.INVALID_ARGUMENT,`Invalid field path (${e}). Paths must not be empty, begin with '.', end with '.', or contain '..'`);t.push(r),r=""},i=!1;for(;n<e.length;){let t=e[n];if("\\"===t){if(n+1===e.length)throw new b(A.INVALID_ARGUMENT,"Path has trailing escape character: "+e);let t=e[n+1];if("\\"!==t&&"."!==t&&"`"!==t)throw new b(A.INVALID_ARGUMENT,"Path has invalid escape sequence: "+e);r+=t,n+=2}else"`"===t?i=!i:"."!==t||i?r+=t:s(),n++}if(s(),i)throw new b(A.INVALID_ARGUMENT,"Unterminated ` in path: "+e);return new Q(t)}static emptyPath(){return new Q([])}}class H{constructor(e){this.path=e}static fromPath(e){return new H(G.fromString(e))}static fromName(e){return new H(G.fromString(e).popFirst(5))}static empty(){return new H(G.emptyPath())}get collectionGroup(){return this.path.popLast().lastSegment()}hasCollectionId(e){return this.path.length>=2&&this.path.get(this.path.length-2)===e}getCollectionGroup(){return this.path.get(this.path.length-2)}getCollectionPath(){return this.path.popLast()}isEqual(e){return null!==e&&0===G.comparator(this.path,e.path)}toString(){return this.path.toString()}static comparator(e,t){return G.comparator(e.path,t.path)}static isDocumentKey(e){return e.length%2==0}static fromSegments(e){return new H(new G(e.slice()))}}function W(e,t,r){if(!r)throw new b(A.INVALID_ARGUMENT,`Function ${e}() cannot be called with an empty ${t}.`)}function Y(e){if(!H.isDocumentKey(e))throw new b(A.INVALID_ARGUMENT,`Invalid document reference. Document references must have an even number of segments, but ${e} has ${e.length}.`)}function X(e){if(H.isDocumentKey(e))throw new b(A.INVALID_ARGUMENT,`Invalid collection reference. Collection references must have an odd number of segments, but ${e} has ${e.length}.`)}function J(e){return"object"==typeof e&&null!==e&&(Object.getPrototypeOf(e)===Object.prototype||null===Object.getPrototypeOf(e))}function Z(e){if(void 0===e)return"undefined";if(null===e)return"null";if("string"==typeof e)return e.length>20&&(e=`${e.substring(0,20)}...`),JSON.stringify(e);if("number"==typeof e||"boolean"==typeof e)return""+e;if("object"==typeof e){if(e instanceof Array)return"an array";{var t;let r=(t=e).constructor?t.constructor.name:null;return r?`a custom ${r} object`:"an object"}}return"function"==typeof e?"a function":S(12329,{type:typeof e})}function ee(e,t){if("_delegate"in e&&(e=e._delegate),!(e instanceof t)){if(t.name===e.constructor.name)throw new b(A.INVALID_ARGUMENT,"Type does not match the expected instance. Did you pass a reference from a different Firestore SDK?");{let r=Z(e);throw new b(A.INVALID_ARGUMENT,`Expected type '${t.name}', but it was: ${r}`)}}return e}function et(e,t){let r={typeString:e};return t&&(r.value=t),r}function er(e,t){let r;if(!J(e))throw new b(A.INVALID_ARGUMENT,"JSON must be an object");for(let n in t)if(t[n]){let s=t[n].typeString,i="value"in t[n]?{value:t[n].value}:void 0;if(!(n in e)){r=`JSON missing required field: '${n}'`;break}let a=e[n];if(s&&typeof a!==s){r=`JSON field '${n}' must be a ${s}.`;break}if(void 0!==i&&a!==i.value){r=`Expected '${n}' field to equal '${i.value}'`;break}}if(r)throw new b(A.INVALID_ARGUMENT,r);return!0}class en{static now(){return en.fromMillis(Date.now())}static fromDate(e){return en.fromMillis(e.getTime())}static fromMillis(e){let t=Math.floor(e/1e3),r=Math.floor((e-1e3*t)*1e6);return new en(t,r)}constructor(e,t){if(this.seconds=e,this.nanoseconds=t,t<0||t>=1e9)throw new b(A.INVALID_ARGUMENT,"Timestamp nanoseconds out of range: "+t);if(e<-0xe7791f700||e>=0x3afff44180)throw new b(A.INVALID_ARGUMENT,"Timestamp seconds out of range: "+e)}toDate(){return new Date(this.toMillis())}toMillis(){return 1e3*this.seconds+this.nanoseconds/1e6}_compareTo(e){return this.seconds===e.seconds?U(this.nanoseconds,e.nanoseconds):U(this.seconds,e.seconds)}isEqual(e){return e.seconds===this.seconds&&e.nanoseconds===this.nanoseconds}toString(){return"Timestamp(seconds="+this.seconds+", nanoseconds="+this.nanoseconds+")"}toJSON(){return{type:en._jsonSchemaVersion,seconds:this.seconds,nanoseconds:this.nanoseconds}}static fromJSON(e){if(er(e,en._jsonSchema))return new en(e.seconds,e.nanoseconds)}valueOf(){return String(this.seconds- -0xe7791f700).padStart(12,"0")+"."+String(this.nanoseconds).padStart(9,"0")}}en._jsonSchemaVersion="firestore/timestamp/1.0",en._jsonSchema={type:et("string",en._jsonSchemaVersion),seconds:et("number"),nanoseconds:et("number")};class es{static fromTimestamp(e){return new es(e)}static min(){return new es(new en(0,0))}static max(){return new es(new en(0x3afff4417f,0x3b9ac9ff))}constructor(e){this.timestamp=e}compareTo(e){return this.timestamp._compareTo(e.timestamp)}isEqual(e){return this.timestamp.isEqual(e.timestamp)}toMicroseconds(){return 1e6*this.timestamp.seconds+this.timestamp.nanoseconds/1e3}toString(){return"SnapshotVersion("+this.timestamp.toString()+")"}toTimestamp(){return this.timestamp}}class ei{constructor(e,t,r,n){this.indexId=e,this.collectionGroup=t,this.fields=r,this.indexState=n}}ei.UNKNOWN_ID=-1;class ea{constructor(e,t,r){this.readTime=e,this.documentKey=t,this.largestBatchId=r}static min(){return new ea(es.min(),H.empty(),-1)}static max(){return new ea(es.max(),H.empty(),-1)}}class eo{constructor(){this.onCommittedListeners=[]}addOnCommittedListener(e){this.onCommittedListeners.push(e)}raiseOnCommittedEvent(){this.onCommittedListeners.forEach(e=>e())}}async function el(e){if(e.code!==A.FAILED_PRECONDITION||"The current tab is not in the required state to perform this operation. It might be necessary to refresh the browser tab."!==e.message)throw e;_("LocalStore","Unexpectedly lost primary lease")}class eu{constructor(e){this.nextCallback=null,this.catchCallback=null,this.result=void 0,this.error=void 0,this.isDone=!1,this.callbackAttached=!1,e(e=>{this.isDone=!0,this.result=e,this.nextCallback&&this.nextCallback(e)},e=>{this.isDone=!0,this.error=e,this.catchCallback&&this.catchCallback(e)})}catch(e){return this.next(void 0,e)}next(e,t){return this.callbackAttached&&S(59440),this.callbackAttached=!0,this.isDone?this.error?this.wrapFailure(t,this.error):this.wrapSuccess(e,this.result):new eu((r,n)=>{this.nextCallback=t=>{this.wrapSuccess(e,t).next(r,n)},this.catchCallback=e=>{this.wrapFailure(t,e).next(r,n)}})}toPromise(){return new Promise((e,t)=>{this.next(e,t)})}wrapUserFunction(e){try{let t=e();return t instanceof eu?t:eu.resolve(t)}catch(e){return eu.reject(e)}}wrapSuccess(e,t){return e?this.wrapUserFunction(()=>e(t)):eu.resolve(t)}wrapFailure(e,t){return e?this.wrapUserFunction(()=>e(t)):eu.reject(t)}static resolve(e){return new eu((t,r)=>{t(e)})}static reject(e){return new eu((t,r)=>{r(e)})}static waitFor(e){return new eu((t,r)=>{let n=0,s=0,i=!1;e.forEach(e=>{++n,e.next(()=>{++s,i&&s===n&&t()},e=>r(e))}),i=!0,s===n&&t()})}static or(e){let t=eu.resolve(!1);for(let r of e)t=t.next(e=>e?eu.resolve(e):r());return t}static forEach(e,t){let r=[];return e.forEach((e,n)=>{r.push(t.call(this,e,n))}),this.waitFor(r)}static mapArray(e,t){return new eu((r,n)=>{let s=e.length,i=Array(s),a=0;for(let o=0;o<s;o++){let l=o;t(e[l]).next(e=>{i[l]=e,++a===s&&r(i)},e=>n(e))}})}static doWhile(e,t){return new eu((r,n)=>{let s=()=>{!0===e()?t().next(()=>{s()},n):r()};s()})}}function eh(e){return"IndexedDbTransactionError"===e.name}class ec{constructor(e,t){this.previousValue=e,t&&(t.sequenceNumberHandler=e=>this.ae(e),this.ue=e=>t.writeSequenceNumber(e))}ae(e){return this.previousValue=Math.max(e,this.previousValue),this.previousValue}next(){let e=++this.previousValue;return this.ue&&this.ue(e),e}}ec.ce=-1;function ed(e){return 0===e&&1/e==-1/0}function ef(e){let t=0;for(let r in e)Object.prototype.hasOwnProperty.call(e,r)&&t++;return t}function em(e,t){for(let r in e)Object.prototype.hasOwnProperty.call(e,r)&&t(r,e[r])}function eg(e){for(let t in e)if(Object.prototype.hasOwnProperty.call(e,t))return!1;return!0}class ep{constructor(e,t){this.comparator=e,this.root=t||ew.EMPTY}insert(e,t){return new ep(this.comparator,this.root.insert(e,t,this.comparator).copy(null,null,ew.BLACK,null,null))}remove(e){return new ep(this.comparator,this.root.remove(e,this.comparator).copy(null,null,ew.BLACK,null,null))}get(e){let t=this.root;for(;!t.isEmpty();){let r=this.comparator(e,t.key);if(0===r)return t.value;r<0?t=t.left:r>0&&(t=t.right)}return null}indexOf(e){let t=0,r=this.root;for(;!r.isEmpty();){let n=this.comparator(e,r.key);if(0===n)return t+r.left.size;n<0?r=r.left:(t+=r.left.size+1,r=r.right)}return -1}isEmpty(){return this.root.isEmpty()}get size(){return this.root.size}minKey(){return this.root.minKey()}maxKey(){return this.root.maxKey()}inorderTraversal(e){return this.root.inorderTraversal(e)}forEach(e){this.inorderTraversal((t,r)=>(e(t,r),!1))}toString(){let e=[];return this.inorderTraversal((t,r)=>(e.push(`${t}:${r}`),!1)),`{${e.join(", ")}}`}reverseTraversal(e){return this.root.reverseTraversal(e)}getIterator(){return new ey(this.root,null,this.comparator,!1)}getIteratorFrom(e){return new ey(this.root,e,this.comparator,!1)}getReverseIterator(){return new ey(this.root,null,this.comparator,!0)}getReverseIteratorFrom(e){return new ey(this.root,e,this.comparator,!0)}}class ey{constructor(e,t,r,n){this.isReverse=n,this.nodeStack=[];let s=1;for(;!e.isEmpty();)if(s=t?r(e.key,t):1,t&&n&&(s*=-1),s<0)e=this.isReverse?e.left:e.right;else{if(0===s){this.nodeStack.push(e);break}this.nodeStack.push(e),e=this.isReverse?e.right:e.left}}getNext(){let e=this.nodeStack.pop(),t={key:e.key,value:e.value};if(this.isReverse)for(e=e.left;!e.isEmpty();)this.nodeStack.push(e),e=e.right;else for(e=e.right;!e.isEmpty();)this.nodeStack.push(e),e=e.left;return t}hasNext(){return this.nodeStack.length>0}peek(){if(0===this.nodeStack.length)return null;let e=this.nodeStack[this.nodeStack.length-1];return{key:e.key,value:e.value}}}class ew{constructor(e,t,r,n,s){this.key=e,this.value=t,this.color=null!=r?r:ew.RED,this.left=null!=n?n:ew.EMPTY,this.right=null!=s?s:ew.EMPTY,this.size=this.left.size+1+this.right.size}copy(e,t,r,n,s){return new ew(null!=e?e:this.key,null!=t?t:this.value,null!=r?r:this.color,null!=n?n:this.left,null!=s?s:this.right)}isEmpty(){return!1}inorderTraversal(e){return this.left.inorderTraversal(e)||e(this.key,this.value)||this.right.inorderTraversal(e)}reverseTraversal(e){return this.right.reverseTraversal(e)||e(this.key,this.value)||this.left.reverseTraversal(e)}min(){return this.left.isEmpty()?this:this.left.min()}minKey(){return this.min().key}maxKey(){return this.right.isEmpty()?this.key:this.right.maxKey()}insert(e,t,r){let n=this,s=r(e,n.key);return(n=s<0?n.copy(null,null,null,n.left.insert(e,t,r),null):0===s?n.copy(null,t,null,null,null):n.copy(null,null,null,null,n.right.insert(e,t,r))).fixUp()}removeMin(){if(this.left.isEmpty())return ew.EMPTY;let e=this;return e.left.isRed()||e.left.left.isRed()||(e=e.moveRedLeft()),(e=e.copy(null,null,null,e.left.removeMin(),null)).fixUp()}remove(e,t){let r,n=this;if(0>t(e,n.key))n.left.isEmpty()||n.left.isRed()||n.left.left.isRed()||(n=n.moveRedLeft()),n=n.copy(null,null,null,n.left.remove(e,t),null);else{if(n.left.isRed()&&(n=n.rotateRight()),n.right.isEmpty()||n.right.isRed()||n.right.left.isRed()||(n=n.moveRedRight()),0===t(e,n.key)){if(n.right.isEmpty())return ew.EMPTY;r=n.right.min(),n=n.copy(r.key,r.value,null,null,n.right.removeMin())}n=n.copy(null,null,null,null,n.right.remove(e,t))}return n.fixUp()}isRed(){return this.color}fixUp(){let e=this;return e.right.isRed()&&!e.left.isRed()&&(e=e.rotateLeft()),e.left.isRed()&&e.left.left.isRed()&&(e=e.rotateRight()),e.left.isRed()&&e.right.isRed()&&(e=e.colorFlip()),e}moveRedLeft(){let e=this.colorFlip();return e.right.left.isRed()&&(e=(e=(e=e.copy(null,null,null,null,e.right.rotateRight())).rotateLeft()).colorFlip()),e}moveRedRight(){let e=this.colorFlip();return e.left.left.isRed()&&(e=(e=e.rotateRight()).colorFlip()),e}rotateLeft(){let e=this.copy(null,null,ew.RED,null,this.right.left);return this.right.copy(null,null,this.color,e,null)}rotateRight(){let e=this.copy(null,null,ew.RED,this.left.right,null);return this.left.copy(null,null,this.color,null,e)}colorFlip(){let e=this.left.copy(null,null,!this.left.color,null,null),t=this.right.copy(null,null,!this.right.color,null,null);return this.copy(null,null,!this.color,e,t)}checkMaxDepth(){return Math.pow(2,this.check())<=this.size+1}check(){if(this.isRed()&&this.left.isRed())throw S(43730,{key:this.key,value:this.value});if(this.right.isRed())throw S(14113,{key:this.key,value:this.value});let e=this.left.check();if(e!==this.right.check())throw S(27949);return e+ +!this.isRed()}}ew.EMPTY=null,ew.RED=!0,ew.BLACK=!1,ew.EMPTY=new class{constructor(){this.size=0}get key(){throw S(57766)}get value(){throw S(16141)}get color(){throw S(16727)}get left(){throw S(29726)}get right(){throw S(36894)}copy(e,t,r,n,s){return this}insert(e,t,r){return new ew(e,t)}remove(e,t){return this}isEmpty(){return!0}inorderTraversal(e){return!1}reverseTraversal(e){return!1}minKey(){return null}maxKey(){return null}isRed(){return!1}checkMaxDepth(){return!0}check(){return 0}};class ev{constructor(e){this.comparator=e,this.data=new ep(this.comparator)}has(e){return null!==this.data.get(e)}first(){return this.data.minKey()}last(){return this.data.maxKey()}get size(){return this.data.size}indexOf(e){return this.data.indexOf(e)}forEach(e){this.data.inorderTraversal((t,r)=>(e(t),!1))}forEachInRange(e,t){let r=this.data.getIteratorFrom(e[0]);for(;r.hasNext();){let n=r.getNext();if(this.comparator(n.key,e[1])>=0)return;t(n.key)}}forEachWhile(e,t){let r;for(r=void 0!==t?this.data.getIteratorFrom(t):this.data.getIterator();r.hasNext();)if(!e(r.getNext().key))return}firstAfterOrEqual(e){let t=this.data.getIteratorFrom(e);return t.hasNext()?t.getNext().key:null}getIterator(){return new e_(this.data.getIterator())}getIteratorFrom(e){return new e_(this.data.getIteratorFrom(e))}add(e){return this.copy(this.data.remove(e).insert(e,!0))}delete(e){return this.has(e)?this.copy(this.data.remove(e)):this}isEmpty(){return this.data.isEmpty()}unionWith(e){let t=this;return t.size<e.size&&(t=e,e=this),e.forEach(e=>{t=t.add(e)}),t}isEqual(e){if(!(e instanceof ev)||this.size!==e.size)return!1;let t=this.data.getIterator(),r=e.data.getIterator();for(;t.hasNext();){let e=t.getNext().key,n=r.getNext().key;if(0!==this.comparator(e,n))return!1}return!0}toArray(){let e=[];return this.forEach(t=>{e.push(t)}),e}toString(){let e=[];return this.forEach(t=>e.push(t)),"SortedSet("+e.toString()+")"}copy(e){let t=new ev(this.comparator);return t.data=e,t}}class e_{constructor(e){this.iter=e}getNext(){return this.iter.getNext().key}hasNext(){return this.iter.hasNext()}}class eE{constructor(e){this.fields=e,e.sort(Q.comparator)}static empty(){return new eE([])}unionWith(e){let t=new ev(Q.comparator);for(let e of this.fields)t=t.add(e);for(let r of e)t=t.add(r);return new eE(t.toArray())}covers(e){for(let t of this.fields)if(t.isPrefixOf(e))return!0;return!1}isEqual(e){return B(this.fields,e.fields,(e,t)=>e.isEqual(t))}}class eT extends Error{constructor(){super(...arguments),this.name="Base64DecodeError"}}class eI{constructor(e){this.binaryString=e}static fromBase64String(e){return new eI(function(e){try{return atob(e)}catch(e){throw"undefined"!=typeof DOMException&&e instanceof DOMException?new eT("Invalid base64 string: "+e):e}}(e))}static fromUint8Array(e){return new eI(function(e){let t="";for(let r=0;r<e.length;++r)t+=String.fromCharCode(e[r]);return t}(e))}[Symbol.iterator](){let e=0;return{next:()=>e<this.binaryString.length?{value:this.binaryString.charCodeAt(e++),done:!1}:{value:void 0,done:!0}}}toBase64(){return btoa(this.binaryString)}toUint8Array(){var e=this.binaryString;let t=new Uint8Array(e.length);for(let r=0;r<e.length;r++)t[r]=e.charCodeAt(r);return t}approximateByteSize(){return 2*this.binaryString.length}compareTo(e){return U(this.binaryString,e.binaryString)}isEqual(e){return this.binaryString===e.binaryString}}eI.EMPTY_BYTE_STRING=new eI("");let eS=new RegExp(/^\d{4}-\d\d-\d\dT\d\d:\d\d:\d\d(?:\.(\d+))?Z$/);function eC(e){if(N(!!e,39018),"string"==typeof e){let t=0,r=eS.exec(e);if(N(!!r,46558,{timestamp:e}),r[1]){let e=r[1];t=Number(e=(e+"000000000").substr(0,9))}return{seconds:Math.floor(new Date(e).getTime()/1e3),nanos:t}}return{seconds:eN(e.seconds),nanos:eN(e.nanos)}}function eN(e){return"number"==typeof e?e:"string"==typeof e?Number(e):0}function eA(e){return"string"==typeof e?eI.fromBase64String(e):eI.fromUint8Array(e)}let eb="server_timestamp",eD="__type__",ek="__previous_value__",ex="__local_write_time__";function eR(e){return(e?.mapValue?.fields||{})[eD]?.stringValue===eb}function eV(e){let t=e.mapValue.fields[ek];return eR(t)?eV(t):t}function eL(e){let t=eC(e.mapValue.fields[ex].timestampValue);return new en(t.seconds,t.nanos)}class eO{constructor(e,t,r,n,s,i,a,o,l,u){this.databaseId=e,this.appId=t,this.persistenceKey=r,this.host=n,this.ssl=s,this.forceLongPolling=i,this.autoDetectLongPolling=a,this.longPollingOptions=o,this.useFetchStreams=l,this.isUsingEmulator=u}}let eM="(default)";class eF{constructor(e,t){this.projectId=e,this.database=t||eM}static empty(){return new eF("","")}get isDefaultDatabase(){return this.database===eM}isEqual(e){return e instanceof eF&&e.projectId===this.projectId&&e.database===this.database}}let eP="__type__",eU="__max__",eq={mapValue:{fields:{__type__:{stringValue:eU}}}},e$="__vector__",eB="value";function ez(e){return"nullValue"in e?0:"booleanValue"in e?1:"integerValue"in e||"doubleValue"in e?2:"timestampValue"in e?3:"stringValue"in e?5:"bytesValue"in e?6:"referenceValue"in e?7:"geoPointValue"in e?8:"arrayValue"in e?9:"mapValue"in e?eR(e)?4:e4(e)?0x1fffffffffffff:e2(e)?10:11:S(28295,{value:e})}function ej(e,t){if(e===t)return!0;let r=ez(e);if(r!==ez(t))return!1;switch(r){case 0:case 0x1fffffffffffff:return!0;case 1:return e.booleanValue===t.booleanValue;case 4:return eL(e).isEqual(eL(t));case 3:if("string"==typeof e.timestampValue&&"string"==typeof t.timestampValue&&e.timestampValue.length===t.timestampValue.length)return e.timestampValue===t.timestampValue;let n=eC(e.timestampValue),s=eC(t.timestampValue);return n.seconds===s.seconds&&n.nanos===s.nanos;case 5:return e.stringValue===t.stringValue;case 6:return eA(e.bytesValue).isEqual(eA(t.bytesValue));case 7:return e.referenceValue===t.referenceValue;case 8:return eN(e.geoPointValue.latitude)===eN(t.geoPointValue.latitude)&&eN(e.geoPointValue.longitude)===eN(t.geoPointValue.longitude);case 2:if("integerValue"in e&&"integerValue"in t)return eN(e.integerValue)===eN(t.integerValue);if("doubleValue"in e&&"doubleValue"in t){let r=eN(e.doubleValue),n=eN(t.doubleValue);return r===n?ed(r)===ed(n):isNaN(r)&&isNaN(n)}return!1;case 9:return B(e.arrayValue.values||[],t.arrayValue.values||[],ej);case 10:case 11:let i=e.mapValue.fields||{},a=t.mapValue.fields||{};if(ef(i)!==ef(a))return!1;for(let e in i)if(i.hasOwnProperty(e)&&(void 0===a[e]||!ej(i[e],a[e])))return!1;return!0;default:return S(52216,{left:e})}}function eG(e,t){return void 0!==(e.values||[]).find(e=>ej(e,t))}function eK(e,t){if(e===t)return 0;let r=ez(e),n=ez(t);if(r!==n)return U(r,n);switch(r){case 0:case 0x1fffffffffffff:return 0;case 1:return U(e.booleanValue,t.booleanValue);case 2:let s=eN(e.integerValue||e.doubleValue),i=eN(t.integerValue||t.doubleValue);return s<i?-1:s>i?1:s===i?0:isNaN(s)?isNaN(i)?0:-1:1;case 3:return eQ(e.timestampValue,t.timestampValue);case 4:return eQ(eL(e),eL(t));case 5:return q(e.stringValue,t.stringValue);case 6:return function(e,t){let r=eA(e),n=eA(t);return r.compareTo(n)}(e.bytesValue,t.bytesValue);case 7:return function(e,t){let r=e.split("/"),n=t.split("/");for(let e=0;e<r.length&&e<n.length;e++){let t=U(r[e],n[e]);if(0!==t)return t}return U(r.length,n.length)}(e.referenceValue,t.referenceValue);case 8:return function(e,t){let r=U(eN(e.latitude),eN(t.latitude));return 0!==r?r:U(eN(e.longitude),eN(t.longitude))}(e.geoPointValue,t.geoPointValue);case 9:return eH(e.arrayValue,t.arrayValue);case 10:return function(e,t){let r=e.fields||{},n=t.fields||{},s=r[eB]?.arrayValue,i=n[eB]?.arrayValue,a=U(s?.values?.length||0,i?.values?.length||0);return 0!==a?a:eH(s,i)}(e.mapValue,t.mapValue);case 11:return function(e,t){if(e===eq.mapValue&&t===eq.mapValue)return 0;if(e===eq.mapValue)return 1;if(t===eq.mapValue)return -1;let r=e.fields||{},n=Object.keys(r),s=t.fields||{},i=Object.keys(s);n.sort(),i.sort();for(let e=0;e<n.length&&e<i.length;++e){let t=q(n[e],i[e]);if(0!==t)return t;let a=eK(r[n[e]],s[i[e]]);if(0!==a)return a}return U(n.length,i.length)}(e.mapValue,t.mapValue);default:throw S(23264,{he:r})}}function eQ(e,t){if("string"==typeof e&&"string"==typeof t&&e.length===t.length)return U(e,t);let r=eC(e),n=eC(t),s=U(r.seconds,n.seconds);return 0!==s?s:U(r.nanos,n.nanos)}function eH(e,t){let r=e.values||[],n=t.values||[];for(let e=0;e<r.length&&e<n.length;++e){let t=eK(r[e],n[e]);if(t)return t}return U(r.length,n.length)}function eW(e){var t,r;return"nullValue"in e?"null":"booleanValue"in e?""+e.booleanValue:"integerValue"in e?""+e.integerValue:"doubleValue"in e?""+e.doubleValue:"timestampValue"in e?function(e){let t=eC(e);return`time(${t.seconds},${t.nanos})`}(e.timestampValue):"stringValue"in e?e.stringValue:"bytesValue"in e?eA(e.bytesValue).toBase64():"referenceValue"in e?(t=e.referenceValue,H.fromName(t).toString()):"geoPointValue"in e?(r=e.geoPointValue,`geo(${r.latitude},${r.longitude})`):"arrayValue"in e?function(e){let t="[",r=!0;for(let n of e.values||[])r?r=!1:t+=",",t+=eW(n);return t+"]"}(e.arrayValue):"mapValue"in e?function(e){let t=Object.keys(e.fields||{}).sort(),r="{",n=!0;for(let s of t)n?n=!1:r+=",",r+=`${s}:${eW(e.fields[s])}`;return r+"}"}(e.mapValue):S(61005,{value:e})}function eY(e,t){return{referenceValue:`projects/${e.projectId}/databases/${e.database}/documents/${t.path.canonicalString()}`}}function eX(e){return!!e&&"integerValue"in e}function eJ(e){return!!e&&"arrayValue"in e}function eZ(e){return!!e&&"nullValue"in e}function e0(e){return!!e&&"doubleValue"in e&&isNaN(Number(e.doubleValue))}function e1(e){return!!e&&"mapValue"in e}function e2(e){return(e?.mapValue?.fields||{})[eP]?.stringValue===e$}function e3(e){if(e.geoPointValue)return{geoPointValue:{...e.geoPointValue}};if(e.timestampValue&&"object"==typeof e.timestampValue)return{timestampValue:{...e.timestampValue}};if(e.mapValue){let t={mapValue:{fields:{}}};return em(e.mapValue.fields,(e,r)=>t.mapValue.fields[e]=e3(r)),t}if(e.arrayValue){let t={arrayValue:{values:[]}};for(let r=0;r<(e.arrayValue.values||[]).length;++r)t.arrayValue.values[r]=e3(e.arrayValue.values[r]);return t}return{...e}}function e4(e){return(((e.mapValue||{}).fields||{}).__type__||{}).stringValue===eU}class e6{constructor(e){this.value=e}static empty(){return new e6({mapValue:{}})}field(e){if(e.isEmpty())return this.value;{let t=this.value;for(let r=0;r<e.length-1;++r)if(!e1(t=(t.mapValue.fields||{})[e.get(r)]))return null;return(t=(t.mapValue.fields||{})[e.lastSegment()])||null}}set(e,t){this.getFieldsMap(e.popLast())[e.lastSegment()]=e3(t)}setAll(e){let t=Q.emptyPath(),r={},n=[];e.forEach((e,s)=>{if(!t.isImmediateParentOf(s)){let e=this.getFieldsMap(t);this.applyChanges(e,r,n),r={},n=[],t=s.popLast()}e?r[s.lastSegment()]=e3(e):n.push(s.lastSegment())});let s=this.getFieldsMap(t);this.applyChanges(s,r,n)}delete(e){let t=this.field(e.popLast());e1(t)&&t.mapValue.fields&&delete t.mapValue.fields[e.lastSegment()]}isEqual(e){return ej(this.value,e.value)}getFieldsMap(e){let t=this.value;t.mapValue.fields||(t.mapValue={fields:{}});for(let r=0;r<e.length;++r){let n=t.mapValue.fields[e.get(r)];e1(n)&&n.mapValue.fields||(n={mapValue:{fields:{}}},t.mapValue.fields[e.get(r)]=n),t=n}return t.mapValue.fields}applyChanges(e,t,r){for(let n of(em(t,(t,r)=>e[t]=r),r))delete e[n]}clone(){return new e6(e3(this.value))}}class e5{constructor(e,t,r,n,s,i,a){this.key=e,this.documentType=t,this.version=r,this.readTime=n,this.createTime=s,this.data=i,this.documentState=a}static newInvalidDocument(e){return new e5(e,0,es.min(),es.min(),es.min(),e6.empty(),0)}static newFoundDocument(e,t,r,n){return new e5(e,1,t,es.min(),r,n,0)}static newNoDocument(e,t){return new e5(e,2,t,es.min(),es.min(),e6.empty(),0)}static newUnknownDocument(e,t){return new e5(e,3,t,es.min(),es.min(),e6.empty(),2)}convertToFoundDocument(e,t){return this.createTime.isEqual(es.min())&&(2===this.documentType||0===this.documentType)&&(this.createTime=e),this.version=e,this.documentType=1,this.data=t,this.documentState=0,this}convertToNoDocument(e){return this.version=e,this.documentType=2,this.data=e6.empty(),this.documentState=0,this}convertToUnknownDocument(e){return this.version=e,this.documentType=3,this.data=e6.empty(),this.documentState=2,this}setHasCommittedMutations(){return this.documentState=2,this}setHasLocalMutations(){return this.documentState=1,this.version=es.min(),this}setReadTime(e){return this.readTime=e,this}get hasLocalMutations(){return 1===this.documentState}get hasCommittedMutations(){return 2===this.documentState}get hasPendingWrites(){return this.hasLocalMutations||this.hasCommittedMutations}isValidDocument(){return 0!==this.documentType}isFoundDocument(){return 1===this.documentType}isNoDocument(){return 2===this.documentType}isUnknownDocument(){return 3===this.documentType}isEqual(e){return e instanceof e5&&this.key.isEqual(e.key)&&this.version.isEqual(e.version)&&this.documentType===e.documentType&&this.documentState===e.documentState&&this.data.isEqual(e.data)}mutableCopy(){return new e5(this.key,this.documentType,this.version,this.readTime,this.createTime,this.data.clone(),this.documentState)}toString(){return`Document(${this.key}, ${this.version}, ${JSON.stringify(this.data.value)}, {createTime: ${this.createTime}}), {documentType: ${this.documentType}}), {documentState: ${this.documentState}})`}}class e8{constructor(e,t){this.position=e,this.inclusive=t}}function e9(e,t,r){let n=0;for(let s=0;s<e.position.length;s++){let i=t[s],a=e.position[s];if(n=i.field.isKeyField()?H.comparator(H.fromName(a.referenceValue),r.key):eK(a,r.data.field(i.field)),"desc"===i.dir&&(n*=-1),0!==n)break}return n}function e7(e,t){if(null===e)return null===t;if(null===t||e.inclusive!==t.inclusive||e.position.length!==t.position.length)return!1;for(let r=0;r<e.position.length;r++)if(!ej(e.position[r],t.position[r]))return!1;return!0}class te{constructor(e,t="asc"){this.field=e,this.dir=t}}class tt{}class tr extends tt{constructor(e,t,r){super(),this.field=e,this.op=t,this.value=r}static create(e,t,r){return e.isKeyField()?"in"===t||"not-in"===t?this.createKeyFieldInFilter(e,t,r):new ta(e,t,r):"array-contains"===t?new th(e,r):"in"===t?new tc(e,r):"not-in"===t?new td(e,r):"array-contains-any"===t?new tf(e,r):new tr(e,t,r)}static createKeyFieldInFilter(e,t,r){return"in"===t?new to(e,r):new tl(e,r)}matches(e){let t=e.data.field(this.field);return"!="===this.op?null!==t&&void 0===t.nullValue&&this.matchesComparison(eK(t,this.value)):null!==t&&ez(this.value)===ez(t)&&this.matchesComparison(eK(t,this.value))}matchesComparison(e){switch(this.op){case"<":return e<0;case"<=":return e<=0;case"==":return 0===e;case"!=":return 0!==e;case">":return e>0;case">=":return e>=0;default:return S(47266,{operator:this.op})}}isInequality(){return["<","<=",">",">=","!=","not-in"].indexOf(this.op)>=0}getFlattenedFilters(){return[this]}getFilters(){return[this]}}class tn extends tt{constructor(e,t){super(),this.filters=e,this.op=t,this.Pe=null}static create(e,t){return new tn(e,t)}matches(e){return ts(this)?void 0===this.filters.find(t=>!t.matches(e)):void 0!==this.filters.find(t=>t.matches(e))}getFlattenedFilters(){return null!==this.Pe||(this.Pe=this.filters.reduce((e,t)=>e.concat(t.getFlattenedFilters()),[])),this.Pe}getFilters(){return Object.assign([],this.filters)}}function ts(e){return"and"===e.op}function ti(e){for(let t of e.filters)if(t instanceof tn)return!1;return!0}class ta extends tr{constructor(e,t,r){super(e,t,r),this.key=H.fromName(r.referenceValue)}matches(e){let t=H.comparator(e.key,this.key);return this.matchesComparison(t)}}class to extends tr{constructor(e,t){super(e,"in",t),this.keys=tu("in",t)}matches(e){return this.keys.some(t=>t.isEqual(e.key))}}class tl extends tr{constructor(e,t){super(e,"not-in",t),this.keys=tu("not-in",t)}matches(e){return!this.keys.some(t=>t.isEqual(e.key))}}function tu(e,t){return(t.arrayValue?.values||[]).map(e=>H.fromName(e.referenceValue))}class th extends tr{constructor(e,t){super(e,"array-contains",t)}matches(e){let t=e.data.field(this.field);return eJ(t)&&eG(t.arrayValue,this.value)}}class tc extends tr{constructor(e,t){super(e,"in",t)}matches(e){let t=e.data.field(this.field);return null!==t&&eG(this.value.arrayValue,t)}}class td extends tr{constructor(e,t){super(e,"not-in",t)}matches(e){if(eG(this.value.arrayValue,{nullValue:"NULL_VALUE"}))return!1;let t=e.data.field(this.field);return null!==t&&void 0===t.nullValue&&!eG(this.value.arrayValue,t)}}class tf extends tr{constructor(e,t){super(e,"array-contains-any",t)}matches(e){let t=e.data.field(this.field);return!(!eJ(t)||!t.arrayValue.values)&&t.arrayValue.values.some(e=>eG(this.value.arrayValue,e))}}class tm{constructor(e,t=null,r=[],n=[],s=null,i=null,a=null){this.path=e,this.collectionGroup=t,this.orderBy=r,this.filters=n,this.limit=s,this.startAt=i,this.endAt=a,this.Te=null}}function tg(e,t=null,r=[],n=[],s=null,i=null,a=null){return new tm(e,t,r,n,s,i,a)}function tp(e){if(null===e.Te){let t=e.path.canonicalString();null!==e.collectionGroup&&(t+="|cg:"+e.collectionGroup),t+="|f:",t+=e.filters.map(e=>(function e(t){if(t instanceof tr)return t.field.canonicalString()+t.op.toString()+eW(t.value);if(ti(t)&&ts(t))return t.filters.map(t=>e(t)).join(",");{let r=t.filters.map(t=>e(t)).join(",");return`${t.op}(${r})`}})(e)).join(","),t+="|ob:",t+=e.orderBy.map(e=>e.field.canonicalString()+e.dir).join(","),null==e.limit||(t+="|l:",t+=e.limit),e.startAt&&(t+="|lb:",t+=e.startAt.inclusive?"b:":"a:",t+=e.startAt.position.map(e=>eW(e)).join(",")),e.endAt&&(t+="|ub:",t+=e.endAt.inclusive?"a:":"b:",t+=e.endAt.position.map(e=>eW(e)).join(",")),e.Te=t}return e.Te}function ty(e,t){if(e.limit!==t.limit||e.orderBy.length!==t.orderBy.length)return!1;for(let s=0;s<e.orderBy.length;s++){var r,n;if(r=e.orderBy[s],n=t.orderBy[s],!(r.dir===n.dir&&r.field.isEqual(n.field)))return!1}if(e.filters.length!==t.filters.length)return!1;for(let r=0;r<e.filters.length;r++)if(!function e(t,r){return t instanceof tr?r instanceof tr&&t.op===r.op&&t.field.isEqual(r.field)&&ej(t.value,r.value):t instanceof tn?r instanceof tn&&t.op===r.op&&t.filters.length===r.filters.length&&t.filters.reduce((t,n,s)=>t&&e(n,r.filters[s]),!0):void S(19439)}(e.filters[r],t.filters[r]))return!1;return e.collectionGroup===t.collectionGroup&&!!e.path.isEqual(t.path)&&!!e7(e.startAt,t.startAt)&&e7(e.endAt,t.endAt)}function tw(e){return H.isDocumentKey(e.path)&&null===e.collectionGroup&&0===e.filters.length}class tv{constructor(e,t=null,r=[],n=[],s=null,i="F",a=null,o=null){this.path=e,this.collectionGroup=t,this.explicitOrderBy=r,this.filters=n,this.limit=s,this.limitType=i,this.startAt=a,this.endAt=o,this.Ie=null,this.Ee=null,this.de=null,this.startAt,this.endAt}}function t_(e){return 0===e.filters.length&&null===e.limit&&null==e.startAt&&null==e.endAt&&(0===e.explicitOrderBy.length||1===e.explicitOrderBy.length&&e.explicitOrderBy[0].field.isKeyField())}function tE(e){return null!==e.collectionGroup}function tT(e){if(null===e.Ie){let t;e.Ie=[];let r=new Set;for(let t of e.explicitOrderBy)e.Ie.push(t),r.add(t.field.canonicalString());let n=e.explicitOrderBy.length>0?e.explicitOrderBy[e.explicitOrderBy.length-1].dir:"asc";(t=new ev(Q.comparator),e.filters.forEach(e=>{e.getFlattenedFilters().forEach(e=>{e.isInequality()&&(t=t.add(e.field))})}),t).forEach(t=>{r.has(t.canonicalString())||t.isKeyField()||e.Ie.push(new te(t,n))}),r.has(Q.keyField().canonicalString())||e.Ie.push(new te(Q.keyField(),n))}return e.Ie}function tI(e){return e.Ee||(e.Ee=function(e,t){if("F"===e.limitType)return tg(e.path,e.collectionGroup,t,e.filters,e.limit,e.startAt,e.endAt);{t=t.map(e=>{let t="desc"===e.dir?"asc":"desc";return new te(e.field,t)});let r=e.endAt?new e8(e.endAt.position,e.endAt.inclusive):null,n=e.startAt?new e8(e.startAt.position,e.startAt.inclusive):null;return tg(e.path,e.collectionGroup,t,e.filters,e.limit,r,n)}}(e,tT(e))),e.Ee}function tS(e,t){let r=e.filters.concat([t]);return new tv(e.path,e.collectionGroup,e.explicitOrderBy.slice(),r,e.limit,e.limitType,e.startAt,e.endAt)}function tC(e,t,r){return new tv(e.path,e.collectionGroup,e.explicitOrderBy.slice(),e.filters.slice(),t,r,e.startAt,e.endAt)}function tN(e,t){return ty(tI(e),tI(t))&&e.limitType===t.limitType}function tA(e){return`${tp(tI(e))}|lt:${e.limitType}`}function tb(e){var t;let r;return`Query(target=${r=(t=tI(e)).path.canonicalString(),null!==t.collectionGroup&&(r+=" collectionGroup="+t.collectionGroup),t.filters.length>0&&(r+=`, filters: [${t.filters.map(e=>(function e(t){return t instanceof tr?`${t.field.canonicalString()} ${t.op} ${eW(t.value)}`:t instanceof tn?t.op.toString()+" {"+t.getFilters().map(e).join(" ,")+"}":"Filter"})(e)).join(", ")}]`),null==t.limit||(r+=", limit: "+t.limit),t.orderBy.length>0&&(r+=`, orderBy: [${t.orderBy.map(e=>`${e.field.canonicalString()} (${e.dir})`).join(", ")}]`),t.startAt&&(r+=", startAt: ",r+=t.startAt.inclusive?"b:":"a:",r+=t.startAt.position.map(e=>eW(e)).join(",")),t.endAt&&(r+=", endAt: ",r+=t.endAt.inclusive?"a:":"b:",r+=t.endAt.position.map(e=>eW(e)).join(",")),`Target(${r})`}; limitType=${e.limitType})`}function tD(e,t){return t.isFoundDocument()&&function(e,t){let r=t.key.path;return null!==e.collectionGroup?t.key.hasCollectionId(e.collectionGroup)&&e.path.isPrefixOf(r):H.isDocumentKey(e.path)?e.path.isEqual(r):e.path.isImmediateParentOf(r)}(e,t)&&function(e,t){for(let r of tT(e))if(!r.field.isKeyField()&&null===t.data.field(r.field))return!1;return!0}(e,t)&&function(e,t){for(let r of e.filters)if(!r.matches(t))return!1;return!0}(e,t)&&(!e.startAt||!!function(e,t,r){let n=e9(e,t,r);return e.inclusive?n<=0:n<0}(e.startAt,tT(e),t))&&(!e.endAt||!!function(e,t,r){let n=e9(e,t,r);return e.inclusive?n>=0:n>0}(e.endAt,tT(e),t))}function tk(e){return(t,r)=>{let n=!1;for(let s of tT(e)){let e=function(e,t,r){let n=e.field.isKeyField()?H.comparator(t.key,r.key):function(e,t,r){let n=t.data.field(e),s=r.data.field(e);return null!==n&&null!==s?eK(n,s):S(42886)}(e.field,t,r);switch(e.dir){case"asc":return n;case"desc":return -1*n;default:return S(19790,{direction:e.dir})}}(s,t,r);if(0!==e)return e;n=n||s.field.isKeyField()}return 0}}class tx{constructor(e,t){this.mapKeyFn=e,this.equalsFn=t,this.inner={},this.innerSize=0}get(e){let t=this.mapKeyFn(e),r=this.inner[t];if(void 0!==r){for(let[t,n]of r)if(this.equalsFn(t,e))return n}}has(e){return void 0!==this.get(e)}set(e,t){let r=this.mapKeyFn(e),n=this.inner[r];if(void 0===n)return this.inner[r]=[[e,t]],void this.innerSize++;for(let r=0;r<n.length;r++)if(this.equalsFn(n[r][0],e))return void(n[r]=[e,t]);n.push([e,t]),this.innerSize++}delete(e){let t=this.mapKeyFn(e),r=this.inner[t];if(void 0===r)return!1;for(let n=0;n<r.length;n++)if(this.equalsFn(r[n][0],e))return 1===r.length?delete this.inner[t]:r.splice(n,1),this.innerSize--,!0;return!1}forEach(e){em(this.inner,(t,r)=>{for(let[t,n]of r)e(t,n)})}isEmpty(){return eg(this.inner)}size(){return this.innerSize}}let tR=new ep(H.comparator),tV=new ep(H.comparator);function tL(...e){let t=tV;for(let r of e)t=t.insert(r.key,r);return t}function tO(e){let t=tV;return e.forEach((e,r)=>t=t.insert(e,r.overlayedDocument)),t}function tM(){return new tx(e=>e.toString(),(e,t)=>e.isEqual(t))}let tF=new ep(H.comparator),tP=new ev(H.comparator);function tU(...e){let t=tP;for(let r of e)t=t.add(r);return t}let tq=new ev(U);function t$(e,t){if(e.useProto3Json){if(isNaN(t))return{doubleValue:"NaN"};if(t===1/0)return{doubleValue:"Infinity"};if(t===-1/0)return{doubleValue:"-Infinity"}}return{doubleValue:ed(t)?"-0":t}}function tB(e){return{integerValue:""+e}}class tz{constructor(){this._=void 0}}function tj(e,t){return e instanceof tY?eX(t)||t&&"doubleValue"in t?t:{integerValue:0}:null}class tG extends tz{}class tK extends tz{constructor(e){super(),this.elements=e}}function tQ(e,t){let r=tJ(t);for(let t of e.elements)r.some(e=>ej(e,t))||r.push(t);return{arrayValue:{values:r}}}class tH extends tz{constructor(e){super(),this.elements=e}}function tW(e,t){let r=tJ(t);for(let t of e.elements)r=r.filter(e=>!ej(e,t));return{arrayValue:{values:r}}}class tY extends tz{constructor(e,t){super(),this.serializer=e,this.Ae=t}}function tX(e){return eN(e.integerValue||e.doubleValue)}function tJ(e){return eJ(e)&&e.arrayValue.values?e.arrayValue.values.slice():[]}class tZ{constructor(e,t){this.field=e,this.transform=t}}class t0{constructor(e,t){this.version=e,this.transformResults=t}}class t1{constructor(e,t){this.updateTime=e,this.exists=t}static none(){return new t1}static exists(e){return new t1(void 0,e)}static updateTime(e){return new t1(e)}get isNone(){return void 0===this.updateTime&&void 0===this.exists}isEqual(e){return this.exists===e.exists&&(this.updateTime?!!e.updateTime&&this.updateTime.isEqual(e.updateTime):!e.updateTime)}}function t2(e,t){return void 0!==e.updateTime?t.isFoundDocument()&&t.version.isEqual(e.updateTime):void 0===e.exists||e.exists===t.isFoundDocument()}class t3{}function t4(e,t){if(!e.hasLocalMutations||t&&0===t.fields.length)return null;if(null===t)return e.isNoDocument()?new rr(e.key,t1.none()):new t8(e.key,e.data,t1.none());{let r=e.data,n=e6.empty(),s=new ev(Q.comparator);for(let e of t.fields)if(!s.has(e)){let t=r.field(e);null===t&&e.length>1&&(e=e.popLast(),t=r.field(e)),null===t?n.delete(e):n.set(e,t),s=s.add(e)}return new t9(e.key,n,new eE(s.toArray()),t1.none())}}function t6(e,t,r,n){return e instanceof t8?function(e,t,r,n){if(!t2(e.precondition,t))return r;let s=e.value.clone(),i=rt(e.fieldTransforms,n,t);return s.setAll(i),t.convertToFoundDocument(t.version,s).setHasLocalMutations(),null}(e,t,r,n):e instanceof t9?function(e,t,r,n){if(!t2(e.precondition,t))return r;let s=rt(e.fieldTransforms,n,t),i=t.data;return(i.setAll(t7(e)),i.setAll(s),t.convertToFoundDocument(t.version,i).setHasLocalMutations(),null===r)?null:r.unionWith(e.fieldMask.fields).unionWith(e.fieldTransforms.map(e=>e.field))}(e,t,r,n):t2(e.precondition,t)?(t.convertToNoDocument(t.version).setHasLocalMutations(),null):r}function t5(e,t){var r,n;return e.type===t.type&&!!e.key.isEqual(t.key)&&!!e.precondition.isEqual(t.precondition)&&(r=e.fieldTransforms,n=t.fieldTransforms,!!(void 0===r&&void 0===n||!(!r||!n)&&B(r,n,(e,t)=>{var r,n;return e.field.isEqual(t.field)&&(r=e.transform,n=t.transform,r instanceof tK&&n instanceof tK||r instanceof tH&&n instanceof tH?B(r.elements,n.elements,ej):r instanceof tY&&n instanceof tY?ej(r.Ae,n.Ae):r instanceof tG&&n instanceof tG)})))&&(0===e.type?e.value.isEqual(t.value):1!==e.type||e.data.isEqual(t.data)&&e.fieldMask.isEqual(t.fieldMask))}class t8 extends t3{constructor(e,t,r,n=[]){super(),this.key=e,this.value=t,this.precondition=r,this.fieldTransforms=n,this.type=0}getFieldMask(){return null}}class t9 extends t3{constructor(e,t,r,n,s=[]){super(),this.key=e,this.data=t,this.fieldMask=r,this.precondition=n,this.fieldTransforms=s,this.type=1}getFieldMask(){return this.fieldMask}}function t7(e){let t=new Map;return e.fieldMask.fields.forEach(r=>{if(!r.isEmpty()){let n=e.data.field(r);t.set(r,n)}}),t}function re(e,t,r){let n=new Map;N(e.length===r.length,32656,{Re:r.length,Ve:e.length});for(let i=0;i<r.length;i++){var s;let a=e[i],o=a.transform,l=t.data.field(a.field);n.set(a.field,(s=r[i],o instanceof tK?tQ(o,l):o instanceof tH?tW(o,l):s))}return n}function rt(e,t,r){let n=new Map;for(let s of e){let e=s.transform,i=r.data.field(s.field);n.set(s.field,e instanceof tG?function(e,t){let r={fields:{[eD]:{stringValue:eb},[ex]:{timestampValue:{seconds:e.seconds,nanos:e.nanoseconds}}}};return t&&eR(t)&&(t=eV(t)),t&&(r.fields[ek]=t),{mapValue:r}}(t,i):e instanceof tK?tQ(e,i):e instanceof tH?tW(e,i):function(e,t){let r=tj(e,t),n=tX(r)+tX(e.Ae);return eX(r)&&eX(e.Ae)?tB(n):t$(e.serializer,n)}(e,i))}return n}class rr extends t3{constructor(e,t){super(),this.key=e,this.precondition=t,this.type=2,this.fieldTransforms=[]}getFieldMask(){return null}}class rn extends t3{constructor(e,t){super(),this.key=e,this.precondition=t,this.type=3,this.fieldTransforms=[]}getFieldMask(){return null}}class rs{constructor(e,t,r,n){this.batchId=e,this.localWriteTime=t,this.baseMutations=r,this.mutations=n}applyToRemoteDocument(e,t){let r=t.mutationResults;for(let t=0;t<this.mutations.length;t++){let n=this.mutations[t];n.key.isEqual(e.key)&&function(e,t,r){e instanceof t8?function(e,t,r){let n=e.value.clone(),s=re(e.fieldTransforms,t,r.transformResults);n.setAll(s),t.convertToFoundDocument(r.version,n).setHasCommittedMutations()}(e,t,r):e instanceof t9?function(e,t,r){if(!t2(e.precondition,t))return t.convertToUnknownDocument(r.version);let n=re(e.fieldTransforms,t,r.transformResults),s=t.data;s.setAll(t7(e)),s.setAll(n),t.convertToFoundDocument(r.version,s).setHasCommittedMutations()}(e,t,r):t.convertToNoDocument(r.version).setHasCommittedMutations()}(n,e,r[t])}}applyToLocalView(e,t){for(let r of this.baseMutations)r.key.isEqual(e.key)&&(t=t6(r,e,t,this.localWriteTime));for(let r of this.mutations)r.key.isEqual(e.key)&&(t=t6(r,e,t,this.localWriteTime));return t}applyToLocalDocumentSet(e,t){let r=tM();return this.mutations.forEach(n=>{let s=e.get(n.key),i=s.overlayedDocument,a=this.applyToLocalView(i,s.mutatedFields),o=t4(i,a=t.has(n.key)?null:a);null!==o&&r.set(n.key,o),i.isValidDocument()||i.convertToNoDocument(es.min())}),r}keys(){return this.mutations.reduce((e,t)=>e.add(t.key),tU())}isEqual(e){return this.batchId===e.batchId&&B(this.mutations,e.mutations,(e,t)=>t5(e,t))&&B(this.baseMutations,e.baseMutations,(e,t)=>t5(e,t))}}class ri{constructor(e,t,r,n){this.batch=e,this.commitVersion=t,this.mutationResults=r,this.docVersions=n}static from(e,t,r){N(e.mutations.length===r.length,58842,{me:e.mutations.length,fe:r.length});let n=tF,s=e.mutations;for(let e=0;e<s.length;e++)n=n.insert(s[e].key,r[e].version);return new ri(e,t,r,n)}}class ra{constructor(e,t){this.largestBatchId=e,this.mutation=t}getKey(){return this.mutation.key}isEqual(e){return null!==e&&this.mutation===e.mutation}toString(){return`Overlay{
      largestBatchId: ${this.largestBatchId},
      mutation: ${this.mutation.toString()}
    }`}}class ro{constructor(e,t){this.count=e,this.unchangedNames=t}}function rl(e){if(void 0===e)return E("GRPC error has no .code"),A.UNKNOWN;switch(e){case n.OK:return A.OK;case n.CANCELLED:return A.CANCELLED;case n.UNKNOWN:return A.UNKNOWN;case n.DEADLINE_EXCEEDED:return A.DEADLINE_EXCEEDED;case n.RESOURCE_EXHAUSTED:return A.RESOURCE_EXHAUSTED;case n.INTERNAL:return A.INTERNAL;case n.UNAVAILABLE:return A.UNAVAILABLE;case n.UNAUTHENTICATED:return A.UNAUTHENTICATED;case n.INVALID_ARGUMENT:return A.INVALID_ARGUMENT;case n.NOT_FOUND:return A.NOT_FOUND;case n.ALREADY_EXISTS:return A.ALREADY_EXISTS;case n.PERMISSION_DENIED:return A.PERMISSION_DENIED;case n.FAILED_PRECONDITION:return A.FAILED_PRECONDITION;case n.ABORTED:return A.ABORTED;case n.OUT_OF_RANGE:return A.OUT_OF_RANGE;case n.UNIMPLEMENTED:return A.UNIMPLEMENTED;case n.DATA_LOSS:return A.DATA_LOSS;default:return S(39323,{code:e})}}(s=n||(n={}))[s.OK=0]="OK",s[s.CANCELLED=1]="CANCELLED",s[s.UNKNOWN=2]="UNKNOWN",s[s.INVALID_ARGUMENT=3]="INVALID_ARGUMENT",s[s.DEADLINE_EXCEEDED=4]="DEADLINE_EXCEEDED",s[s.NOT_FOUND=5]="NOT_FOUND",s[s.ALREADY_EXISTS=6]="ALREADY_EXISTS",s[s.PERMISSION_DENIED=7]="PERMISSION_DENIED",s[s.UNAUTHENTICATED=16]="UNAUTHENTICATED",s[s.RESOURCE_EXHAUSTED=8]="RESOURCE_EXHAUSTED",s[s.FAILED_PRECONDITION=9]="FAILED_PRECONDITION",s[s.ABORTED=10]="ABORTED",s[s.OUT_OF_RANGE=11]="OUT_OF_RANGE",s[s.UNIMPLEMENTED=12]="UNIMPLEMENTED",s[s.INTERNAL=13]="INTERNAL",s[s.UNAVAILABLE=14]="UNAVAILABLE",s[s.DATA_LOSS=15]="DATA_LOSS";let ru=new c.jz([0xffffffff,0xffffffff],0);function rh(e){let t=(new TextEncoder).encode(e),r=new c.VV;return r.update(t),new Uint8Array(r.digest())}function rc(e){let t=new DataView(e.buffer),r=t.getUint32(0,!0),n=t.getUint32(4,!0),s=t.getUint32(8,!0),i=t.getUint32(12,!0);return[new c.jz([r,n],0),new c.jz([s,i],0)]}class rd{constructor(e,t,r){if(this.bitmap=e,this.padding=t,this.hashCount=r,t<0||t>=8)throw new rf(`Invalid padding: ${t}`);if(r<0||e.length>0&&0===this.hashCount)throw new rf(`Invalid hash count: ${r}`);if(0===e.length&&0!==t)throw new rf(`Invalid padding when bitmap length is 0: ${t}`);this.ge=8*e.length-t,this.pe=c.jz.fromNumber(this.ge)}ye(e,t,r){let n=e.add(t.multiply(c.jz.fromNumber(r)));return 1===n.compare(ru)&&(n=new c.jz([n.getBits(0),n.getBits(1)],0)),n.modulo(this.pe).toNumber()}we(e){return!!(this.bitmap[Math.floor(e/8)]&1<<e%8)}mightContain(e){if(0===this.ge)return!1;let[t,r]=rc(rh(e));for(let e=0;e<this.hashCount;e++){let n=this.ye(t,r,e);if(!this.we(n))return!1}return!0}static create(e,t,r){let n=new rd(new Uint8Array(Math.ceil(e/8)),e%8==0?0:8-e%8,t);return r.forEach(e=>n.insert(e)),n}insert(e){if(0===this.ge)return;let[t,r]=rc(rh(e));for(let e=0;e<this.hashCount;e++){let n=this.ye(t,r,e);this.Se(n)}}Se(e){let t=Math.floor(e/8);this.bitmap[t]|=1<<e%8}}class rf extends Error{constructor(){super(...arguments),this.name="BloomFilterError"}}class rm{constructor(e,t,r,n,s){this.snapshotVersion=e,this.targetChanges=t,this.targetMismatches=r,this.documentUpdates=n,this.resolvedLimboDocuments=s}static createSynthesizedRemoteEventForCurrentChange(e,t,r){let n=new Map;return n.set(e,rg.createSynthesizedTargetChangeForCurrentChange(e,t,r)),new rm(es.min(),n,new ep(U),tR,tU())}}class rg{constructor(e,t,r,n,s){this.resumeToken=e,this.current=t,this.addedDocuments=r,this.modifiedDocuments=n,this.removedDocuments=s}static createSynthesizedTargetChangeForCurrentChange(e,t,r){return new rg(r,t,tU(),tU(),tU())}}class rp{constructor(e,t,r,n){this.be=e,this.removedTargetIds=t,this.key=r,this.De=n}}class ry{constructor(e,t){this.targetId=e,this.Ce=t}}class rw{constructor(e,t,r=eI.EMPTY_BYTE_STRING,n=null){this.state=e,this.targetIds=t,this.resumeToken=r,this.cause=n}}class rv{constructor(){this.ve=0,this.Fe=rT(),this.Me=eI.EMPTY_BYTE_STRING,this.xe=!1,this.Oe=!0}get current(){return this.xe}get resumeToken(){return this.Me}get Ne(){return 0!==this.ve}get Be(){return this.Oe}Le(e){e.approximateByteSize()>0&&(this.Oe=!0,this.Me=e)}ke(){let e=tU(),t=tU(),r=tU();return this.Fe.forEach((n,s)=>{switch(s){case 0:e=e.add(n);break;case 2:t=t.add(n);break;case 1:r=r.add(n);break;default:S(38017,{changeType:s})}}),new rg(this.Me,this.xe,e,t,r)}qe(){this.Oe=!1,this.Fe=rT()}Qe(e,t){this.Oe=!0,this.Fe=this.Fe.insert(e,t)}$e(e){this.Oe=!0,this.Fe=this.Fe.remove(e)}Ue(){this.ve+=1}Ke(){this.ve-=1,N(this.ve>=0,3241,{ve:this.ve})}We(){this.Oe=!0,this.xe=!0}}class r_{constructor(e){this.Ge=e,this.ze=new Map,this.je=tR,this.Je=rE(),this.He=rE(),this.Ye=new ep(U)}Ze(e){for(let t of e.be)e.De&&e.De.isFoundDocument()?this.Xe(t,e.De):this.et(t,e.key,e.De);for(let t of e.removedTargetIds)this.et(t,e.key,e.De)}tt(e){this.forEachTarget(e,t=>{let r=this.nt(t);switch(e.state){case 0:this.rt(t)&&r.Le(e.resumeToken);break;case 1:r.Ke(),r.Ne||r.qe(),r.Le(e.resumeToken);break;case 2:r.Ke(),r.Ne||this.removeTarget(t);break;case 3:this.rt(t)&&(r.We(),r.Le(e.resumeToken));break;case 4:this.rt(t)&&(this.it(t),r.Le(e.resumeToken));break;default:S(56790,{state:e.state})}})}forEachTarget(e,t){e.targetIds.length>0?e.targetIds.forEach(t):this.ze.forEach((e,r)=>{this.rt(r)&&t(r)})}st(e){let t=e.targetId,r=e.Ce.count,n=this.ot(t);if(n){let s=n.target;if(tw(s))if(0===r){let e=new H(s.path);this.et(t,e,e5.newNoDocument(e,es.min()))}else N(1===r,20013,{expectedCount:r});else{let n=this._t(t);if(n!==r){let r=this.ut(e),s=r?this.ct(r,e,n):1;0!==s&&(this.it(t),this.Ye=this.Ye.insert(t,2===s?"TargetPurposeExistenceFilterMismatchBloom":"TargetPurposeExistenceFilterMismatch"))}}}}ut(e){let t,r,n=e.Ce.unchangedNames;if(!n||!n.bits)return null;let{bits:{bitmap:s="",padding:i=0},hashCount:a=0}=n;try{t=eA(s).toUint8Array()}catch(e){if(e instanceof eT)return T("Decoding the base64 bloom filter in existence filter failed ("+e.message+"); ignoring the bloom filter and falling back to full re-query."),null;throw e}try{r=new rd(t,i,a)}catch(e){return T(e instanceof rf?"BloomFilter error: ":"Applying bloom filter failed: ",e),null}return 0===r.ge?null:r}ct(e,t,r){return 2*(t.Ce.count!==r-this.Pt(e,t.targetId))}Pt(e,t){let r=this.Ge.getRemoteKeysForTarget(t),n=0;return r.forEach(r=>{let s=this.Ge.ht(),i=`projects/${s.projectId}/databases/${s.database}/documents/${r.path.canonicalString()}`;e.mightContain(i)||(this.et(t,r,null),n++)}),n}Tt(e){let t=new Map;this.ze.forEach((r,n)=>{let s=this.ot(n);if(s){if(r.current&&tw(s.target)){let t=new H(s.target.path);this.It(t).has(n)||this.Et(n,t)||this.et(n,t,e5.newNoDocument(t,e))}r.Be&&(t.set(n,r.ke()),r.qe())}});let r=tU();this.He.forEach((e,t)=>{let n=!0;t.forEachWhile(e=>{let t=this.ot(e);return!t||"TargetPurposeLimboResolution"===t.purpose||(n=!1,!1)}),n&&(r=r.add(e))}),this.je.forEach((t,r)=>r.setReadTime(e));let n=new rm(e,t,this.Ye,this.je,r);return this.je=tR,this.Je=rE(),this.He=rE(),this.Ye=new ep(U),n}Xe(e,t){if(!this.rt(e))return;let r=2*!!this.Et(e,t.key);this.nt(e).Qe(t.key,r),this.je=this.je.insert(t.key,t),this.Je=this.Je.insert(t.key,this.It(t.key).add(e)),this.He=this.He.insert(t.key,this.dt(t.key).add(e))}et(e,t,r){if(!this.rt(e))return;let n=this.nt(e);this.Et(e,t)?n.Qe(t,1):n.$e(t),this.He=this.He.insert(t,this.dt(t).delete(e)),this.He=this.He.insert(t,this.dt(t).add(e)),r&&(this.je=this.je.insert(t,r))}removeTarget(e){this.ze.delete(e)}_t(e){let t=this.nt(e).ke();return this.Ge.getRemoteKeysForTarget(e).size+t.addedDocuments.size-t.removedDocuments.size}Ue(e){this.nt(e).Ue()}nt(e){let t=this.ze.get(e);return t||(t=new rv,this.ze.set(e,t)),t}dt(e){let t=this.He.get(e);return t||(t=new ev(U),this.He=this.He.insert(e,t)),t}It(e){let t=this.Je.get(e);return t||(t=new ev(U),this.Je=this.Je.insert(e,t)),t}rt(e){let t=null!==this.ot(e);return t||_("WatchChangeAggregator","Detected inactive target",e),t}ot(e){let t=this.ze.get(e);return t&&t.Ne?null:this.Ge.At(e)}it(e){this.ze.set(e,new rv),this.Ge.getRemoteKeysForTarget(e).forEach(t=>{this.et(e,t,null)})}Et(e,t){return this.Ge.getRemoteKeysForTarget(e).has(t)}}function rE(){return new ep(H.comparator)}function rT(){return new ep(H.comparator)}let rI={asc:"ASCENDING",desc:"DESCENDING"},rS={"<":"LESS_THAN","<=":"LESS_THAN_OR_EQUAL",">":"GREATER_THAN",">=":"GREATER_THAN_OR_EQUAL","==":"EQUAL","!=":"NOT_EQUAL","array-contains":"ARRAY_CONTAINS",in:"IN","not-in":"NOT_IN","array-contains-any":"ARRAY_CONTAINS_ANY"},rC={and:"AND",or:"OR"};class rN{constructor(e,t){this.databaseId=e,this.useProto3Json=t}}function rA(e,t){return e.useProto3Json||null==t?t:{value:t}}function rb(e,t){return e.useProto3Json?`${new Date(1e3*t.seconds).toISOString().replace(/\.\d*/,"").replace("Z","")}.${("000000000"+t.nanoseconds).slice(-9)}Z`:{seconds:""+t.seconds,nanos:t.nanoseconds}}function rD(e,t){return e.useProto3Json?t.toBase64():t.toUint8Array()}function rk(e){return N(!!e,49232),es.fromTimestamp(function(e){let t=eC(e);return new en(t.seconds,t.nanos)}(e))}function rx(e,t){return rR(e,t).canonicalString()}function rR(e,t){let r=new G(["projects",e.projectId,"databases",e.database]).child("documents");return void 0===t?r:r.child(t)}function rV(e){let t=G.fromString(e);return N(rB(t),10190,{key:t.toString()}),t}function rL(e,t){return rx(e.databaseId,t.path)}function rO(e,t){let r=rV(t);if(r.get(1)!==e.databaseId.projectId)throw new b(A.INVALID_ARGUMENT,"Tried to deserialize key from different project: "+r.get(1)+" vs "+e.databaseId.projectId);if(r.get(3)!==e.databaseId.database)throw new b(A.INVALID_ARGUMENT,"Tried to deserialize key from different database: "+r.get(3)+" vs "+e.databaseId.database);return new H(rP(r))}function rM(e,t){return rx(e.databaseId,t)}function rF(e){return new G(["projects",e.databaseId.projectId,"databases",e.databaseId.database]).canonicalString()}function rP(e){return N(e.length>4&&"documents"===e.get(4),29091,{key:e.toString()}),e.popFirst(5)}function rU(e,t,r){return{name:rL(e,t),fields:r.value.mapValue.fields}}function rq(e){return{fieldPath:e.canonicalString()}}function r$(e){return Q.fromServerFormat(e.fieldPath)}function rB(e){return e.length>=4&&"projects"===e.get(0)&&"databases"===e.get(2)}class rz{constructor(e,t,r,n,s=es.min(),i=es.min(),a=eI.EMPTY_BYTE_STRING,o=null){this.target=e,this.targetId=t,this.purpose=r,this.sequenceNumber=n,this.snapshotVersion=s,this.lastLimboFreeSnapshotVersion=i,this.resumeToken=a,this.expectedCount=o}withSequenceNumber(e){return new rz(this.target,this.targetId,this.purpose,e,this.snapshotVersion,this.lastLimboFreeSnapshotVersion,this.resumeToken,this.expectedCount)}withResumeToken(e,t){return new rz(this.target,this.targetId,this.purpose,this.sequenceNumber,t,this.lastLimboFreeSnapshotVersion,e,null)}withExpectedCount(e){return new rz(this.target,this.targetId,this.purpose,this.sequenceNumber,this.snapshotVersion,this.lastLimboFreeSnapshotVersion,this.resumeToken,e)}withLastLimboFreeSnapshotVersion(e){return new rz(this.target,this.targetId,this.purpose,this.sequenceNumber,this.snapshotVersion,e,this.resumeToken,this.expectedCount)}}class rj{constructor(e){this.yt=e}}class rG{constructor(){}Dt(e,t){this.Ct(e,t),t.vt()}Ct(e,t){if("nullValue"in e)this.Ft(t,5);else if("booleanValue"in e)this.Ft(t,10),t.Mt(+!!e.booleanValue);else if("integerValue"in e)this.Ft(t,15),t.Mt(eN(e.integerValue));else if("doubleValue"in e){let r=eN(e.doubleValue);isNaN(r)?this.Ft(t,13):(this.Ft(t,15),ed(r)?t.Mt(0):t.Mt(r))}else if("timestampValue"in e){let r=e.timestampValue;this.Ft(t,20),"string"==typeof r&&(r=eC(r)),t.xt(`${r.seconds||""}`),t.Mt(r.nanos||0)}else if("stringValue"in e)this.Ot(e.stringValue,t),this.Nt(t);else if("bytesValue"in e)this.Ft(t,30),t.Bt(eA(e.bytesValue)),this.Nt(t);else if("referenceValue"in e)this.Lt(e.referenceValue,t);else if("geoPointValue"in e){let r=e.geoPointValue;this.Ft(t,45),t.Mt(r.latitude||0),t.Mt(r.longitude||0)}else"mapValue"in e?e4(e)?this.Ft(t,Number.MAX_SAFE_INTEGER):e2(e)?this.kt(e.mapValue,t):(this.qt(e.mapValue,t),this.Nt(t)):"arrayValue"in e?(this.Qt(e.arrayValue,t),this.Nt(t)):S(19022,{$t:e})}Ot(e,t){this.Ft(t,25),this.Ut(e,t)}Ut(e,t){t.xt(e)}qt(e,t){let r=e.fields||{};for(let e of(this.Ft(t,55),Object.keys(r)))this.Ot(e,t),this.Ct(r[e],t)}kt(e,t){let r=e.fields||{};this.Ft(t,53);let n=r[eB].arrayValue?.values?.length||0;this.Ft(t,15),t.Mt(eN(n)),this.Ot(eB,t),this.Ct(r[eB],t)}Qt(e,t){let r=e.values||[];for(let e of(this.Ft(t,50),r))this.Ct(e,t)}Lt(e,t){this.Ft(t,37),H.fromName(e).path.forEach(e=>{this.Ft(t,60),this.Ut(e,t)})}Ft(e,t){e.Mt(t)}Nt(e){e.Mt(2)}}rG.Kt=new rG;class rK{constructor(){this.Cn=new rQ}addToCollectionParentIndex(e,t){return this.Cn.add(t),eu.resolve()}getCollectionParents(e,t){return eu.resolve(this.Cn.getEntries(t))}addFieldIndex(e,t){return eu.resolve()}deleteFieldIndex(e,t){return eu.resolve()}deleteAllFieldIndexes(e){return eu.resolve()}createTargetIndexes(e,t){return eu.resolve()}getDocumentsMatchingTarget(e,t){return eu.resolve(null)}getIndexType(e,t){return eu.resolve(0)}getFieldIndexes(e,t){return eu.resolve([])}getNextCollectionGroupToUpdate(e){return eu.resolve(null)}getMinOffset(e,t){return eu.resolve(ea.min())}getMinOffsetFromCollectionGroup(e,t){return eu.resolve(ea.min())}updateCollectionGroup(e,t,r){return eu.resolve()}updateIndexEntries(e,t){return eu.resolve()}}class rQ{constructor(){this.index={}}add(e){let t=e.lastSegment(),r=e.popLast(),n=this.index[t]||new ev(G.comparator),s=!n.has(r);return this.index[t]=n.add(r),s}has(e){let t=e.lastSegment(),r=e.popLast(),n=this.index[t];return n&&n.has(r)}getEntries(e){return(this.index[e]||new ev(G.comparator)).toArray()}}new Uint8Array(0);let rH={didRun:!1,sequenceNumbersCollected:0,targetsRemoved:0,documentsRemoved:0};class rW{static withCacheSize(e){return new rW(e,rW.DEFAULT_COLLECTION_PERCENTILE,rW.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT)}constructor(e,t,r){this.cacheSizeCollectionThreshold=e,this.percentileToCollect=t,this.maximumSequenceNumbersToCollect=r}}rW.DEFAULT_COLLECTION_PERCENTILE=10,rW.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT=1e3,rW.DEFAULT=new rW(0x2800000,rW.DEFAULT_COLLECTION_PERCENTILE,rW.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT),rW.DISABLED=new rW(-1,0,0);class rY{constructor(e){this.ar=e}next(){return this.ar+=2,this.ar}static ur(){return new rY(0)}static cr(){return new rY(-1)}}let rX="LruGarbageCollector";function rJ([e,t],[r,n]){let s=U(e,r);return 0===s?U(t,n):s}class rZ{constructor(e){this.Ir=e,this.buffer=new ev(rJ),this.Er=0}dr(){return++this.Er}Ar(e){let t=[e,this.dr()];if(this.buffer.size<this.Ir)this.buffer=this.buffer.add(t);else{let e=this.buffer.last();0>rJ(t,e)&&(this.buffer=this.buffer.delete(e).add(t))}}get maxValue(){return this.buffer.last()[0]}}class r0{constructor(e,t,r){this.garbageCollector=e,this.asyncQueue=t,this.localStore=r,this.Rr=null}start(){-1!==this.garbageCollector.params.cacheSizeCollectionThreshold&&this.Vr(6e4)}stop(){this.Rr&&(this.Rr.cancel(),this.Rr=null)}get started(){return null!==this.Rr}Vr(e){_(rX,`Garbage collection scheduled in ${e}ms`),this.Rr=this.asyncQueue.enqueueAfterDelay("lru_garbage_collection",e,async()=>{this.Rr=null;try{await this.localStore.collectGarbage(this.garbageCollector)}catch(e){eh(e)?_(rX,"Ignoring IndexedDB error during garbage collection: ",e):await el(e)}await this.Vr(3e5)})}}class r1{constructor(e,t){this.mr=e,this.params=t}calculateTargetCount(e,t){return this.mr.gr(e).next(e=>Math.floor(t/100*e))}nthSequenceNumber(e,t){if(0===t)return eu.resolve(ec.ce);let r=new rZ(t);return this.mr.forEachTarget(e,e=>r.Ar(e.sequenceNumber)).next(()=>this.mr.pr(e,e=>r.Ar(e))).next(()=>r.maxValue)}removeTargets(e,t,r){return this.mr.removeTargets(e,t,r)}removeOrphanedDocuments(e,t){return this.mr.removeOrphanedDocuments(e,t)}collect(e,t){return -1===this.params.cacheSizeCollectionThreshold?(_("LruGarbageCollector","Garbage collection skipped; disabled"),eu.resolve(rH)):this.getCacheSize(e).next(r=>r<this.params.cacheSizeCollectionThreshold?(_("LruGarbageCollector",`Garbage collection skipped; Cache size ${r} is lower than threshold ${this.params.cacheSizeCollectionThreshold}`),rH):this.yr(e,t))}getCacheSize(e){return this.mr.getCacheSize(e)}yr(e,t){let r,n,s,i,a,o,l,h=Date.now();return this.calculateTargetCount(e,this.params.percentileToCollect).next(t=>(t>this.params.maximumSequenceNumbersToCollect?(_("LruGarbageCollector",`Capping sequence numbers to collect down to the maximum of ${this.params.maximumSequenceNumbersToCollect} from ${t}`),n=this.params.maximumSequenceNumbersToCollect):n=t,i=Date.now(),this.nthSequenceNumber(e,n))).next(n=>(r=n,a=Date.now(),this.removeTargets(e,r,t))).next(t=>(s=t,o=Date.now(),this.removeOrphanedDocuments(e,r))).next(e=>(l=Date.now(),v()<=u.$b.DEBUG&&_("LruGarbageCollector",`LRU Garbage Collection
	Counted targets in ${i-h}ms
	Determined least recently used ${n} in `+(a-i)+"ms\n"+`	Removed ${s} targets in `+(o-a)+"ms\n"+`	Removed ${e} documents in `+(l-o)+"ms\n"+`Total Duration: ${l-h}ms`),eu.resolve({didRun:!0,sequenceNumbersCollected:n,targetsRemoved:s,documentsRemoved:e})))}}class r2{constructor(){this.changes=new tx(e=>e.toString(),(e,t)=>e.isEqual(t)),this.changesApplied=!1}addEntry(e){this.assertNotApplied(),this.changes.set(e.key,e)}removeEntry(e,t){this.assertNotApplied(),this.changes.set(e,e5.newInvalidDocument(e).setReadTime(t))}getEntry(e,t){this.assertNotApplied();let r=this.changes.get(t);return void 0!==r?eu.resolve(r):this.getFromCache(e,t)}getEntries(e,t){return this.getAllFromCache(e,t)}apply(e){return this.assertNotApplied(),this.changesApplied=!0,this.applyChanges(e)}assertNotApplied(){}}class r3{constructor(e,t){this.overlayedDocument=e,this.mutatedFields=t}}class r4{constructor(e,t,r,n){this.remoteDocumentCache=e,this.mutationQueue=t,this.documentOverlayCache=r,this.indexManager=n}getDocument(e,t){let r=null;return this.documentOverlayCache.getOverlay(e,t).next(n=>(r=n,this.remoteDocumentCache.getEntry(e,t))).next(e=>(null!==r&&t6(r.mutation,e,eE.empty(),en.now()),e))}getDocuments(e,t){return this.remoteDocumentCache.getEntries(e,t).next(t=>this.getLocalViewOfDocuments(e,t,tU()).next(()=>t))}getLocalViewOfDocuments(e,t,r=tU()){let n=tM();return this.populateOverlays(e,n,t).next(()=>this.computeViews(e,t,n,r).next(e=>{let t=tL();return e.forEach((e,r)=>{t=t.insert(e,r.overlayedDocument)}),t}))}getOverlayedDocuments(e,t){let r=tM();return this.populateOverlays(e,r,t).next(()=>this.computeViews(e,t,r,tU()))}populateOverlays(e,t,r){let n=[];return r.forEach(e=>{t.has(e)||n.push(e)}),this.documentOverlayCache.getOverlays(e,n).next(e=>{e.forEach((e,r)=>{t.set(e,r)})})}computeViews(e,t,r,n){let s=tR,i=tM(),a=tM();return t.forEach((e,t)=>{let a=r.get(t.key);n.has(t.key)&&(void 0===a||a.mutation instanceof t9)?s=s.insert(t.key,t):void 0!==a?(i.set(t.key,a.mutation.getFieldMask()),t6(a.mutation,t,a.mutation.getFieldMask(),en.now())):i.set(t.key,eE.empty())}),this.recalculateAndSaveOverlays(e,s).next(e=>(e.forEach((e,t)=>i.set(e,t)),t.forEach((e,t)=>a.set(e,new r3(t,i.get(e)??null))),a))}recalculateAndSaveOverlays(e,t){let r=tM(),n=new ep((e,t)=>e-t),s=tU();return this.mutationQueue.getAllMutationBatchesAffectingDocumentKeys(e,t).next(e=>{for(let s of e)s.keys().forEach(e=>{let i=t.get(e);if(null===i)return;let a=r.get(e)||eE.empty();a=s.applyToLocalView(i,a),r.set(e,a);let o=(n.get(s.batchId)||tU()).add(e);n=n.insert(s.batchId,o)})}).next(()=>{let i=[],a=n.getReverseIterator();for(;a.hasNext();){let n=a.getNext(),o=n.key,l=n.value,u=tM();l.forEach(e=>{if(!s.has(e)){let n=t4(t.get(e),r.get(e));null!==n&&u.set(e,n),s=s.add(e)}}),i.push(this.documentOverlayCache.saveOverlays(e,o,u))}return eu.waitFor(i)}).next(()=>r)}recalculateAndSaveOverlaysForDocumentKeys(e,t){return this.remoteDocumentCache.getEntries(e,t).next(t=>this.recalculateAndSaveOverlays(e,t))}getDocumentsMatchingQuery(e,t,r,n){return H.isDocumentKey(t.path)&&null===t.collectionGroup&&0===t.filters.length?this.getDocumentsMatchingDocumentQuery(e,t.path):tE(t)?this.getDocumentsMatchingCollectionGroupQuery(e,t,r,n):this.getDocumentsMatchingCollectionQuery(e,t,r,n)}getNextDocuments(e,t,r,n){return this.remoteDocumentCache.getAllFromCollectionGroup(e,t,r,n).next(s=>{let i=n-s.size>0?this.documentOverlayCache.getOverlaysForCollectionGroup(e,t,r.largestBatchId,n-s.size):eu.resolve(tM()),a=-1,o=s;return i.next(t=>eu.forEach(t,(t,r)=>(a<r.largestBatchId&&(a=r.largestBatchId),s.get(t)?eu.resolve():this.remoteDocumentCache.getEntry(e,t).next(e=>{o=o.insert(t,e)}))).next(()=>this.populateOverlays(e,t,s)).next(()=>this.computeViews(e,o,t,tU())).next(e=>({batchId:a,changes:tO(e)})))})}getDocumentsMatchingDocumentQuery(e,t){return this.getDocument(e,new H(t)).next(e=>{let t=tL();return e.isFoundDocument()&&(t=t.insert(e.key,e)),t})}getDocumentsMatchingCollectionGroupQuery(e,t,r,n){let s=t.collectionGroup,i=tL();return this.indexManager.getCollectionParents(e,s).next(a=>eu.forEach(a,a=>{let o=new tv(a.child(s),null,t.explicitOrderBy.slice(),t.filters.slice(),t.limit,t.limitType,t.startAt,t.endAt);return this.getDocumentsMatchingCollectionQuery(e,o,r,n).next(e=>{e.forEach((e,t)=>{i=i.insert(e,t)})})}).next(()=>i))}getDocumentsMatchingCollectionQuery(e,t,r,n){let s;return this.documentOverlayCache.getOverlaysForCollection(e,t.path,r.largestBatchId).next(i=>(s=i,this.remoteDocumentCache.getDocumentsMatchingQuery(e,t,r,s,n))).next(e=>{s.forEach((t,r)=>{let n=r.getKey();null===e.get(n)&&(e=e.insert(n,e5.newInvalidDocument(n)))});let r=tL();return e.forEach((e,n)=>{let i=s.get(e);void 0!==i&&t6(i.mutation,n,eE.empty(),en.now()),tD(t,n)&&(r=r.insert(e,n))}),r})}}class r6{constructor(e){this.serializer=e,this.Lr=new Map,this.kr=new Map}getBundleMetadata(e,t){return eu.resolve(this.Lr.get(t))}saveBundleMetadata(e,t){return this.Lr.set(t.id,{id:t.id,version:t.version,createTime:rk(t.createTime)}),eu.resolve()}getNamedQuery(e,t){return eu.resolve(this.kr.get(t))}saveNamedQuery(e,t){return this.kr.set(t.name,{name:t.name,query:function(e){let t=function(e){var t;let r,n=function(e){let t=rV(e);return 4===t.length?G.emptyPath():rP(t)}(e.parent),s=e.structuredQuery,i=s.from?s.from.length:0,a=null;if(i>0){N(1===i,65062);let e=s.from[0];e.allDescendants?a=e.collectionId:n=n.child(e.collectionId)}let o=[];s.where&&(o=function(e){var t;let r=function e(t){return void 0!==t.unaryFilter?function(e){switch(e.unaryFilter.op){case"IS_NAN":let t=r$(e.unaryFilter.field);return tr.create(t,"==",{doubleValue:NaN});case"IS_NULL":let r=r$(e.unaryFilter.field);return tr.create(r,"==",{nullValue:"NULL_VALUE"});case"IS_NOT_NAN":let n=r$(e.unaryFilter.field);return tr.create(n,"!=",{doubleValue:NaN});case"IS_NOT_NULL":let s=r$(e.unaryFilter.field);return tr.create(s,"!=",{nullValue:"NULL_VALUE"});case"OPERATOR_UNSPECIFIED":return S(61313);default:return S(60726)}}(t):void 0!==t.fieldFilter?tr.create(r$(t.fieldFilter.field),function(e){switch(e){case"EQUAL":return"==";case"NOT_EQUAL":return"!=";case"GREATER_THAN":return">";case"GREATER_THAN_OR_EQUAL":return">=";case"LESS_THAN":return"<";case"LESS_THAN_OR_EQUAL":return"<=";case"ARRAY_CONTAINS":return"array-contains";case"IN":return"in";case"NOT_IN":return"not-in";case"ARRAY_CONTAINS_ANY":return"array-contains-any";case"OPERATOR_UNSPECIFIED":return S(58110);default:return S(50506)}}(t.fieldFilter.op),t.fieldFilter.value):void 0!==t.compositeFilter?tn.create(t.compositeFilter.filters.map(t=>e(t)),function(e){switch(e){case"AND":return"and";case"OR":return"or";default:return S(1026)}}(t.compositeFilter.op)):S(30097,{filter:t})}(e);return r instanceof tn&&ti(t=r)&&ts(t)?r.getFilters():[r]}(s.where));let l=[];s.orderBy&&(l=s.orderBy.map(e=>new te(r$(e.field),function(e){switch(e){case"ASCENDING":return"asc";case"DESCENDING":return"desc";default:return}}(e.direction))));let u=null;s.limit&&(u=null==(r="object"==typeof(t=s.limit)?t.value:t)?null:r);let h=null;s.startAt&&(h=function(e){let t=!!e.before;return new e8(e.values||[],t)}(s.startAt));let c=null;return s.endAt&&(c=function(e){let t=!e.before;return new e8(e.values||[],t)}(s.endAt)),new tv(n,a,l,o,u,"F",h,c)}({parent:e.parent,structuredQuery:e.structuredQuery});return"LAST"===e.limitType?tC(t,t.limit,"L"):t}(t.bundledQuery),readTime:rk(t.readTime)}),eu.resolve()}}class r5{constructor(){this.overlays=new ep(H.comparator),this.qr=new Map}getOverlay(e,t){return eu.resolve(this.overlays.get(t))}getOverlays(e,t){let r=tM();return eu.forEach(t,t=>this.getOverlay(e,t).next(e=>{null!==e&&r.set(t,e)})).next(()=>r)}saveOverlays(e,t,r){return r.forEach((r,n)=>{this.St(e,t,n)}),eu.resolve()}removeOverlaysForBatchId(e,t,r){let n=this.qr.get(r);return void 0!==n&&(n.forEach(e=>this.overlays=this.overlays.remove(e)),this.qr.delete(r)),eu.resolve()}getOverlaysForCollection(e,t,r){let n=tM(),s=t.length+1,i=new H(t.child("")),a=this.overlays.getIteratorFrom(i);for(;a.hasNext();){let e=a.getNext().value,i=e.getKey();if(!t.isPrefixOf(i.path))break;i.path.length===s&&e.largestBatchId>r&&n.set(e.getKey(),e)}return eu.resolve(n)}getOverlaysForCollectionGroup(e,t,r,n){let s=new ep((e,t)=>e-t),i=this.overlays.getIterator();for(;i.hasNext();){let e=i.getNext().value;if(e.getKey().getCollectionGroup()===t&&e.largestBatchId>r){let t=s.get(e.largestBatchId);null===t&&(t=tM(),s=s.insert(e.largestBatchId,t)),t.set(e.getKey(),e)}}let a=tM(),o=s.getIterator();for(;o.hasNext()&&(o.getNext().value.forEach((e,t)=>a.set(e,t)),!(a.size()>=n)););return eu.resolve(a)}St(e,t,r){let n=this.overlays.get(r.key);if(null!==n){let e=this.qr.get(n.largestBatchId).delete(r.key);this.qr.set(n.largestBatchId,e)}this.overlays=this.overlays.insert(r.key,new ra(t,r));let s=this.qr.get(t);void 0===s&&(s=tU(),this.qr.set(t,s)),this.qr.set(t,s.add(r.key))}}class r8{constructor(){this.sessionToken=eI.EMPTY_BYTE_STRING}getSessionToken(e){return eu.resolve(this.sessionToken)}setSessionToken(e,t){return this.sessionToken=t,eu.resolve()}}class r9{constructor(){this.Qr=new ev(r7.$r),this.Ur=new ev(r7.Kr)}isEmpty(){return this.Qr.isEmpty()}addReference(e,t){let r=new r7(e,t);this.Qr=this.Qr.add(r),this.Ur=this.Ur.add(r)}Wr(e,t){e.forEach(e=>this.addReference(e,t))}removeReference(e,t){this.Gr(new r7(e,t))}zr(e,t){e.forEach(e=>this.removeReference(e,t))}jr(e){let t=new H(new G([])),r=new r7(t,e),n=new r7(t,e+1),s=[];return this.Ur.forEachInRange([r,n],e=>{this.Gr(e),s.push(e.key)}),s}Jr(){this.Qr.forEach(e=>this.Gr(e))}Gr(e){this.Qr=this.Qr.delete(e),this.Ur=this.Ur.delete(e)}Hr(e){let t=new H(new G([])),r=new r7(t,e),n=new r7(t,e+1),s=tU();return this.Ur.forEachInRange([r,n],e=>{s=s.add(e.key)}),s}containsKey(e){let t=new r7(e,0),r=this.Qr.firstAfterOrEqual(t);return null!==r&&e.isEqual(r.key)}}class r7{constructor(e,t){this.key=e,this.Yr=t}static $r(e,t){return H.comparator(e.key,t.key)||U(e.Yr,t.Yr)}static Kr(e,t){return U(e.Yr,t.Yr)||H.comparator(e.key,t.key)}}class ne{constructor(e,t){this.indexManager=e,this.referenceDelegate=t,this.mutationQueue=[],this.tr=1,this.Zr=new ev(r7.$r)}checkEmpty(e){return eu.resolve(0===this.mutationQueue.length)}addMutationBatch(e,t,r,n){let s=this.tr;this.tr++,this.mutationQueue.length>0&&this.mutationQueue[this.mutationQueue.length-1];let i=new rs(s,t,r,n);for(let t of(this.mutationQueue.push(i),n))this.Zr=this.Zr.add(new r7(t.key,s)),this.indexManager.addToCollectionParentIndex(e,t.key.path.popLast());return eu.resolve(i)}lookupMutationBatch(e,t){return eu.resolve(this.Xr(t))}getNextMutationBatchAfterBatchId(e,t){let r=this.ei(t+1),n=r<0?0:r;return eu.resolve(this.mutationQueue.length>n?this.mutationQueue[n]:null)}getHighestUnacknowledgedBatchId(){return eu.resolve(0===this.mutationQueue.length?-1:this.tr-1)}getAllMutationBatches(e){return eu.resolve(this.mutationQueue.slice())}getAllMutationBatchesAffectingDocumentKey(e,t){let r=new r7(t,0),n=new r7(t,1/0),s=[];return this.Zr.forEachInRange([r,n],e=>{let t=this.Xr(e.Yr);s.push(t)}),eu.resolve(s)}getAllMutationBatchesAffectingDocumentKeys(e,t){let r=new ev(U);return t.forEach(e=>{let t=new r7(e,0),n=new r7(e,1/0);this.Zr.forEachInRange([t,n],e=>{r=r.add(e.Yr)})}),eu.resolve(this.ti(r))}getAllMutationBatchesAffectingQuery(e,t){let r=t.path,n=r.length+1,s=r;H.isDocumentKey(s)||(s=s.child(""));let i=new r7(new H(s),0),a=new ev(U);return this.Zr.forEachWhile(e=>{let t=e.key.path;return!!r.isPrefixOf(t)&&(t.length===n&&(a=a.add(e.Yr)),!0)},i),eu.resolve(this.ti(a))}ti(e){let t=[];return e.forEach(e=>{let r=this.Xr(e);null!==r&&t.push(r)}),t}removeMutationBatch(e,t){N(0===this.ni(t.batchId,"removed"),55003),this.mutationQueue.shift();let r=this.Zr;return eu.forEach(t.mutations,n=>{let s=new r7(n.key,t.batchId);return r=r.delete(s),this.referenceDelegate.markPotentiallyOrphaned(e,n.key)}).next(()=>{this.Zr=r})}ir(e){}containsKey(e,t){let r=new r7(t,0),n=this.Zr.firstAfterOrEqual(r);return eu.resolve(t.isEqual(n&&n.key))}performConsistencyCheck(e){return this.mutationQueue.length,eu.resolve()}ni(e,t){return this.ei(e)}ei(e){return 0===this.mutationQueue.length?0:e-this.mutationQueue[0].batchId}Xr(e){let t=this.ei(e);return t<0||t>=this.mutationQueue.length?null:this.mutationQueue[t]}}class nt{constructor(e){this.ri=e,this.docs=new ep(H.comparator),this.size=0}setIndexManager(e){this.indexManager=e}addEntry(e,t){let r=t.key,n=this.docs.get(r),s=n?n.size:0,i=this.ri(t);return this.docs=this.docs.insert(r,{document:t.mutableCopy(),size:i}),this.size+=i-s,this.indexManager.addToCollectionParentIndex(e,r.path.popLast())}removeEntry(e){let t=this.docs.get(e);t&&(this.docs=this.docs.remove(e),this.size-=t.size)}getEntry(e,t){let r=this.docs.get(t);return eu.resolve(r?r.document.mutableCopy():e5.newInvalidDocument(t))}getEntries(e,t){let r=tR;return t.forEach(e=>{let t=this.docs.get(e);r=r.insert(e,t?t.document.mutableCopy():e5.newInvalidDocument(e))}),eu.resolve(r)}getDocumentsMatchingQuery(e,t,r,n){let s=tR,i=t.path,a=new H(i.child("__id-9223372036854775808__")),o=this.docs.getIteratorFrom(a);for(;o.hasNext();){let{key:e,value:{document:a}}=o.getNext();if(!i.isPrefixOf(e.path))break;e.path.length>i.length+1||0>=function(e,t){let r=e.readTime.compareTo(t.readTime);return 0!==r||0!==(r=H.comparator(e.documentKey,t.documentKey))?r:U(e.largestBatchId,t.largestBatchId)}(new ea(a.readTime,a.key,-1),r)||(n.has(a.key)||tD(t,a))&&(s=s.insert(a.key,a.mutableCopy()))}return eu.resolve(s)}getAllFromCollectionGroup(e,t,r,n){S(9500)}ii(e,t){return eu.forEach(this.docs,e=>t(e))}newChangeBuffer(e){return new nr(this)}getSize(e){return eu.resolve(this.size)}}class nr extends r2{constructor(e){super(),this.Nr=e}applyChanges(e){let t=[];return this.changes.forEach((r,n)=>{n.isValidDocument()?t.push(this.Nr.addEntry(e,n)):this.Nr.removeEntry(r)}),eu.waitFor(t)}getFromCache(e,t){return this.Nr.getEntry(e,t)}getAllFromCache(e,t){return this.Nr.getEntries(e,t)}}class nn{constructor(e){this.persistence=e,this.si=new tx(e=>tp(e),ty),this.lastRemoteSnapshotVersion=es.min(),this.highestTargetId=0,this.oi=0,this._i=new r9,this.targetCount=0,this.ai=rY.ur()}forEachTarget(e,t){return this.si.forEach((e,r)=>t(r)),eu.resolve()}getLastRemoteSnapshotVersion(e){return eu.resolve(this.lastRemoteSnapshotVersion)}getHighestSequenceNumber(e){return eu.resolve(this.oi)}allocateTargetId(e){return this.highestTargetId=this.ai.next(),eu.resolve(this.highestTargetId)}setTargetsMetadata(e,t,r){return r&&(this.lastRemoteSnapshotVersion=r),t>this.oi&&(this.oi=t),eu.resolve()}Pr(e){this.si.set(e.target,e);let t=e.targetId;t>this.highestTargetId&&(this.ai=new rY(t),this.highestTargetId=t),e.sequenceNumber>this.oi&&(this.oi=e.sequenceNumber)}addTargetData(e,t){return this.Pr(t),this.targetCount+=1,eu.resolve()}updateTargetData(e,t){return this.Pr(t),eu.resolve()}removeTargetData(e,t){return this.si.delete(t.target),this._i.jr(t.targetId),this.targetCount-=1,eu.resolve()}removeTargets(e,t,r){let n=0,s=[];return this.si.forEach((i,a)=>{a.sequenceNumber<=t&&null===r.get(a.targetId)&&(this.si.delete(i),s.push(this.removeMatchingKeysForTargetId(e,a.targetId)),n++)}),eu.waitFor(s).next(()=>n)}getTargetCount(e){return eu.resolve(this.targetCount)}getTargetData(e,t){let r=this.si.get(t)||null;return eu.resolve(r)}addMatchingKeys(e,t,r){return this._i.Wr(t,r),eu.resolve()}removeMatchingKeys(e,t,r){this._i.zr(t,r);let n=this.persistence.referenceDelegate,s=[];return n&&t.forEach(t=>{s.push(n.markPotentiallyOrphaned(e,t))}),eu.waitFor(s)}removeMatchingKeysForTargetId(e,t){return this._i.jr(t),eu.resolve()}getMatchingKeysForTargetId(e,t){let r=this._i.Hr(t);return eu.resolve(r)}containsKey(e,t){return eu.resolve(this._i.containsKey(t))}}class ns{constructor(e,t){this.ui={},this.overlays={},this.ci=new ec(0),this.li=!1,this.li=!0,this.hi=new r8,this.referenceDelegate=e(this),this.Pi=new nn(this),this.indexManager=new rK,this.remoteDocumentCache=new nt(e=>this.referenceDelegate.Ti(e)),this.serializer=new rj(t),this.Ii=new r6(this.serializer)}start(){return Promise.resolve()}shutdown(){return this.li=!1,Promise.resolve()}get started(){return this.li}setDatabaseDeletedListener(){}setNetworkEnabled(){}getIndexManager(e){return this.indexManager}getDocumentOverlayCache(e){let t=this.overlays[e.toKey()];return t||(t=new r5,this.overlays[e.toKey()]=t),t}getMutationQueue(e,t){let r=this.ui[e.toKey()];return r||(r=new ne(t,this.referenceDelegate),this.ui[e.toKey()]=r),r}getGlobalsCache(){return this.hi}getTargetCache(){return this.Pi}getRemoteDocumentCache(){return this.remoteDocumentCache}getBundleCache(){return this.Ii}runTransaction(e,t,r){_("MemoryPersistence","Starting transaction:",e);let n=new ni(this.ci.next());return this.referenceDelegate.Ei(),r(n).next(e=>this.referenceDelegate.di(n).next(()=>e)).toPromise().then(e=>(n.raiseOnCommittedEvent(),e))}Ai(e,t){return eu.or(Object.values(this.ui).map(r=>()=>r.containsKey(e,t)))}}class ni extends eo{constructor(e){super(),this.currentSequenceNumber=e}}class na{constructor(e){this.persistence=e,this.Ri=new r9,this.Vi=null}static mi(e){return new na(e)}get fi(){if(this.Vi)return this.Vi;throw S(60996)}addReference(e,t,r){return this.Ri.addReference(r,t),this.fi.delete(r.toString()),eu.resolve()}removeReference(e,t,r){return this.Ri.removeReference(r,t),this.fi.add(r.toString()),eu.resolve()}markPotentiallyOrphaned(e,t){return this.fi.add(t.toString()),eu.resolve()}removeTarget(e,t){this.Ri.jr(t.targetId).forEach(e=>this.fi.add(e.toString()));let r=this.persistence.getTargetCache();return r.getMatchingKeysForTargetId(e,t.targetId).next(e=>{e.forEach(e=>this.fi.add(e.toString()))}).next(()=>r.removeTargetData(e,t))}Ei(){this.Vi=new Set}di(e){let t=this.persistence.getRemoteDocumentCache().newChangeBuffer();return eu.forEach(this.fi,r=>{let n=H.fromPath(r);return this.gi(e,n).next(e=>{e||t.removeEntry(n,es.min())})}).next(()=>(this.Vi=null,t.apply(e)))}updateLimboDocument(e,t){return this.gi(e,t).next(e=>{e?this.fi.delete(t.toString()):this.fi.add(t.toString())})}Ti(e){return 0}gi(e,t){return eu.or([()=>eu.resolve(this.Ri.containsKey(t)),()=>this.persistence.getTargetCache().containsKey(e,t),()=>this.persistence.Ai(e,t)])}}class no{constructor(e,t){this.persistence=e,this.pi=new tx(e=>(function(e){let t="";for(let r=0;r<e.length;r++)t.length>0&&(t+="\x01\x01"),t=function(e,t){let r=t,n=e.length;for(let t=0;t<n;t++){let n=e.charAt(t);switch(n){case"\0":r+="\x01\x10";break;case"\x01":r+="\x01\x11";break;default:r+=n}}return r}(e.get(r),t);return t+"\x01\x01"})(e.path),(e,t)=>e.isEqual(t)),this.garbageCollector=new r1(this,t)}static mi(e,t){return new no(e,t)}Ei(){}di(e){return eu.resolve()}forEachTarget(e,t){return this.persistence.getTargetCache().forEachTarget(e,t)}gr(e){let t=this.wr(e);return this.persistence.getTargetCache().getTargetCount(e).next(e=>t.next(t=>e+t))}wr(e){let t=0;return this.pr(e,e=>{t++}).next(()=>t)}pr(e,t){return eu.forEach(this.pi,(r,n)=>this.br(e,r,n).next(e=>e?eu.resolve():t(n)))}removeTargets(e,t,r){return this.persistence.getTargetCache().removeTargets(e,t,r)}removeOrphanedDocuments(e,t){let r=0,n=this.persistence.getRemoteDocumentCache(),s=n.newChangeBuffer();return n.ii(e,n=>this.br(e,n,t).next(e=>{e||(r++,s.removeEntry(n,es.min()))})).next(()=>s.apply(e)).next(()=>r)}markPotentiallyOrphaned(e,t){return this.pi.set(t,e.currentSequenceNumber),eu.resolve()}removeTarget(e,t){let r=t.withSequenceNumber(e.currentSequenceNumber);return this.persistence.getTargetCache().updateTargetData(e,r)}addReference(e,t,r){return this.pi.set(r,e.currentSequenceNumber),eu.resolve()}removeReference(e,t,r){return this.pi.set(r,e.currentSequenceNumber),eu.resolve()}updateLimboDocument(e,t){return this.pi.set(t,e.currentSequenceNumber),eu.resolve()}Ti(e){let t=e.key.toString().length;return e.isFoundDocument()&&(t+=function e(t){switch(ez(t)){case 0:case 1:return 4;case 2:return 8;case 3:case 8:return 16;case 4:let r=eV(t);return r?16+e(r):16;case 5:return 2*t.stringValue.length;case 6:return eA(t.bytesValue).approximateByteSize();case 7:return t.referenceValue.length;case 9:return(t.arrayValue.values||[]).reduce((t,r)=>t+e(r),0);case 10:case 11:var n;let s;return n=t.mapValue,s=0,em(n.fields,(t,r)=>{s+=t.length+e(r)}),s;default:throw S(13486,{value:t})}}(e.data.value)),t}br(e,t,r){return eu.or([()=>this.persistence.Ai(e,t),()=>this.persistence.getTargetCache().containsKey(e,t),()=>{let e=this.pi.get(t);return eu.resolve(void 0!==e&&e>r)}])}getCacheSize(e){return this.persistence.getRemoteDocumentCache().getSize(e)}}class nl{constructor(e,t,r,n){this.targetId=e,this.fromCache=t,this.Es=r,this.ds=n}static As(e,t){let r=tU(),n=tU();for(let e of t.docChanges)switch(e.type){case 0:r=r.add(e.doc.key);break;case 1:n=n.add(e.doc.key)}return new nl(e,t.fromCache,r,n)}}class nu{constructor(){this._documentReadCount=0}get documentReadCount(){return this._documentReadCount}incrementDocumentReadCount(e){this._documentReadCount+=e}}class nh{constructor(){this.Rs=!1,this.Vs=!1,this.fs=100,this.gs=(0,h.nr)()?8:function(e){let t=e.match(/Android ([\d.]+)/i);return Number(t?t[1].split(".").slice(0,2).join("."):"-1")}((0,h.ZQ)())>0?6:4}initialize(e,t){this.ps=e,this.indexManager=t,this.Rs=!0}getDocumentsMatchingQuery(e,t,r,n){let s={result:null};return this.ys(e,t).next(e=>{s.result=e}).next(()=>{if(!s.result)return this.ws(e,t,n,r).next(e=>{s.result=e})}).next(()=>{if(s.result)return;let r=new nu;return this.Ss(e,t,r).next(n=>{if(s.result=n,this.Vs)return this.bs(e,t,r,n.size)})}).next(()=>s.result)}bs(e,t,r,n){return r.documentReadCount<this.fs?(v()<=u.$b.DEBUG&&_("QueryEngine","SDK will not create cache indexes for query:",tb(t),"since it only creates cache indexes for collection contains","more than or equal to",this.fs,"documents"),eu.resolve()):(v()<=u.$b.DEBUG&&_("QueryEngine","Query:",tb(t),"scans",r.documentReadCount,"local documents and returns",n,"documents as results."),r.documentReadCount>this.gs*n?(v()<=u.$b.DEBUG&&_("QueryEngine","The SDK decides to create cache indexes for query:",tb(t),"as using cache indexes may help improve performance."),this.indexManager.createTargetIndexes(e,tI(t))):eu.resolve())}ys(e,t){if(t_(t))return eu.resolve(null);let r=tI(t);return this.indexManager.getIndexType(e,r).next(n=>0===n?null:(null!==t.limit&&1===n&&(r=tI(t=tC(t,null,"F"))),this.indexManager.getDocumentsMatchingTarget(e,r).next(n=>{let s=tU(...n);return this.ps.getDocuments(e,s).next(n=>this.indexManager.getMinOffset(e,r).next(r=>{let i=this.Ds(t,n);return this.Cs(t,i,s,r.readTime)?this.ys(e,tC(t,null,"F")):this.vs(e,i,t,r)}))})))}ws(e,t,r,n){return t_(t)||n.isEqual(es.min())?eu.resolve(null):this.ps.getDocuments(e,r).next(s=>{let i=this.Ds(t,s);return this.Cs(t,i,r,n)?eu.resolve(null):(v()<=u.$b.DEBUG&&_("QueryEngine","Re-using previous result from %s to execute query: %s",n.toString(),tb(t)),this.vs(e,i,t,function(e,t){let r=e.toTimestamp().seconds,n=e.toTimestamp().nanoseconds+1;return new ea(es.fromTimestamp(1e9===n?new en(r+1,0):new en(r,n)),H.empty(),-1)}(n,0)).next(e=>e))})}Ds(e,t){let r=new ev(tk(e));return t.forEach((t,n)=>{tD(e,n)&&(r=r.add(n))}),r}Cs(e,t,r,n){if(null===e.limit)return!1;if(r.size!==t.size)return!0;let s="F"===e.limitType?t.last():t.first();return!!s&&(s.hasPendingWrites||s.version.compareTo(n)>0)}Ss(e,t,r){return v()<=u.$b.DEBUG&&_("QueryEngine","Using full collection scan to execute query:",tb(t)),this.ps.getDocumentsMatchingQuery(e,t,ea.min(),r)}vs(e,t,r,n){return this.ps.getDocumentsMatchingQuery(e,r,n).next(e=>(t.forEach(t=>{e=e.insert(t.key,t)}),e))}}let nc="LocalStore";class nd{constructor(e,t,r,n){this.persistence=e,this.Fs=t,this.serializer=n,this.Ms=new ep(U),this.xs=new tx(e=>tp(e),ty),this.Os=new Map,this.Ns=e.getRemoteDocumentCache(),this.Pi=e.getTargetCache(),this.Ii=e.getBundleCache(),this.Bs(r)}Bs(e){this.documentOverlayCache=this.persistence.getDocumentOverlayCache(e),this.indexManager=this.persistence.getIndexManager(e),this.mutationQueue=this.persistence.getMutationQueue(e,this.indexManager),this.localDocuments=new r4(this.Ns,this.mutationQueue,this.documentOverlayCache,this.indexManager),this.Ns.setIndexManager(this.indexManager),this.Fs.initialize(this.localDocuments,this.indexManager)}collectGarbage(e){return this.persistence.runTransaction("Collect garbage","readwrite-primary",t=>e.collect(t,this.Ms))}}async function nf(e,t){return await e.persistence.runTransaction("Handle user change","readonly",r=>{let n;return e.mutationQueue.getAllMutationBatches(r).next(s=>(n=s,e.Bs(t),e.mutationQueue.getAllMutationBatches(r))).next(t=>{let s=[],i=[],a=tU();for(let e of n)for(let t of(s.push(e.batchId),e.mutations))a=a.add(t.key);for(let e of t)for(let t of(i.push(e.batchId),e.mutations))a=a.add(t.key);return e.localDocuments.getDocuments(r,a).next(e=>({Ls:e,removedBatchIds:s,addedBatchIds:i}))})})}function nm(e){return e.persistence.runTransaction("Get last remote snapshot version","readonly",t=>e.Pi.getLastRemoteSnapshotVersion(t))}async function ng(e,t,r){let n=e.Ms.get(t);try{r||await e.persistence.runTransaction("Release target",r?"readwrite":"readwrite-primary",t=>e.persistence.referenceDelegate.removeTarget(t,n))}catch(e){if(!eh(e))throw e;_(nc,`Failed to update sequence numbers for target ${t}: ${e}`)}e.Ms=e.Ms.remove(t),e.xs.delete(n.target)}function np(e,t,r){let n=es.min(),s=tU();return e.persistence.runTransaction("Execute query","readwrite",i=>(function(e,t,r){let n=e.xs.get(r);return void 0!==n?eu.resolve(e.Ms.get(n)):e.Pi.getTargetData(t,r)})(e,i,tI(t)).next(t=>{if(t)return n=t.lastLimboFreeSnapshotVersion,e.Pi.getMatchingKeysForTargetId(i,t.targetId).next(e=>{s=e})}).next(()=>e.Fs.getDocumentsMatchingQuery(i,t,r?n:es.min(),r?s:tU())).next(r=>{var n,i,a;let o;return n=e,i=t.collectionGroup||(t.path.length%2==1?t.path.lastSegment():t.path.get(t.path.length-2)),a=r,o=n.Os.get(i)||es.min(),a.forEach((e,t)=>{t.readTime.compareTo(o)>0&&(o=t.readTime)}),n.Os.set(i,o),{documents:r,Qs:s}}))}class ny{constructor(){this.activeTargetIds=tq}zs(e){this.activeTargetIds=this.activeTargetIds.add(e)}js(e){this.activeTargetIds=this.activeTargetIds.delete(e)}Gs(){return JSON.stringify({activeTargetIds:this.activeTargetIds.toArray(),updateTimeMs:Date.now()})}}class nw{constructor(){this.Mo=new ny,this.xo={},this.onlineStateHandler=null,this.sequenceNumberHandler=null}addPendingMutation(e){}updateMutationState(e,t,r){}addLocalQueryTarget(e,t=!0){return t&&this.Mo.zs(e),this.xo[e]||"not-current"}updateQueryState(e,t,r){this.xo[e]=t}removeLocalQueryTarget(e){this.Mo.js(e)}isLocalQueryTarget(e){return this.Mo.activeTargetIds.has(e)}clearQueryState(e){delete this.xo[e]}getAllActiveQueryTargets(){return this.Mo.activeTargetIds}isActiveQueryTarget(e){return this.Mo.activeTargetIds.has(e)}start(){return this.Mo=new ny,Promise.resolve()}handleUserChange(e,t,r){}setOnlineState(e){}shutdown(){}writeSequenceNumber(e){}notifyBundleLoaded(e){}}class nv{Oo(e){}shutdown(){}}let n_="ConnectivityMonitor";class nE{constructor(){this.No=()=>this.Bo(),this.Lo=()=>this.ko(),this.qo=[],this.Qo()}Oo(e){this.qo.push(e)}shutdown(){window.removeEventListener("online",this.No),window.removeEventListener("offline",this.Lo)}Qo(){window.addEventListener("online",this.No),window.addEventListener("offline",this.Lo)}Bo(){for(let e of(_(n_,"Network connectivity changed: AVAILABLE"),this.qo))e(0)}ko(){for(let e of(_(n_,"Network connectivity changed: UNAVAILABLE"),this.qo))e(1)}static v(){return"undefined"!=typeof window&&void 0!==window.addEventListener&&void 0!==window.removeEventListener}}let nT=null;function nI(){return null===nT?nT=0x10000000+Math.round(0x80000000*Math.random()):nT++,"0x"+nT.toString(16)}let nS="RestConnection",nC={BatchGetDocuments:"batchGet",Commit:"commit",RunQuery:"runQuery",RunAggregationQuery:"runAggregationQuery"};class nN{get $o(){return!1}constructor(e){this.databaseInfo=e,this.databaseId=e.databaseId;let t=e.ssl?"https":"http",r=encodeURIComponent(this.databaseId.projectId),n=encodeURIComponent(this.databaseId.database);this.Uo=t+"://"+e.host,this.Ko=`projects/${r}/databases/${n}`,this.Wo=this.databaseId.database===eM?`project_id=${r}`:`project_id=${r}&database_id=${n}`}Go(e,t,r,n,s){let i=nI(),a=this.zo(e,t.toUriEncodedString());_(nS,`Sending RPC '${e}' ${i}:`,a,r);let o={"google-cloud-resource-prefix":this.Ko,"x-goog-request-params":this.Wo};this.jo(o,n,s);let{host:l}=new URL(a),u=(0,h.zJ)(l);return this.Jo(e,a,o,r,u).then(t=>(_(nS,`Received RPC '${e}' ${i}: `,t),t),t=>{throw T(nS,`RPC '${e}' ${i} failed with error: `,t,"url: ",a,"request:",r),t})}Ho(e,t,r,n,s,i){return this.Go(e,t,r,n,s)}jo(e,t,r){e["X-Goog-Api-Client"]="gl-js/ fire/"+y,e["Content-Type"]="text/plain",this.databaseInfo.appId&&(e["X-Firebase-GMPID"]=this.databaseInfo.appId),t&&t.headers.forEach((t,r)=>e[r]=t),r&&r.headers.forEach((t,r)=>e[r]=t)}zo(e,t){let r=nC[e];return`${this.Uo}/v1/${t}:${r}`}terminate(){}}class nA{constructor(e){this.Yo=e.Yo,this.Zo=e.Zo}Xo(e){this.e_=e}t_(e){this.n_=e}r_(e){this.i_=e}onMessage(e){this.s_=e}close(){this.Zo()}send(e){this.Yo(e)}o_(){this.e_()}__(){this.n_()}a_(e){this.i_(e)}u_(e){this.s_(e)}}let nb="WebChannelConnection";class nD extends nN{constructor(e){super(e),this.c_=[],this.forceLongPolling=e.forceLongPolling,this.autoDetectLongPolling=e.autoDetectLongPolling,this.useFetchStreams=e.useFetchStreams,this.longPollingOptions=e.longPollingOptions}Jo(e,t,r,n,s){let i=nI();return new Promise((s,a)=>{let o=new d.ZS;o.setWithCredentials(!0),o.listenOnce(d.Bx.COMPLETE,()=>{try{switch(o.getLastErrorCode()){case d.O4.NO_ERROR:let t=o.getResponseJson();_(nb,`XHR for RPC '${e}' ${i} received:`,JSON.stringify(t)),s(t);break;case d.O4.TIMEOUT:_(nb,`RPC '${e}' ${i} timed out`),a(new b(A.DEADLINE_EXCEEDED,"Request time out"));break;case d.O4.HTTP_ERROR:let r=o.getStatus();if(_(nb,`RPC '${e}' ${i} failed with status:`,r,"response text:",o.getResponseText()),r>0){let e=o.getResponseJson();Array.isArray(e)&&(e=e[0]);let t=e?.error;if(t&&t.status&&t.message){let e=function(e){let t=e.toLowerCase().replace(/_/g,"-");return Object.values(A).indexOf(t)>=0?t:A.UNKNOWN}(t.status);a(new b(e,t.message))}else a(new b(A.UNKNOWN,"Server responded with status "+o.getStatus()))}else a(new b(A.UNAVAILABLE,"Connection failed."));break;default:S(9055,{l_:e,streamId:i,h_:o.getLastErrorCode(),P_:o.getLastError()})}}finally{_(nb,`RPC '${e}' ${i} completed.`)}});let l=JSON.stringify(n);_(nb,`RPC '${e}' ${i} sending request:`,n),o.send(t,"POST",l,r,15)})}T_(e,t,r){let s=nI(),i=[this.Uo,"/","google.firestore.v1.Firestore","/",e,"/channel"],a=(0,d.fF)(),o=(0,d.Ao)(),l={httpSessionIdParam:"gsessionid",initMessageHeaders:{},messageUrlParams:{database:`projects/${this.databaseId.projectId}/databases/${this.databaseId.database}`},sendRawJson:!0,supportsCrossDomainXhr:!0,internalChannelParams:{forwardChannelRequestTimeoutMs:6e5},forceLongPolling:this.forceLongPolling,detectBufferingProxy:this.autoDetectLongPolling},u=this.longPollingOptions.timeoutSeconds;void 0!==u&&(l.longPollingTimeout=Math.round(1e3*u)),this.useFetchStreams&&(l.useFetchStreams=!0),this.jo(l.initMessageHeaders,t,r),l.encodeInitMessageHeaders=!0;let h=i.join("");_(nb,`Creating RPC '${e}' stream ${s}: ${h}`,l);let c=a.createWebChannel(h,l);this.I_(c);let f=!1,m=!1,g=new nA({Yo:t=>{m?_(nb,`Not sending because RPC '${e}' stream ${s} is closed:`,t):(f||(_(nb,`Opening RPC '${e}' stream ${s} transport.`),c.open(),f=!0),_(nb,`RPC '${e}' stream ${s} sending:`,t),c.send(t))},Zo:()=>c.close()}),p=(e,t,r)=>{e.listen(t,e=>{try{r(e)}catch(e){setTimeout(()=>{throw e},0)}})};return p(c,d.iO.EventType.OPEN,()=>{m||(_(nb,`RPC '${e}' stream ${s} transport opened.`),g.o_())}),p(c,d.iO.EventType.CLOSE,()=>{m||(m=!0,_(nb,`RPC '${e}' stream ${s} transport closed`),g.a_(),this.E_(c))}),p(c,d.iO.EventType.ERROR,t=>{m||(m=!0,T(nb,`RPC '${e}' stream ${s} transport errored. Name:`,t.name,"Message:",t.message),g.a_(new b(A.UNAVAILABLE,"The operation could not be completed")))}),p(c,d.iO.EventType.MESSAGE,t=>{if(!m){let r=t.data[0];N(!!r,16349);let i=r?.error||r[0]?.error;if(i){_(nb,`RPC '${e}' stream ${s} received error:`,i);let t=i.status,r=function(e){let t=n[e];if(void 0!==t)return rl(t)}(t),a=i.message;void 0===r&&(r=A.INTERNAL,a="Unknown error status: "+t+" with message "+i.message),m=!0,g.a_(new b(r,a)),c.close()}else _(nb,`RPC '${e}' stream ${s} received:`,r),g.u_(r)}}),p(o,d.Jh.STAT_EVENT,t=>{t.stat===d.ro.PROXY?_(nb,`RPC '${e}' stream ${s} detected buffering proxy`):t.stat===d.ro.NOPROXY&&_(nb,`RPC '${e}' stream ${s} detected no buffering proxy`)}),setTimeout(()=>{g.__()},0),g}terminate(){this.c_.forEach(e=>e.close()),this.c_=[]}I_(e){this.c_.push(e)}E_(e){this.c_=this.c_.filter(t=>t===e)}}function nk(){return"undefined"!=typeof document?document:null}function nx(e){return new rN(e,!0)}class nR{constructor(e,t,r=1e3,n=1.5,s=6e4){this.Mi=e,this.timerId=t,this.d_=r,this.A_=n,this.R_=s,this.V_=0,this.m_=null,this.f_=Date.now(),this.reset()}reset(){this.V_=0}g_(){this.V_=this.R_}p_(e){this.cancel();let t=Math.floor(this.V_+this.y_()),r=Math.max(0,Date.now()-this.f_),n=Math.max(0,t-r);n>0&&_("ExponentialBackoff",`Backing off for ${n} ms (base delay: ${this.V_} ms, delay with jitter: ${t} ms, last attempt: ${r} ms ago)`),this.m_=this.Mi.enqueueAfterDelay(this.timerId,n,()=>(this.f_=Date.now(),e())),this.V_*=this.A_,this.V_<this.d_&&(this.V_=this.d_),this.V_>this.R_&&(this.V_=this.R_)}w_(){null!==this.m_&&(this.m_.skipDelay(),this.m_=null)}cancel(){null!==this.m_&&(this.m_.cancel(),this.m_=null)}y_(){return(Math.random()-.5)*this.V_}}let nV="PersistentStream";class nL{constructor(e,t,r,n,s,i,a,o){this.Mi=e,this.S_=r,this.b_=n,this.connection=s,this.authCredentialsProvider=i,this.appCheckCredentialsProvider=a,this.listener=o,this.state=0,this.D_=0,this.C_=null,this.v_=null,this.stream=null,this.F_=0,this.M_=new nR(e,t)}x_(){return 1===this.state||5===this.state||this.O_()}O_(){return 2===this.state||3===this.state}start(){this.F_=0,4!==this.state?this.auth():this.N_()}async stop(){this.x_()&&await this.close(0)}B_(){this.state=0,this.M_.reset()}L_(){this.O_()&&null===this.C_&&(this.C_=this.Mi.enqueueAfterDelay(this.S_,6e4,()=>this.k_()))}q_(e){this.Q_(),this.stream.send(e)}async k_(){if(this.O_())return this.close(0)}Q_(){this.C_&&(this.C_.cancel(),this.C_=null)}U_(){this.v_&&(this.v_.cancel(),this.v_=null)}async close(e,t){this.Q_(),this.U_(),this.M_.cancel(),this.D_++,4!==e?this.M_.reset():t&&t.code===A.RESOURCE_EXHAUSTED?(E(t.toString()),E("Using maximum backoff delay to prevent overloading the backend."),this.M_.g_()):t&&t.code===A.UNAUTHENTICATED&&3!==this.state&&(this.authCredentialsProvider.invalidateToken(),this.appCheckCredentialsProvider.invalidateToken()),null!==this.stream&&(this.K_(),this.stream.close(),this.stream=null),this.state=e,await this.listener.r_(t)}K_(){}auth(){this.state=1;let e=this.W_(this.D_),t=this.D_;Promise.all([this.authCredentialsProvider.getToken(),this.appCheckCredentialsProvider.getToken()]).then(([e,r])=>{this.D_===t&&this.G_(e,r)},t=>{e(()=>{let e=new b(A.UNKNOWN,"Fetching auth token failed: "+t.message);return this.z_(e)})})}G_(e,t){let r=this.W_(this.D_);this.stream=this.j_(e,t),this.stream.Xo(()=>{r(()=>this.listener.Xo())}),this.stream.t_(()=>{r(()=>(this.state=2,this.v_=this.Mi.enqueueAfterDelay(this.b_,1e4,()=>(this.O_()&&(this.state=3),Promise.resolve())),this.listener.t_()))}),this.stream.r_(e=>{r(()=>this.z_(e))}),this.stream.onMessage(e=>{r(()=>1==++this.F_?this.J_(e):this.onNext(e))})}N_(){this.state=5,this.M_.p_(async()=>{this.state=0,this.start()})}z_(e){return _(nV,`close with error: ${e}`),this.stream=null,this.close(4,e)}W_(e){return t=>{this.Mi.enqueueAndForget(()=>this.D_===e?t():(_(nV,"stream callback skipped by getCloseGuardedDispatcher."),Promise.resolve()))}}}class nO extends nL{constructor(e,t,r,n,s,i){super(e,"listen_stream_connection_backoff","listen_stream_idle","health_check_timeout",t,r,n,i),this.serializer=s}j_(e,t){return this.connection.T_("Listen",e,t)}J_(e){return this.onNext(e)}onNext(e){this.M_.reset();let t=function(e,t){let r;if("targetChange"in t){var n,s;t.targetChange;let i="NO_CHANGE"===(n=t.targetChange.targetChangeType||"NO_CHANGE")?0:"ADD"===n?1:"REMOVE"===n?2:"CURRENT"===n?3:"RESET"===n?4:S(39313,{state:n}),a=t.targetChange.targetIds||[],o=(s=t.targetChange.resumeToken,e.useProto3Json?(N(void 0===s||"string"==typeof s,58123),eI.fromBase64String(s||"")):(N(void 0===s||s instanceof f||s instanceof Uint8Array,16193),eI.fromUint8Array(s||new Uint8Array))),l=t.targetChange.cause;r=new rw(i,a,o,l&&new b(void 0===l.code?A.UNKNOWN:rl(l.code),l.message||"")||null)}else if("documentChange"in t){t.documentChange;let n=t.documentChange;n.document,n.document.name,n.document.updateTime;let s=rO(e,n.document.name),i=rk(n.document.updateTime),a=n.document.createTime?rk(n.document.createTime):es.min(),o=new e6({mapValue:{fields:n.document.fields}}),l=e5.newFoundDocument(s,i,a,o);r=new rp(n.targetIds||[],n.removedTargetIds||[],l.key,l)}else if("documentDelete"in t){t.documentDelete;let n=t.documentDelete;n.document;let s=rO(e,n.document),i=n.readTime?rk(n.readTime):es.min(),a=e5.newNoDocument(s,i);r=new rp([],n.removedTargetIds||[],a.key,a)}else if("documentRemove"in t){t.documentRemove;let n=t.documentRemove;n.document;let s=rO(e,n.document);r=new rp([],n.removedTargetIds||[],s,null)}else{if(!("filter"in t))return S(11601,{Rt:t});{t.filter;let e=t.filter;e.targetId;let{count:n=0,unchangedNames:s}=e,i=new ro(n,s);r=new ry(e.targetId,i)}}return r}(this.serializer,e),r=function(e){if(!("targetChange"in e))return es.min();let t=e.targetChange;return t.targetIds&&t.targetIds.length?es.min():t.readTime?rk(t.readTime):es.min()}(e);return this.listener.H_(t,r)}Y_(e){let t={};t.database=rF(this.serializer),t.addTarget=function(e,t){let r,n=t.target;if((r=tw(n)?{documents:{documents:[rM(e,n.path)]}}:{query:function(e,t){var r,n;let s,i={structuredQuery:{}},a=t.path;null!==t.collectionGroup?(s=a,i.structuredQuery.from=[{collectionId:t.collectionGroup,allDescendants:!0}]):(s=a.popLast(),i.structuredQuery.from=[{collectionId:a.lastSegment()}]),i.parent=rM(e,s);let o=function(e){if(0!==e.length)return function e(t){return t instanceof tr?function(e){if("=="===e.op){if(e0(e.value))return{unaryFilter:{field:rq(e.field),op:"IS_NAN"}};if(eZ(e.value))return{unaryFilter:{field:rq(e.field),op:"IS_NULL"}}}else if("!="===e.op){if(e0(e.value))return{unaryFilter:{field:rq(e.field),op:"IS_NOT_NAN"}};if(eZ(e.value))return{unaryFilter:{field:rq(e.field),op:"IS_NOT_NULL"}}}return{fieldFilter:{field:rq(e.field),op:rS[e.op],value:e.value}}}(t):t instanceof tn?function(t){let r=t.getFilters().map(t=>e(t));return 1===r.length?r[0]:{compositeFilter:{op:rC[t.op],filters:r}}}(t):S(54877,{filter:t})}(tn.create(e,"and"))}(t.filters);o&&(i.structuredQuery.where=o);let l=function(e){if(0!==e.length)return e.map(e=>({field:rq(e.field),direction:rI[e.dir]}))}(t.orderBy);l&&(i.structuredQuery.orderBy=l);let u=rA(e,t.limit);return null!==u&&(i.structuredQuery.limit=u),t.startAt&&(i.structuredQuery.startAt={before:(r=t.startAt).inclusive,values:r.position}),t.endAt&&(i.structuredQuery.endAt={before:!(n=t.endAt).inclusive,values:n.position}),{ft:i,parent:s}}(e,n).ft}).targetId=t.targetId,t.resumeToken.approximateByteSize()>0){r.resumeToken=rD(e,t.resumeToken);let n=rA(e,t.expectedCount);null!==n&&(r.expectedCount=n)}else if(t.snapshotVersion.compareTo(es.min())>0){r.readTime=rb(e,t.snapshotVersion.toTimestamp());let n=rA(e,t.expectedCount);null!==n&&(r.expectedCount=n)}return r}(this.serializer,e);let r=function(e,t){let r=function(e){switch(e){case"TargetPurposeListen":return null;case"TargetPurposeExistenceFilterMismatch":return"existence-filter-mismatch";case"TargetPurposeExistenceFilterMismatchBloom":return"existence-filter-mismatch-bloom";case"TargetPurposeLimboResolution":return"limbo-document";default:return S(28987,{purpose:e})}}(t.purpose);return null==r?null:{"goog-listen-tags":r}}(this.serializer,e);r&&(t.labels=r),this.q_(t)}Z_(e){let t={};t.database=rF(this.serializer),t.removeTarget=e,this.q_(t)}}class nM extends nL{constructor(e,t,r,n,s,i){super(e,"write_stream_connection_backoff","write_stream_idle","health_check_timeout",t,r,n,i),this.serializer=s}get X_(){return this.F_>0}start(){this.lastStreamToken=void 0,super.start()}K_(){this.X_&&this.ea([])}j_(e,t){return this.connection.T_("Write",e,t)}J_(e){return N(!!e.streamToken,31322),this.lastStreamToken=e.streamToken,N(!e.writeResults||0===e.writeResults.length,55816),this.listener.ta()}onNext(e){var t,r;N(!!e.streamToken,12678),this.lastStreamToken=e.streamToken,this.M_.reset();let n=(t=e.writeResults,r=e.commitTime,t&&t.length>0?(N(void 0!==r,14353),t.map(e=>{let t;return(t=e.updateTime?rk(e.updateTime):rk(r)).isEqual(es.min())&&(t=rk(r)),new t0(t,e.transformResults||[])})):[]),s=rk(e.commitTime);return this.listener.na(s,n)}ra(){let e={};e.database=rF(this.serializer),this.q_(e)}ea(e){let t={streamToken:this.lastStreamToken,writes:e.map(e=>(function(e,t){var r;let n;if(t instanceof t8)n={update:rU(e,t.key,t.value)};else if(t instanceof rr)n={delete:rL(e,t.key)};else if(t instanceof t9)n={update:rU(e,t.key,t.data),updateMask:function(e){let t=[];return e.fields.forEach(e=>t.push(e.canonicalString())),{fieldPaths:t}}(t.fieldMask)};else{if(!(t instanceof rn))return S(16599,{Vt:t.type});n={verify:rL(e,t.key)}}return t.fieldTransforms.length>0&&(n.updateTransforms=t.fieldTransforms.map(e=>(function(e,t){let r=t.transform;if(r instanceof tG)return{fieldPath:t.field.canonicalString(),setToServerValue:"REQUEST_TIME"};if(r instanceof tK)return{fieldPath:t.field.canonicalString(),appendMissingElements:{values:r.elements}};if(r instanceof tH)return{fieldPath:t.field.canonicalString(),removeAllFromArray:{values:r.elements}};if(r instanceof tY)return{fieldPath:t.field.canonicalString(),increment:r.Ae};throw S(20930,{transform:t.transform})})(0,e))),t.precondition.isNone||(n.currentDocument=void 0!==(r=t.precondition).updateTime?{updateTime:rb(e,r.updateTime.toTimestamp())}:void 0!==r.exists?{exists:r.exists}:S(27497)),n})(this.serializer,e))};this.q_(t)}}class nF{}class nP extends nF{constructor(e,t,r,n){super(),this.authCredentials=e,this.appCheckCredentials=t,this.connection=r,this.serializer=n,this.ia=!1}sa(){if(this.ia)throw new b(A.FAILED_PRECONDITION,"The client has already been terminated.")}Go(e,t,r,n){return this.sa(),Promise.all([this.authCredentials.getToken(),this.appCheckCredentials.getToken()]).then(([s,i])=>this.connection.Go(e,rR(t,r),n,s,i)).catch(e=>{throw"FirebaseError"===e.name?(e.code===A.UNAUTHENTICATED&&(this.authCredentials.invalidateToken(),this.appCheckCredentials.invalidateToken()),e):new b(A.UNKNOWN,e.toString())})}Ho(e,t,r,n,s){return this.sa(),Promise.all([this.authCredentials.getToken(),this.appCheckCredentials.getToken()]).then(([i,a])=>this.connection.Ho(e,rR(t,r),n,i,a,s)).catch(e=>{throw"FirebaseError"===e.name?(e.code===A.UNAUTHENTICATED&&(this.authCredentials.invalidateToken(),this.appCheckCredentials.invalidateToken()),e):new b(A.UNKNOWN,e.toString())})}terminate(){this.ia=!0,this.connection.terminate()}}class nU{constructor(e,t){this.asyncQueue=e,this.onlineStateHandler=t,this.state="Unknown",this.oa=0,this._a=null,this.aa=!0}ua(){0===this.oa&&(this.ca("Unknown"),this._a=this.asyncQueue.enqueueAfterDelay("online_state_timeout",1e4,()=>(this._a=null,this.la("Backend didn't respond within 10 seconds."),this.ca("Offline"),Promise.resolve())))}ha(e){"Online"===this.state?this.ca("Unknown"):(this.oa++,this.oa>=1&&(this.Pa(),this.la(`Connection failed 1 times. Most recent error: ${e.toString()}`),this.ca("Offline")))}set(e){this.Pa(),this.oa=0,"Online"===e&&(this.aa=!1),this.ca(e)}ca(e){e!==this.state&&(this.state=e,this.onlineStateHandler(e))}la(e){let t=`Could not reach Cloud Firestore backend. ${e}
This typically indicates that your device does not have a healthy Internet connection at the moment. The client will operate in offline mode until it is able to successfully connect to the backend.`;this.aa?(E(t),this.aa=!1):_("OnlineStateTracker",t)}Pa(){null!==this._a&&(this._a.cancel(),this._a=null)}}let nq="RemoteStore";class n${constructor(e,t,r,n,s){this.localStore=e,this.datastore=t,this.asyncQueue=r,this.remoteSyncer={},this.Ta=[],this.Ia=new Map,this.Ea=new Set,this.da=[],this.Aa=s,this.Aa.Oo(e=>{r.enqueueAndForget(async()=>{nY(this)&&(_(nq,"Restarting streams for network reachability change."),await async function(e){e.Ea.add(4),await nz(e),e.Ra.set("Unknown"),e.Ea.delete(4),await nB(e)}(this))})}),this.Ra=new nU(r,n)}}async function nB(e){if(nY(e))for(let t of e.da)await t(!0)}async function nz(e){for(let t of e.da)await t(!1)}function nj(e,t){e.Ia.has(t.targetId)||(e.Ia.set(t.targetId,t),nW(e)?nH(e):sr(e).O_()&&nK(e,t))}function nG(e,t){let r=sr(e);e.Ia.delete(t),r.O_()&&nQ(e,t),0===e.Ia.size&&(r.O_()?r.L_():nY(e)&&e.Ra.set("Unknown"))}function nK(e,t){if(e.Va.Ue(t.targetId),t.resumeToken.approximateByteSize()>0||t.snapshotVersion.compareTo(es.min())>0){let r=e.remoteSyncer.getRemoteKeysForTarget(t.targetId).size;t=t.withExpectedCount(r)}sr(e).Y_(t)}function nQ(e,t){e.Va.Ue(t),sr(e).Z_(t)}function nH(e){e.Va=new r_({getRemoteKeysForTarget:t=>e.remoteSyncer.getRemoteKeysForTarget(t),At:t=>e.Ia.get(t)||null,ht:()=>e.datastore.serializer.databaseId}),sr(e).start(),e.Ra.ua()}function nW(e){return nY(e)&&!sr(e).x_()&&e.Ia.size>0}function nY(e){return 0===e.Ea.size}async function nX(e){e.Ra.set("Online")}async function nJ(e){e.Ia.forEach((t,r)=>{nK(e,t)})}async function nZ(e,t){e.Va=void 0,nW(e)?(e.Ra.ha(t),nH(e)):e.Ra.set("Unknown")}async function n0(e,t,r){if(e.Ra.set("Online"),t instanceof rw&&2===t.state&&t.cause)try{await async function(e,t){let r=t.cause;for(let n of t.targetIds)e.Ia.has(n)&&(await e.remoteSyncer.rejectListen(n,r),e.Ia.delete(n),e.Va.removeTarget(n))}(e,t)}catch(r){_(nq,"Failed to remove targets %s: %s ",t.targetIds.join(","),r),await n1(e,r)}else if(t instanceof rp?e.Va.Ze(t):t instanceof ry?e.Va.st(t):e.Va.tt(t),!r.isEqual(es.min()))try{let t=await nm(e.localStore);r.compareTo(t)>=0&&await function(e,t){let r=e.Va.Tt(t);return r.targetChanges.forEach((r,n)=>{if(r.resumeToken.approximateByteSize()>0){let s=e.Ia.get(n);s&&e.Ia.set(n,s.withResumeToken(r.resumeToken,t))}}),r.targetMismatches.forEach((t,r)=>{let n=e.Ia.get(t);if(!n)return;e.Ia.set(t,n.withResumeToken(eI.EMPTY_BYTE_STRING,n.snapshotVersion)),nQ(e,t);let s=new rz(n.target,t,r,n.sequenceNumber);nK(e,s)}),e.remoteSyncer.applyRemoteEvent(r)}(e,r)}catch(t){_(nq,"Failed to raise snapshot:",t),await n1(e,t)}}async function n1(e,t,r){if(!eh(t))throw t;e.Ea.add(1),await nz(e),e.Ra.set("Offline"),r||(r=()=>nm(e.localStore)),e.asyncQueue.enqueueRetryable(async()=>{_(nq,"Retrying IndexedDB access"),await r(),e.Ea.delete(1),await nB(e)})}function n2(e,t){return t().catch(r=>n1(e,r,t))}async function n3(e){var t;let r=sn(e),n=e.Ta.length>0?e.Ta[e.Ta.length-1].batchId:-1;for(;nY(t=e)&&t.Ta.length<10;)try{let t=await function(e,t){return e.persistence.runTransaction("Get next mutation batch","readonly",r=>(void 0===t&&(t=-1),e.mutationQueue.getNextMutationBatchAfterBatchId(r,t)))}(e.localStore,n);if(null===t){0===e.Ta.length&&r.L_();break}n=t.batchId,function(e,t){e.Ta.push(t);let r=sn(e);r.O_()&&r.X_&&r.ea(t.mutations)}(e,t)}catch(t){await n1(e,t)}n4(e)&&n6(e)}function n4(e){return nY(e)&&!sn(e).x_()&&e.Ta.length>0}function n6(e){sn(e).start()}async function n5(e){sn(e).ra()}async function n8(e){let t=sn(e);for(let r of e.Ta)t.ea(r.mutations)}async function n9(e,t,r){let n=e.Ta.shift(),s=ri.from(n,t,r);await n2(e,()=>e.remoteSyncer.applySuccessfulWrite(s)),await n3(e)}async function n7(e,t){t&&sn(e).X_&&await async function(e,t){var r;if(function(e){switch(e){case A.OK:return S(64938);case A.CANCELLED:case A.UNKNOWN:case A.DEADLINE_EXCEEDED:case A.RESOURCE_EXHAUSTED:case A.INTERNAL:case A.UNAVAILABLE:case A.UNAUTHENTICATED:return!1;case A.INVALID_ARGUMENT:case A.NOT_FOUND:case A.ALREADY_EXISTS:case A.PERMISSION_DENIED:case A.FAILED_PRECONDITION:case A.ABORTED:case A.OUT_OF_RANGE:case A.UNIMPLEMENTED:case A.DATA_LOSS:return!0;default:return S(15467,{code:e})}}(r=t.code)&&r!==A.ABORTED){let r=e.Ta.shift();sn(e).B_(),await n2(e,()=>e.remoteSyncer.rejectFailedWrite(r.batchId,t)),await n3(e)}}(e,t),n4(e)&&n6(e)}async function se(e,t){e.asyncQueue.verifyOperationInProgress(),_(nq,"RemoteStore received new credentials");let r=nY(e);e.Ea.add(3),await nz(e),r&&e.Ra.set("Unknown"),await e.remoteSyncer.handleCredentialChange(t),e.Ea.delete(3),await nB(e)}async function st(e,t){t?(e.Ea.delete(2),await nB(e)):t||(e.Ea.add(2),await nz(e),e.Ra.set("Unknown"))}function sr(e){var t,r,n;return e.ma||(t=e.datastore,r=e.asyncQueue,n={Xo:nX.bind(null,e),t_:nJ.bind(null,e),r_:nZ.bind(null,e),H_:n0.bind(null,e)},t.sa(),e.ma=new nO(r,t.connection,t.authCredentials,t.appCheckCredentials,t.serializer,n),e.da.push(async t=>{t?(e.ma.B_(),nW(e)?nH(e):e.Ra.set("Unknown")):(await e.ma.stop(),e.Va=void 0)})),e.ma}function sn(e){var t,r,n;return e.fa||(t=e.datastore,r=e.asyncQueue,n={Xo:()=>Promise.resolve(),t_:n5.bind(null,e),r_:n7.bind(null,e),ta:n8.bind(null,e),na:n9.bind(null,e)},t.sa(),e.fa=new nM(r,t.connection,t.authCredentials,t.appCheckCredentials,t.serializer,n),e.da.push(async t=>{t?(e.fa.B_(),await n3(e)):(await e.fa.stop(),e.Ta.length>0&&(_(nq,`Stopping write stream with ${e.Ta.length} pending writes`),e.Ta=[]))})),e.fa}class ss{constructor(e,t,r,n,s){this.asyncQueue=e,this.timerId=t,this.targetTimeMs=r,this.op=n,this.removalCallback=s,this.deferred=new D,this.then=this.deferred.promise.then.bind(this.deferred.promise),this.deferred.promise.catch(e=>{})}get promise(){return this.deferred.promise}static createAndSchedule(e,t,r,n,s){let i=new ss(e,t,Date.now()+r,n,s);return i.start(r),i}start(e){this.timerHandle=setTimeout(()=>this.handleDelayElapsed(),e)}skipDelay(){return this.handleDelayElapsed()}cancel(e){null!==this.timerHandle&&(this.clearTimeout(),this.deferred.reject(new b(A.CANCELLED,"Operation cancelled"+(e?": "+e:""))))}handleDelayElapsed(){this.asyncQueue.enqueueAndForget(()=>null!==this.timerHandle?(this.clearTimeout(),this.op().then(e=>this.deferred.resolve(e))):Promise.resolve())}clearTimeout(){null!==this.timerHandle&&(this.removalCallback(this),clearTimeout(this.timerHandle),this.timerHandle=null)}}function si(e,t){if(E("AsyncQueue",`${t}: ${e}`),eh(e))return new b(A.UNAVAILABLE,`${t}: ${e}`);throw e}class sa{static emptySet(e){return new sa(e.comparator)}constructor(e){this.comparator=e?(t,r)=>e(t,r)||H.comparator(t.key,r.key):(e,t)=>H.comparator(e.key,t.key),this.keyedMap=tL(),this.sortedSet=new ep(this.comparator)}has(e){return null!=this.keyedMap.get(e)}get(e){return this.keyedMap.get(e)}first(){return this.sortedSet.minKey()}last(){return this.sortedSet.maxKey()}isEmpty(){return this.sortedSet.isEmpty()}indexOf(e){let t=this.keyedMap.get(e);return t?this.sortedSet.indexOf(t):-1}get size(){return this.sortedSet.size}forEach(e){this.sortedSet.inorderTraversal((t,r)=>(e(t),!1))}add(e){let t=this.delete(e.key);return t.copy(t.keyedMap.insert(e.key,e),t.sortedSet.insert(e,null))}delete(e){let t=this.get(e);return t?this.copy(this.keyedMap.remove(e),this.sortedSet.remove(t)):this}isEqual(e){if(!(e instanceof sa)||this.size!==e.size)return!1;let t=this.sortedSet.getIterator(),r=e.sortedSet.getIterator();for(;t.hasNext();){let e=t.getNext().key,n=r.getNext().key;if(!e.isEqual(n))return!1}return!0}toString(){let e=[];return this.forEach(t=>{e.push(t.toString())}),0===e.length?"DocumentSet ()":"DocumentSet (\n  "+e.join("  \n")+"\n)"}copy(e,t){let r=new sa;return r.comparator=this.comparator,r.keyedMap=e,r.sortedSet=t,r}}class so{constructor(){this.ga=new ep(H.comparator)}track(e){let t=e.doc.key,r=this.ga.get(t);r?0!==e.type&&3===r.type?this.ga=this.ga.insert(t,e):3===e.type&&1!==r.type?this.ga=this.ga.insert(t,{type:r.type,doc:e.doc}):2===e.type&&2===r.type?this.ga=this.ga.insert(t,{type:2,doc:e.doc}):2===e.type&&0===r.type?this.ga=this.ga.insert(t,{type:0,doc:e.doc}):1===e.type&&0===r.type?this.ga=this.ga.remove(t):1===e.type&&2===r.type?this.ga=this.ga.insert(t,{type:1,doc:r.doc}):0===e.type&&1===r.type?this.ga=this.ga.insert(t,{type:2,doc:e.doc}):S(63341,{Rt:e,pa:r}):this.ga=this.ga.insert(t,e)}ya(){let e=[];return this.ga.inorderTraversal((t,r)=>{e.push(r)}),e}}class sl{constructor(e,t,r,n,s,i,a,o,l){this.query=e,this.docs=t,this.oldDocs=r,this.docChanges=n,this.mutatedKeys=s,this.fromCache=i,this.syncStateChanged=a,this.excludesMetadataChanges=o,this.hasCachedResults=l}static fromInitialDocuments(e,t,r,n,s){let i=[];return t.forEach(e=>{i.push({type:0,doc:e})}),new sl(e,t,sa.emptySet(t),i,r,n,!0,!1,s)}get hasPendingWrites(){return!this.mutatedKeys.isEmpty()}isEqual(e){if(!(this.fromCache===e.fromCache&&this.hasCachedResults===e.hasCachedResults&&this.syncStateChanged===e.syncStateChanged&&this.mutatedKeys.isEqual(e.mutatedKeys)&&tN(this.query,e.query)&&this.docs.isEqual(e.docs)&&this.oldDocs.isEqual(e.oldDocs)))return!1;let t=this.docChanges,r=e.docChanges;if(t.length!==r.length)return!1;for(let e=0;e<t.length;e++)if(t[e].type!==r[e].type||!t[e].doc.isEqual(r[e].doc))return!1;return!0}}class su{constructor(){this.wa=void 0,this.Sa=[]}ba(){return this.Sa.some(e=>e.Da())}}class sh{constructor(){this.queries=sc(),this.onlineState="Unknown",this.Ca=new Set}terminate(){!function(e,t){let r=e.queries;e.queries=sc(),r.forEach((e,r)=>{for(let e of r.Sa)e.onError(t)})}(this,new b(A.ABORTED,"Firestore shutting down"))}}function sc(){return new tx(e=>tA(e),tN)}async function sd(e,t){let r=3,n=t.query,s=e.queries.get(n);s?!s.ba()&&t.Da()&&(r=2):(s=new su,r=+!t.Da());try{switch(r){case 0:s.wa=await e.onListen(n,!0);break;case 1:s.wa=await e.onListen(n,!1);break;case 2:await e.onFirstRemoteStoreListen(n)}}catch(r){let e=si(r,`Initialization of query '${tb(t.query)}' failed`);return void t.onError(e)}e.queries.set(n,s),s.Sa.push(t),t.va(e.onlineState),s.wa&&t.Fa(s.wa)&&sp(e)}async function sf(e,t){let r=t.query,n=3,s=e.queries.get(r);if(s){let e=s.Sa.indexOf(t);e>=0&&(s.Sa.splice(e,1),0===s.Sa.length?n=+!t.Da():!s.ba()&&t.Da()&&(n=2))}switch(n){case 0:return e.queries.delete(r),e.onUnlisten(r,!0);case 1:return e.queries.delete(r),e.onUnlisten(r,!1);case 2:return e.onLastRemoteStoreUnlisten(r);default:return}}function sm(e,t){let r=!1;for(let n of t){let t=n.query,s=e.queries.get(t);if(s){for(let e of s.Sa)e.Fa(n)&&(r=!0);s.wa=n}}r&&sp(e)}function sg(e,t,r){let n=e.queries.get(t);if(n)for(let e of n.Sa)e.onError(r);e.queries.delete(t)}function sp(e){e.Ca.forEach(e=>{e.next()})}(a=i||(i={})).Ma="default",a.Cache="cache";class sy{constructor(e,t,r){this.query=e,this.xa=t,this.Oa=!1,this.Na=null,this.onlineState="Unknown",this.options=r||{}}Fa(e){if(!this.options.includeMetadataChanges){let t=[];for(let r of e.docChanges)3!==r.type&&t.push(r);e=new sl(e.query,e.docs,e.oldDocs,t,e.mutatedKeys,e.fromCache,e.syncStateChanged,!0,e.hasCachedResults)}let t=!1;return this.Oa?this.Ba(e)&&(this.xa.next(e),t=!0):this.La(e,this.onlineState)&&(this.ka(e),t=!0),this.Na=e,t}onError(e){this.xa.error(e)}va(e){this.onlineState=e;let t=!1;return this.Na&&!this.Oa&&this.La(this.Na,e)&&(this.ka(this.Na),t=!0),t}La(e,t){return!(e.fromCache&&this.Da())||(!this.options.qa||"Offline"===t)&&(!e.docs.isEmpty()||e.hasCachedResults||"Offline"===t)}Ba(e){if(e.docChanges.length>0)return!0;let t=this.Na&&this.Na.hasPendingWrites!==e.hasPendingWrites;return!(!e.syncStateChanged&&!t)&&!0===this.options.includeMetadataChanges}ka(e){e=sl.fromInitialDocuments(e.query,e.docs,e.mutatedKeys,e.fromCache,e.hasCachedResults),this.Oa=!0,this.xa.next(e)}Da(){return this.options.source!==i.Cache}}class sw{constructor(e){this.key=e}}class sv{constructor(e){this.key=e}}class s_{constructor(e,t){this.query=e,this.Ya=t,this.Za=null,this.hasCachedResults=!1,this.current=!1,this.Xa=tU(),this.mutatedKeys=tU(),this.eu=tk(e),this.tu=new sa(this.eu)}get nu(){return this.Ya}ru(e,t){let r=t?t.iu:new so,n=t?t.tu:this.tu,s=t?t.mutatedKeys:this.mutatedKeys,i=n,a=!1,o="F"===this.query.limitType&&n.size===this.query.limit?n.last():null,l="L"===this.query.limitType&&n.size===this.query.limit?n.first():null;if(e.inorderTraversal((e,t)=>{let u=n.get(e),h=tD(this.query,t)?t:null,c=!!u&&this.mutatedKeys.has(u.key),d=!!h&&(h.hasLocalMutations||this.mutatedKeys.has(h.key)&&h.hasCommittedMutations),f=!1;u&&h?u.data.isEqual(h.data)?c!==d&&(r.track({type:3,doc:h}),f=!0):this.su(u,h)||(r.track({type:2,doc:h}),f=!0,(o&&this.eu(h,o)>0||l&&0>this.eu(h,l))&&(a=!0)):!u&&h?(r.track({type:0,doc:h}),f=!0):u&&!h&&(r.track({type:1,doc:u}),f=!0,(o||l)&&(a=!0)),f&&(h?(i=i.add(h),s=d?s.add(e):s.delete(e)):(i=i.delete(e),s=s.delete(e)))}),null!==this.query.limit)for(;i.size>this.query.limit;){let e="F"===this.query.limitType?i.last():i.first();i=i.delete(e.key),s=s.delete(e.key),r.track({type:1,doc:e})}return{tu:i,iu:r,Cs:a,mutatedKeys:s}}su(e,t){return e.hasLocalMutations&&t.hasCommittedMutations&&!t.hasLocalMutations}applyChanges(e,t,r,n){let s=this.tu;this.tu=e.tu,this.mutatedKeys=e.mutatedKeys;let i=e.iu.ya();i.sort((e,t)=>(function(e,t){let r=e=>{switch(e){case 0:return 1;case 2:case 3:return 2;case 1:return 0;default:return S(20277,{Rt:e})}};return r(e)-r(t)})(e.type,t.type)||this.eu(e.doc,t.doc)),this.ou(r),n=n??!1;let a=t&&!n?this._u():[],o=0===this.Xa.size&&this.current&&!n?1:0,l=o!==this.Za;return(this.Za=o,0!==i.length||l)?{snapshot:new sl(this.query,e.tu,s,i,e.mutatedKeys,0===o,l,!1,!!r&&r.resumeToken.approximateByteSize()>0),au:a}:{au:a}}va(e){return this.current&&"Offline"===e?(this.current=!1,this.applyChanges({tu:this.tu,iu:new so,mutatedKeys:this.mutatedKeys,Cs:!1},!1)):{au:[]}}uu(e){return!this.Ya.has(e)&&!!this.tu.has(e)&&!this.tu.get(e).hasLocalMutations}ou(e){e&&(e.addedDocuments.forEach(e=>this.Ya=this.Ya.add(e)),e.modifiedDocuments.forEach(e=>{}),e.removedDocuments.forEach(e=>this.Ya=this.Ya.delete(e)),this.current=e.current)}_u(){if(!this.current)return[];let e=this.Xa;this.Xa=tU(),this.tu.forEach(e=>{this.uu(e.key)&&(this.Xa=this.Xa.add(e.key))});let t=[];return e.forEach(e=>{this.Xa.has(e)||t.push(new sv(e))}),this.Xa.forEach(r=>{e.has(r)||t.push(new sw(r))}),t}cu(e){this.Ya=e.Qs,this.Xa=tU();let t=this.ru(e.documents);return this.applyChanges(t,!0)}lu(){return sl.fromInitialDocuments(this.query,this.tu,this.mutatedKeys,0===this.Za,this.hasCachedResults)}}let sE="SyncEngine";class sT{constructor(e,t,r){this.query=e,this.targetId=t,this.view=r}}class sI{constructor(e){this.key=e,this.hu=!1}}class sS{constructor(e,t,r,n,s,i){this.localStore=e,this.remoteStore=t,this.eventManager=r,this.sharedClientState=n,this.currentUser=s,this.maxConcurrentLimboResolutions=i,this.Pu={},this.Tu=new tx(e=>tA(e),tN),this.Iu=new Map,this.Eu=new Set,this.du=new ep(H.comparator),this.Au=new Map,this.Ru=new r9,this.Vu={},this.mu=new Map,this.fu=rY.cr(),this.onlineState="Unknown",this.gu=void 0}get isPrimaryClient(){return!0===this.gu}}async function sC(e,t,r=!0){let n,s=sK(e),i=s.Tu.get(t);return i?(s.sharedClientState.addLocalQueryTarget(i.targetId),n=i.view.lu()):n=await sA(s,t,r,!0),n}async function sN(e,t){let r=sK(e);await sA(r,t,!0,!1)}async function sA(e,t,r,n){var s,i;let a,o=await (s=e.localStore,i=tI(t),s.persistence.runTransaction("Allocate target","readwrite",e=>{let t;return s.Pi.getTargetData(e,i).next(r=>r?(t=r,eu.resolve(t)):s.Pi.allocateTargetId(e).next(r=>(t=new rz(i,r,"TargetPurposeListen",e.currentSequenceNumber),s.Pi.addTargetData(e,t).next(()=>t))))}).then(e=>{let t=s.Ms.get(e.targetId);return(null===t||e.snapshotVersion.compareTo(t.snapshotVersion)>0)&&(s.Ms=s.Ms.insert(e.targetId,e),s.xs.set(i,e.targetId)),e})),l=o.targetId,u=e.sharedClientState.addLocalQueryTarget(l,r);return n&&(a=await sb(e,t,l,"current"===u,o.resumeToken)),e.isPrimaryClient&&r&&nj(e.remoteStore,o),a}async function sb(e,t,r,n,s){e.pu=(t,r,n)=>(async function(e,t,r,n){let s=t.view.ru(r);s.Cs&&(s=await np(e.localStore,t.query,!1).then(({documents:e})=>t.view.ru(e,s)));let i=n&&n.targetChanges.get(t.targetId),a=n&&null!=n.targetMismatches.get(t.targetId),o=t.view.applyChanges(s,e.isPrimaryClient,i,a);return s$(e,t.targetId,o.au),o.snapshot})(e,t,r,n);let i=await np(e.localStore,t,!0),a=new s_(t,i.Qs),o=a.ru(i.documents),l=rg.createSynthesizedTargetChangeForCurrentChange(r,n&&"Offline"!==e.onlineState,s),u=a.applyChanges(o,e.isPrimaryClient,l);s$(e,r,u.au);let h=new sT(t,r,a);return e.Tu.set(t,h),e.Iu.has(r)?e.Iu.get(r).push(t):e.Iu.set(r,[t]),u.snapshot}async function sD(e,t,r){let n=e.Tu.get(t),s=e.Iu.get(n.targetId);if(s.length>1)return e.Iu.set(n.targetId,s.filter(e=>!tN(e,t))),void e.Tu.delete(t);e.isPrimaryClient?(e.sharedClientState.removeLocalQueryTarget(n.targetId),e.sharedClientState.isActiveQueryTarget(n.targetId)||await ng(e.localStore,n.targetId,!1).then(()=>{e.sharedClientState.clearQueryState(n.targetId),r&&nG(e.remoteStore,n.targetId),sU(e,n.targetId)}).catch(el)):(sU(e,n.targetId),await ng(e.localStore,n.targetId,!0))}async function sk(e,t){let r=e.Tu.get(t),n=e.Iu.get(r.targetId);e.isPrimaryClient&&1===n.length&&(e.sharedClientState.removeLocalQueryTarget(r.targetId),nG(e.remoteStore,r.targetId))}async function sx(e,t,r){var n,s;let i=((n=e).remoteStore.remoteSyncer.applySuccessfulWrite=sO.bind(null,n),n.remoteStore.remoteSyncer.rejectFailedWrite=sM.bind(null,n),n);try{let e,n=await function(e,t){let r,n,s=en.now(),i=t.reduce((e,t)=>e.add(t.key),tU());return e.persistence.runTransaction("Locally write mutations","readwrite",a=>{let o=tR,l=tU();return e.Ns.getEntries(a,i).next(e=>{(o=e).forEach((e,t)=>{t.isValidDocument()||(l=l.add(e))})}).next(()=>e.localDocuments.getOverlayedDocuments(a,o)).next(n=>{r=n;let i=[];for(let e of t){let t=function(e,t){let r=null;for(let n of e.fieldTransforms){let e=t.data.field(n.field),s=tj(n.transform,e||null);null!=s&&(null===r&&(r=e6.empty()),r.set(n.field,s))}return r||null}(e,r.get(e.key).overlayedDocument);null!=t&&i.push(new t9(e.key,t,function e(t){let r=[];return em(t.fields,(t,n)=>{let s=new Q([t]);if(e1(n)){let t=e(n.mapValue).fields;if(0===t.length)r.push(s);else for(let e of t)r.push(s.child(e))}else r.push(s)}),new eE(r)}(t.value.mapValue),t1.exists(!0)))}return e.mutationQueue.addMutationBatch(a,s,i,t)}).next(t=>{n=t;let s=t.applyToLocalDocumentSet(r,l);return e.documentOverlayCache.saveOverlays(a,t.batchId,s)})}).then(()=>({batchId:n.batchId,changes:tO(r)}))}(i.localStore,t);i.sharedClientState.addPendingMutation(n.batchId),s=n.batchId,(e=i.Vu[i.currentUser.toKey()])||(e=new ep(U)),e=e.insert(s,r),i.Vu[i.currentUser.toKey()]=e,await sz(i,n.changes),await n3(i.remoteStore)}catch(t){let e=si(t,"Failed to persist write");r.reject(e)}}async function sR(e,t){try{let r=await function(e,t){let r=t.snapshotVersion,n=e.Ms;return e.persistence.runTransaction("Apply remote event","readwrite-primary",s=>{var i,a,o;let l,u,h=e.Ns.newChangeBuffer({trackRemovals:!0});n=e.Ms;let c=[];t.targetChanges.forEach((i,a)=>{var o;let l=n.get(a);if(!l)return;c.push(e.Pi.removeMatchingKeys(s,i.removedDocuments,a).next(()=>e.Pi.addMatchingKeys(s,i.addedDocuments,a)));let u=l.withSequenceNumber(s.currentSequenceNumber);null!==t.targetMismatches.get(a)?u=u.withResumeToken(eI.EMPTY_BYTE_STRING,es.min()).withLastLimboFreeSnapshotVersion(es.min()):i.resumeToken.approximateByteSize()>0&&(u=u.withResumeToken(i.resumeToken,r)),n=n.insert(a,u),o=u,(0===l.resumeToken.approximateByteSize()||o.snapshotVersion.toMicroseconds()-l.snapshotVersion.toMicroseconds()>=3e8||i.addedDocuments.size+i.modifiedDocuments.size+i.removedDocuments.size>0)&&c.push(e.Pi.updateTargetData(s,u))});let d=tR,f=tU();if(t.documentUpdates.forEach(r=>{t.resolvedLimboDocuments.has(r)&&c.push(e.persistence.referenceDelegate.updateLimboDocument(s,r))}),c.push((i=s,a=h,o=t.documentUpdates,l=tU(),u=tU(),o.forEach(e=>l=l.add(e)),a.getEntries(i,l).next(e=>{let t=tR;return o.forEach((r,n)=>{let s=e.get(r);n.isFoundDocument()!==s.isFoundDocument()&&(u=u.add(r)),n.isNoDocument()&&n.version.isEqual(es.min())?(a.removeEntry(r,n.readTime),t=t.insert(r,n)):!s.isValidDocument()||n.version.compareTo(s.version)>0||0===n.version.compareTo(s.version)&&s.hasPendingWrites?(a.addEntry(n),t=t.insert(r,n)):_(nc,"Ignoring outdated watch update for ",r,". Current version:",s.version," Watch version:",n.version)}),{ks:t,qs:u}})).next(e=>{d=e.ks,f=e.qs})),!r.isEqual(es.min())){let t=e.Pi.getLastRemoteSnapshotVersion(s).next(t=>e.Pi.setTargetsMetadata(s,s.currentSequenceNumber,r));c.push(t)}return eu.waitFor(c).next(()=>h.apply(s)).next(()=>e.localDocuments.getLocalViewOfDocuments(s,d,f)).next(()=>d)}).then(t=>(e.Ms=n,t))}(e.localStore,t);t.targetChanges.forEach((t,r)=>{let n=e.Au.get(r);n&&(N(t.addedDocuments.size+t.modifiedDocuments.size+t.removedDocuments.size<=1,22616),t.addedDocuments.size>0?n.hu=!0:t.modifiedDocuments.size>0?N(n.hu,14607):t.removedDocuments.size>0&&(N(n.hu,42227),n.hu=!1))}),await sz(e,r,t)}catch(e){await el(e)}}function sV(e,t,r){var n;if(e.isPrimaryClient&&0===r||!e.isPrimaryClient&&1===r){let r,s=[];e.Tu.forEach((e,r)=>{let n=r.view.va(t);n.snapshot&&s.push(n.snapshot)}),(n=e.eventManager).onlineState=t,r=!1,n.queries.forEach((e,n)=>{for(let e of n.Sa)e.va(t)&&(r=!0)}),r&&sp(n),s.length&&e.Pu.H_(s),e.onlineState=t,e.isPrimaryClient&&e.sharedClientState.setOnlineState(t)}}async function sL(e,t,r){e.sharedClientState.updateQueryState(t,"rejected",r);let n=e.Au.get(t),s=n&&n.key;if(s){let r=new ep(H.comparator);r=r.insert(s,e5.newNoDocument(s,es.min()));let n=tU().add(s),i=new rm(es.min(),new Map,new ep(U),r,n);await sR(e,i),e.du=e.du.remove(s),e.Au.delete(t),sB(e)}else await ng(e.localStore,t,!1).then(()=>sU(e,t,r)).catch(el)}async function sO(e,t){var r;let n=t.batch.batchId;try{let s=await (r=e.localStore,r.persistence.runTransaction("Acknowledge batch","readwrite-primary",e=>{let n=t.batch.keys(),s=r.Ns.newChangeBuffer({trackRemovals:!0});return(function(e,t,r,n){let s=r.batch,i=s.keys(),a=eu.resolve();return i.forEach(e=>{a=a.next(()=>n.getEntry(t,e)).next(t=>{let i=r.docVersions.get(e);N(null!==i,48541),0>t.version.compareTo(i)&&(s.applyToRemoteDocument(t,r),t.isValidDocument()&&(t.setReadTime(r.commitVersion),n.addEntry(t)))})}),a.next(()=>e.mutationQueue.removeMutationBatch(t,s))})(r,e,t,s).next(()=>s.apply(e)).next(()=>r.mutationQueue.performConsistencyCheck(e)).next(()=>r.documentOverlayCache.removeOverlaysForBatchId(e,n,t.batch.batchId)).next(()=>r.localDocuments.recalculateAndSaveOverlaysForDocumentKeys(e,function(e){let t=tU();for(let r=0;r<e.mutationResults.length;++r)e.mutationResults[r].transformResults.length>0&&(t=t.add(e.batch.mutations[r].key));return t}(t))).next(()=>r.localDocuments.getDocuments(e,n))}));sP(e,n,null),sF(e,n),e.sharedClientState.updateMutationState(n,"acknowledged"),await sz(e,s)}catch(e){await el(e)}}async function sM(e,t,r){var n;try{let s=await (n=e.localStore,n.persistence.runTransaction("Reject batch","readwrite-primary",e=>{let r;return n.mutationQueue.lookupMutationBatch(e,t).next(t=>(N(null!==t,37113),r=t.keys(),n.mutationQueue.removeMutationBatch(e,t))).next(()=>n.mutationQueue.performConsistencyCheck(e)).next(()=>n.documentOverlayCache.removeOverlaysForBatchId(e,r,t)).next(()=>n.localDocuments.recalculateAndSaveOverlaysForDocumentKeys(e,r)).next(()=>n.localDocuments.getDocuments(e,r))}));sP(e,t,r),sF(e,t),e.sharedClientState.updateMutationState(t,"rejected",r),await sz(e,s)}catch(e){await el(e)}}function sF(e,t){(e.mu.get(t)||[]).forEach(e=>{e.resolve()}),e.mu.delete(t)}function sP(e,t,r){let n=e.Vu[e.currentUser.toKey()];if(n){let s=n.get(t);s&&(r?s.reject(r):s.resolve(),n=n.remove(t)),e.Vu[e.currentUser.toKey()]=n}}function sU(e,t,r=null){for(let n of(e.sharedClientState.removeLocalQueryTarget(t),e.Iu.get(t)))e.Tu.delete(n),r&&e.Pu.yu(n,r);e.Iu.delete(t),e.isPrimaryClient&&e.Ru.jr(t).forEach(t=>{e.Ru.containsKey(t)||sq(e,t)})}function sq(e,t){e.Eu.delete(t.path.canonicalString());let r=e.du.get(t);null!==r&&(nG(e.remoteStore,r),e.du=e.du.remove(t),e.Au.delete(r),sB(e))}function s$(e,t,r){for(let n of r)n instanceof sw?(e.Ru.addReference(n.key,t),function(e,t){let r=t.key,n=r.path.canonicalString();e.du.get(r)||e.Eu.has(n)||(_(sE,"New document in limbo: "+r),e.Eu.add(n),sB(e))}(e,n)):n instanceof sv?(_(sE,"Document no longer in limbo: "+n.key),e.Ru.removeReference(n.key,t),e.Ru.containsKey(n.key)||sq(e,n.key)):S(19791,{wu:n})}function sB(e){for(;e.Eu.size>0&&e.du.size<e.maxConcurrentLimboResolutions;){let t=e.Eu.values().next().value;e.Eu.delete(t);let r=new H(G.fromString(t)),n=e.fu.next();e.Au.set(n,new sI(r)),e.du=e.du.insert(r,n),nj(e.remoteStore,new rz(tI(new tv(r.path)),n,"TargetPurposeLimboResolution",ec.ce))}}async function sz(e,t,r){let n=[],s=[],i=[];e.Tu.isEmpty()||(e.Tu.forEach((a,o)=>{i.push(e.pu(o,t,r).then(t=>{if((t||r)&&e.isPrimaryClient){let n=t?!t.fromCache:r?.targetChanges.get(o.targetId)?.current;e.sharedClientState.updateQueryState(o.targetId,n?"current":"not-current")}if(t){n.push(t);let e=nl.As(o.targetId,t);s.push(e)}}))}),await Promise.all(i),e.Pu.H_(n),await async function(e,t){try{await e.persistence.runTransaction("notifyLocalViewChanges","readwrite",r=>eu.forEach(t,t=>eu.forEach(t.Es,n=>e.persistence.referenceDelegate.addReference(r,t.targetId,n)).next(()=>eu.forEach(t.ds,n=>e.persistence.referenceDelegate.removeReference(r,t.targetId,n)))))}catch(e){if(!eh(e))throw e;_(nc,"Failed to update sequence numbers: "+e)}for(let r of t){let t=r.targetId;if(!r.fromCache){let r=e.Ms.get(t),n=r.snapshotVersion,s=r.withLastLimboFreeSnapshotVersion(n);e.Ms=e.Ms.insert(t,s)}}}(e.localStore,s))}async function sj(e,t){if(!e.currentUser.isEqual(t)){_(sE,"User change. New user:",t.toKey());let r=await nf(e.localStore,t);e.currentUser=t,e.mu.forEach(e=>{e.forEach(e=>{e.reject(new b(A.CANCELLED,"'waitForPendingWrites' promise is rejected due to a user change."))})}),e.mu.clear(),e.sharedClientState.handleUserChange(t,r.removedBatchIds,r.addedBatchIds),await sz(e,r.Ls)}}function sG(e,t){let r=e.Au.get(t);if(r&&r.hu)return tU().add(r.key);{let r=tU(),n=e.Iu.get(t);if(!n)return r;for(let t of n){let n=e.Tu.get(t);r=r.unionWith(n.view.nu)}return r}}function sK(e){return e.remoteStore.remoteSyncer.applyRemoteEvent=sR.bind(null,e),e.remoteStore.remoteSyncer.getRemoteKeysForTarget=sG.bind(null,e),e.remoteStore.remoteSyncer.rejectListen=sL.bind(null,e),e.Pu.H_=sm.bind(null,e.eventManager),e.Pu.yu=sg.bind(null,e.eventManager),e}class sQ{constructor(){this.kind="memory",this.synchronizeTabs=!1}async initialize(e){this.serializer=nx(e.databaseInfo.databaseId),this.sharedClientState=this.Du(e),this.persistence=this.Cu(e),await this.persistence.start(),this.localStore=this.vu(e),this.gcScheduler=this.Fu(e,this.localStore),this.indexBackfillerScheduler=this.Mu(e,this.localStore)}Fu(e,t){return null}Mu(e,t){return null}vu(e){var t,r;return t=this.persistence,r=new nh,new nd(t,r,e.initialUser,this.serializer)}Cu(e){return new ns(na.mi,this.serializer)}Du(e){return new nw}async terminate(){this.gcScheduler?.stop(),this.indexBackfillerScheduler?.stop(),this.sharedClientState.shutdown(),await this.persistence.shutdown()}}sQ.provider={build:()=>new sQ};class sH extends sQ{constructor(e){super(),this.cacheSizeBytes=e}Fu(e,t){return N(this.persistence.referenceDelegate instanceof no,46915),new r0(this.persistence.referenceDelegate.garbageCollector,e.asyncQueue,t)}Cu(e){let t=void 0!==this.cacheSizeBytes?rW.withCacheSize(this.cacheSizeBytes):rW.DEFAULT;return new ns(e=>no.mi(e,t),this.serializer)}}class sW{async initialize(e,t){this.localStore||(this.localStore=e.localStore,this.sharedClientState=e.sharedClientState,this.datastore=this.createDatastore(t),this.remoteStore=this.createRemoteStore(t),this.eventManager=this.createEventManager(t),this.syncEngine=this.createSyncEngine(t,!e.synchronizeTabs),this.sharedClientState.onlineStateHandler=e=>sV(this.syncEngine,e,1),this.remoteStore.remoteSyncer.handleCredentialChange=sj.bind(null,this.syncEngine),await st(this.remoteStore,this.syncEngine.isPrimaryClient))}createEventManager(e){return new sh}createDatastore(e){var t;let r=nx(e.databaseInfo.databaseId),n=new nD(e.databaseInfo);return t=e.authCredentials,new nP(t,e.appCheckCredentials,n,r)}createRemoteStore(e){var t,r;return t=this.localStore,r=this.datastore,new n$(t,r,e.asyncQueue,e=>sV(this.syncEngine,e,0),nE.v()?new nE:new nv)}createSyncEngine(e,t){return function(e,t,r,n,s,i,a){let o=new sS(e,t,r,n,s,i);return a&&(o.gu=!0),o}(this.localStore,this.remoteStore,this.eventManager,this.sharedClientState,e.initialUser,e.maxConcurrentLimboResolutions,t)}async terminate(){await async function(e){_(nq,"RemoteStore shutting down."),e.Ea.add(5),await nz(e),e.Aa.shutdown(),e.Ra.set("Unknown")}(this.remoteStore),this.datastore?.terminate(),this.eventManager?.terminate()}}sW.provider={build:()=>new sW};class sY{constructor(e){this.observer=e,this.muted=!1}next(e){this.muted||this.observer.next&&this.Ou(this.observer.next,e)}error(e){this.muted||(this.observer.error?this.Ou(this.observer.error,e):E("Uncaught Error in snapshot listener:",e.toString()))}Nu(){this.muted=!0}Ou(e,t){setTimeout(()=>{this.muted||e(t)},0)}}let sX="FirestoreClient";class sJ{constructor(e,t,r,n,s){this.authCredentials=e,this.appCheckCredentials=t,this.asyncQueue=r,this.databaseInfo=n,this.user=p.UNAUTHENTICATED,this.clientId=P.newId(),this.authCredentialListener=()=>Promise.resolve(),this.appCheckCredentialListener=()=>Promise.resolve(),this._uninitializedComponentsProvider=s,this.authCredentials.start(r,async e=>{_(sX,"Received user=",e.uid),await this.authCredentialListener(e),this.user=e}),this.appCheckCredentials.start(r,e=>(_(sX,"Received new app check token=",e),this.appCheckCredentialListener(e,this.user)))}get configuration(){return{asyncQueue:this.asyncQueue,databaseInfo:this.databaseInfo,clientId:this.clientId,authCredentials:this.authCredentials,appCheckCredentials:this.appCheckCredentials,initialUser:this.user,maxConcurrentLimboResolutions:100}}setCredentialChangeListener(e){this.authCredentialListener=e}setAppCheckTokenChangeListener(e){this.appCheckCredentialListener=e}terminate(){this.asyncQueue.enterRestrictedMode();let e=new D;return this.asyncQueue.enqueueAndForgetEvenWhileRestricted(async()=>{try{this._onlineComponents&&await this._onlineComponents.terminate(),this._offlineComponents&&await this._offlineComponents.terminate(),this.authCredentials.shutdown(),this.appCheckCredentials.shutdown(),e.resolve()}catch(r){let t=si(r,"Failed to shutdown persistence");e.reject(t)}}),e.promise}}async function sZ(e,t){e.asyncQueue.verifyOperationInProgress(),_(sX,"Initializing OfflineComponentProvider");let r=e.configuration;await t.initialize(r);let n=r.initialUser;e.setCredentialChangeListener(async e=>{n.isEqual(e)||(await nf(t.localStore,e),n=e)}),t.persistence.setDatabaseDeletedListener(()=>e.terminate()),e._offlineComponents=t}async function s0(e,t){e.asyncQueue.verifyOperationInProgress();let r=await s1(e);_(sX,"Initializing OnlineComponentProvider"),await t.initialize(r,e.configuration),e.setCredentialChangeListener(e=>se(t.remoteStore,e)),e.setAppCheckTokenChangeListener((e,r)=>se(t.remoteStore,r)),e._onlineComponents=t}async function s1(e){if(!e._offlineComponents)if(e._uninitializedComponentsProvider){_(sX,"Using user provided OfflineComponentProvider");try{await sZ(e,e._uninitializedComponentsProvider._offline)}catch(t){if(!("FirebaseError"===t.name?t.code===A.FAILED_PRECONDITION||t.code===A.UNIMPLEMENTED:!("undefined"!=typeof DOMException&&t instanceof DOMException)||22===t.code||20===t.code||11===t.code))throw t;T("Error using user provided cache. Falling back to memory cache: "+t),await sZ(e,new sQ)}}else _(sX,"Using default OfflineComponentProvider"),await sZ(e,new sH(void 0));return e._offlineComponents}async function s2(e){return e._onlineComponents||(e._uninitializedComponentsProvider?(_(sX,"Using user provided OnlineComponentProvider"),await s0(e,e._uninitializedComponentsProvider._online)):(_(sX,"Using default OnlineComponentProvider"),await s0(e,new sW))),e._onlineComponents}async function s3(e){let t=await s2(e),r=t.eventManager;return r.onListen=sC.bind(null,t.syncEngine),r.onUnlisten=sD.bind(null,t.syncEngine),r.onFirstRemoteStoreListen=sN.bind(null,t.syncEngine),r.onLastRemoteStoreUnlisten=sk.bind(null,t.syncEngine),r}function s4(e){let t={};return void 0!==e.timeoutSeconds&&(t.timeoutSeconds=e.timeoutSeconds),t}let s6=new Map,s5="firestore.googleapis.com";class s8{constructor(e){if(void 0===e.host){if(void 0!==e.ssl)throw new b(A.INVALID_ARGUMENT,"Can't provide ssl option if host option is not set");this.host=s5,this.ssl=!0}else this.host=e.host,this.ssl=e.ssl??!0;if(this.isUsingEmulator=void 0!==e.emulatorOptions,this.credentials=e.credentials,this.ignoreUndefinedProperties=!!e.ignoreUndefinedProperties,this.localCache=e.localCache,void 0===e.cacheSizeBytes)this.cacheSizeBytes=0x2800000;else{if(-1!==e.cacheSizeBytes&&e.cacheSizeBytes<1048576)throw new b(A.INVALID_ARGUMENT,"cacheSizeBytes must be at least 1048576");this.cacheSizeBytes=e.cacheSizeBytes}(function(e,t,r,n){if(!0===t&&!0===n)throw new b(A.INVALID_ARGUMENT,`${e} and ${r} cannot be used together.`)})("experimentalForceLongPolling",e.experimentalForceLongPolling,"experimentalAutoDetectLongPolling",e.experimentalAutoDetectLongPolling),this.experimentalForceLongPolling=!!e.experimentalForceLongPolling,this.experimentalForceLongPolling?this.experimentalAutoDetectLongPolling=!1:void 0===e.experimentalAutoDetectLongPolling?this.experimentalAutoDetectLongPolling=!0:this.experimentalAutoDetectLongPolling=!!e.experimentalAutoDetectLongPolling,this.experimentalLongPollingOptions=s4(e.experimentalLongPollingOptions??{}),function(e){if(void 0!==e.timeoutSeconds){if(isNaN(e.timeoutSeconds))throw new b(A.INVALID_ARGUMENT,`invalid long polling timeout: ${e.timeoutSeconds} (must not be NaN)`);if(e.timeoutSeconds<5)throw new b(A.INVALID_ARGUMENT,`invalid long polling timeout: ${e.timeoutSeconds} (minimum allowed value is 5)`);if(e.timeoutSeconds>30)throw new b(A.INVALID_ARGUMENT,`invalid long polling timeout: ${e.timeoutSeconds} (maximum allowed value is 30)`)}}(this.experimentalLongPollingOptions),this.useFetchStreams=!!e.useFetchStreams}isEqual(e){var t,r;return this.host===e.host&&this.ssl===e.ssl&&this.credentials===e.credentials&&this.cacheSizeBytes===e.cacheSizeBytes&&this.experimentalForceLongPolling===e.experimentalForceLongPolling&&this.experimentalAutoDetectLongPolling===e.experimentalAutoDetectLongPolling&&(t=this.experimentalLongPollingOptions,r=e.experimentalLongPollingOptions,t.timeoutSeconds===r.timeoutSeconds)&&this.ignoreUndefinedProperties===e.ignoreUndefinedProperties&&this.useFetchStreams===e.useFetchStreams}}class s9{constructor(e,t,r,n){this._authCredentials=e,this._appCheckCredentials=t,this._databaseId=r,this._app=n,this.type="firestore-lite",this._persistenceKey="(lite)",this._settings=new s8({}),this._settingsFrozen=!1,this._emulatorOptions={},this._terminateTask="notTerminated"}get app(){if(!this._app)throw new b(A.FAILED_PRECONDITION,"Firestore was not initialized using the Firebase SDK. 'app' is not available");return this._app}get _initialized(){return this._settingsFrozen}get _terminated(){return"notTerminated"!==this._terminateTask}_setSettings(e){if(this._settingsFrozen)throw new b(A.FAILED_PRECONDITION,"Firestore has already been started and its settings can no longer be changed. You can only modify settings before calling any other methods on a Firestore object.");this._settings=new s8(e),this._emulatorOptions=e.emulatorOptions||{},void 0!==e.credentials&&(this._authCredentials=function(e){if(!e)return new x;switch(e.type){case"firstParty":return new O(e.sessionIndex||"0",e.iamToken||null,e.authTokenFactory||null);case"provider":return e.client;default:throw new b(A.INVALID_ARGUMENT,"makeAuthCredentialsProvider failed due to invalid credential type")}}(e.credentials))}_getSettings(){return this._settings}_getEmulatorOptions(){return this._emulatorOptions}_freezeSettings(){return this._settingsFrozen=!0,this._settings}_delete(){return"notTerminated"===this._terminateTask&&(this._terminateTask=this._terminate()),this._terminateTask}async _restart(){"notTerminated"===this._terminateTask?await this._terminate():this._terminateTask="notTerminated"}toJSON(){return{app:this._app,databaseId:this._databaseId,settings:this._settings}}_terminate(){return function(e){let t=s6.get(e);t&&(_("ComponentProvider","Removing Datastore"),s6.delete(e),t.terminate())}(this),Promise.resolve()}}class s7{constructor(e,t,r){this.converter=t,this._query=r,this.type="query",this.firestore=e}withConverter(e){return new s7(this.firestore,e,this._query)}}class ie{constructor(e,t,r){this.converter=t,this._key=r,this.type="document",this.firestore=e}get _path(){return this._key.path}get id(){return this._key.path.lastSegment()}get path(){return this._key.path.canonicalString()}get parent(){return new it(this.firestore,this.converter,this._key.path.popLast())}withConverter(e){return new ie(this.firestore,e,this._key)}toJSON(){return{type:ie._jsonSchemaVersion,referencePath:this._key.toString()}}static fromJSON(e,t,r){if(er(t,ie._jsonSchema))return new ie(e,r||null,new H(G.fromString(t.referencePath)))}}ie._jsonSchemaVersion="firestore/documentReference/1.0",ie._jsonSchema={type:et("string",ie._jsonSchemaVersion),referencePath:et("string")};class it extends s7{constructor(e,t,r){super(e,t,new tv(r)),this._path=r,this.type="collection"}get id(){return this._query.path.lastSegment()}get path(){return this._query.path.canonicalString()}get parent(){let e=this._path.popLast();return e.isEmpty()?null:new ie(this.firestore,null,new H(e))}withConverter(e){return new it(this.firestore,e,this._path)}}function ir(e,t,...r){if(e=(0,h.Ku)(e),W("collection","path",t),e instanceof s9){let n=G.fromString(t,...r);return X(n),new it(e,null,n)}{if(!(e instanceof ie||e instanceof it))throw new b(A.INVALID_ARGUMENT,"Expected first argument to collection() to be a CollectionReference, a DocumentReference or FirebaseFirestore");let n=e._path.child(G.fromString(t,...r));return X(n),new it(e.firestore,null,n)}}function is(e,t,...r){if(e=(0,h.Ku)(e),1==arguments.length&&(t=P.newId()),W("doc","path",t),e instanceof s9){let n=G.fromString(t,...r);return Y(n),new ie(e,null,new H(n))}{if(!(e instanceof ie||e instanceof it))throw new b(A.INVALID_ARGUMENT,"Expected first argument to collection() to be a CollectionReference, a DocumentReference or FirebaseFirestore");let n=e._path.child(G.fromString(t,...r));return Y(n),new ie(e.firestore,e instanceof it?e.converter:null,new H(n))}}let ii="AsyncQueue";class ia{constructor(e=Promise.resolve()){this.Xu=[],this.ec=!1,this.tc=[],this.nc=null,this.rc=!1,this.sc=!1,this.oc=[],this.M_=new nR(this,"async_queue_retry"),this._c=()=>{let e=nk();e&&_(ii,"Visibility state changed to "+e.visibilityState),this.M_.w_()},this.ac=e;let t=nk();t&&"function"==typeof t.addEventListener&&t.addEventListener("visibilitychange",this._c)}get isShuttingDown(){return this.ec}enqueueAndForget(e){this.enqueue(e)}enqueueAndForgetEvenWhileRestricted(e){this.uc(),this.cc(e)}enterRestrictedMode(e){if(!this.ec){this.ec=!0,this.sc=e||!1;let t=nk();t&&"function"==typeof t.removeEventListener&&t.removeEventListener("visibilitychange",this._c)}}enqueue(e){if(this.uc(),this.ec)return new Promise(()=>{});let t=new D;return this.cc(()=>this.ec&&this.sc?Promise.resolve():(e().then(t.resolve,t.reject),t.promise)).then(()=>t.promise)}enqueueRetryable(e){this.enqueueAndForget(()=>(this.Xu.push(e),this.lc()))}async lc(){if(0!==this.Xu.length){try{await this.Xu[0](),this.Xu.shift(),this.M_.reset()}catch(e){if(!eh(e))throw e;_(ii,"Operation failed with retryable error: "+e)}this.Xu.length>0&&this.M_.p_(()=>this.lc())}}cc(e){let t=this.ac.then(()=>(this.rc=!0,e().catch(e=>{throw this.nc=e,this.rc=!1,E("INTERNAL UNHANDLED ERROR: ",io(e)),e}).then(e=>(this.rc=!1,e))));return this.ac=t,t}enqueueAfterDelay(e,t,r){this.uc(),this.oc.indexOf(e)>-1&&(t=0);let n=ss.createAndSchedule(this,e,t,r,e=>this.hc(e));return this.tc.push(n),n}uc(){this.nc&&S(47125,{Pc:io(this.nc)})}verifyOperationInProgress(){}async Tc(){let e;do e=this.ac,await e;while(e!==this.ac)}Ic(e){for(let t of this.tc)if(t.timerId===e)return!0;return!1}Ec(e){return this.Tc().then(()=>{for(let t of(this.tc.sort((e,t)=>e.targetTimeMs-t.targetTimeMs),this.tc))if(t.skipDelay(),"all"!==e&&t.timerId===e)break;return this.Tc()})}dc(e){this.oc.push(e)}hc(e){let t=this.tc.indexOf(e);this.tc.splice(t,1)}}function io(e){let t=e.message||"";return e.stack&&(t=e.stack.includes(e.message)?e.stack:e.message+"\n"+e.stack),t}class il extends s9{constructor(e,t,r,n){super(e,t,r,n),this.type="firestore",this._queue=new ia,this._persistenceKey=n?.name||"[DEFAULT]"}async _terminate(){if(this._firestoreClient){let e=this._firestoreClient.terminate();this._queue=new ia(e),this._firestoreClient=void 0,await e}}}function iu(e,t){let r="object"==typeof e?e:(0,o.Sx)(),n=(0,o.j6)(r,"firestore").getImmediate({identifier:"string"==typeof e?e:t||eM});if(!n._initialized){let e=(0,h.yU)("firestore");e&&function(e,t,r,n={}){e=ee(e,s9);let s=(0,h.zJ)(t),i=e._getSettings(),a={...i,emulatorOptions:e._getEmulatorOptions()},o=`${t}:${r}`;s&&((0,h.gE)(`https://${o}`),(0,h.P1)("Firestore",!0)),i.host!==s5&&i.host!==o&&T("Host has been set in both settings() and connectFirestoreEmulator(), emulator host will be used.");let l={...i,host:o,ssl:s,emulatorOptions:n};if(!(0,h.bD)(l,a)&&(e._setSettings(l),n.mockUserToken)){let t,r;if("string"==typeof n.mockUserToken)t=n.mockUserToken,r=p.MOCK_USER;else{t=(0,h.Fy)(n.mockUserToken,e._app?.options.projectId);let s=n.mockUserToken.sub||n.mockUserToken.user_id;if(!s)throw new b(A.INVALID_ARGUMENT,"mockUserToken must contain 'sub' or 'user_id' field!");r=new p(s)}e._authCredentials=new R(new k(t,r))}}(n,...e)}return n}function ih(e){if(e._terminated)throw new b(A.FAILED_PRECONDITION,"The client has already been terminated.");return e._firestoreClient||function(e){var t,r;let n=e._freezeSettings(),s=(t=e._databaseId,r=e._app?.options.appId||"",new eO(t,r,e._persistenceKey,n.host,n.ssl,n.experimentalForceLongPolling,n.experimentalAutoDetectLongPolling,s4(n.experimentalLongPollingOptions),n.useFetchStreams,n.isUsingEmulator));e._componentsProvider||n.localCache?._offlineComponentProvider&&n.localCache?._onlineComponentProvider&&(e._componentsProvider={_offline:n.localCache._offlineComponentProvider,_online:n.localCache._onlineComponentProvider}),e._firestoreClient=new sJ(e._authCredentials,e._appCheckCredentials,e._queue,s,e._componentsProvider&&function(e){let t=e?._online.build();return{_offline:e?._offline.build(t),_online:t}}(e._componentsProvider))}(e),e._firestoreClient}class ic{constructor(e){this._byteString=e}static fromBase64String(e){try{return new ic(eI.fromBase64String(e))}catch(e){throw new b(A.INVALID_ARGUMENT,"Failed to construct data from Base64 string: "+e)}}static fromUint8Array(e){return new ic(eI.fromUint8Array(e))}toBase64(){return this._byteString.toBase64()}toUint8Array(){return this._byteString.toUint8Array()}toString(){return"Bytes(base64: "+this.toBase64()+")"}isEqual(e){return this._byteString.isEqual(e._byteString)}toJSON(){return{type:ic._jsonSchemaVersion,bytes:this.toBase64()}}static fromJSON(e){if(er(e,ic._jsonSchema))return ic.fromBase64String(e.bytes)}}ic._jsonSchemaVersion="firestore/bytes/1.0",ic._jsonSchema={type:et("string",ic._jsonSchemaVersion),bytes:et("string")};class id{constructor(...e){for(let t=0;t<e.length;++t)if(0===e[t].length)throw new b(A.INVALID_ARGUMENT,"Invalid field name at argument $(i + 1). Field names must not be empty.");this._internalPath=new Q(e)}isEqual(e){return this._internalPath.isEqual(e._internalPath)}}class im{constructor(e){this._methodName=e}}class ig{constructor(e,t){if(!isFinite(e)||e<-90||e>90)throw new b(A.INVALID_ARGUMENT,"Latitude must be a number between -90 and 90, but was: "+e);if(!isFinite(t)||t<-180||t>180)throw new b(A.INVALID_ARGUMENT,"Longitude must be a number between -180 and 180, but was: "+t);this._lat=e,this._long=t}get latitude(){return this._lat}get longitude(){return this._long}isEqual(e){return this._lat===e._lat&&this._long===e._long}_compareTo(e){return U(this._lat,e._lat)||U(this._long,e._long)}toJSON(){return{latitude:this._lat,longitude:this._long,type:ig._jsonSchemaVersion}}static fromJSON(e){if(er(e,ig._jsonSchema))return new ig(e.latitude,e.longitude)}}ig._jsonSchemaVersion="firestore/geoPoint/1.0",ig._jsonSchema={type:et("string",ig._jsonSchemaVersion),latitude:et("number"),longitude:et("number")};class ip{constructor(e){this._values=(e||[]).map(e=>e)}toArray(){return this._values.map(e=>e)}isEqual(e){return function(e,t){if(e.length!==t.length)return!1;for(let r=0;r<e.length;++r)if(e[r]!==t[r])return!1;return!0}(this._values,e._values)}toJSON(){return{type:ip._jsonSchemaVersion,vectorValues:this._values}}static fromJSON(e){if(er(e,ip._jsonSchema)){if(Array.isArray(e.vectorValues)&&e.vectorValues.every(e=>"number"==typeof e))return new ip(e.vectorValues);throw new b(A.INVALID_ARGUMENT,"Expected 'vectorValues' field to be a number array")}}}ip._jsonSchemaVersion="firestore/vectorValue/1.0",ip._jsonSchema={type:et("string",ip._jsonSchemaVersion),vectorValues:et("object")};let iy=/^__.*__$/;class iw{constructor(e,t,r){this.data=e,this.fieldMask=t,this.fieldTransforms=r}toMutation(e,t){return null!==this.fieldMask?new t9(e,this.data,this.fieldMask,t,this.fieldTransforms):new t8(e,this.data,t,this.fieldTransforms)}}function iv(e){switch(e){case 0:case 2:case 1:return!0;case 3:case 4:return!1;default:throw S(40011,{Ac:e})}}class i_{constructor(e,t,r,n,s,i){this.settings=e,this.databaseId=t,this.serializer=r,this.ignoreUndefinedProperties=n,void 0===s&&this.Rc(),this.fieldTransforms=s||[],this.fieldMask=i||[]}get path(){return this.settings.path}get Ac(){return this.settings.Ac}Vc(e){return new i_({...this.settings,...e},this.databaseId,this.serializer,this.ignoreUndefinedProperties,this.fieldTransforms,this.fieldMask)}mc(e){let t=this.path?.child(e),r=this.Vc({path:t,fc:!1});return r.gc(e),r}yc(e){let t=this.path?.child(e),r=this.Vc({path:t,fc:!1});return r.Rc(),r}wc(e){return this.Vc({path:void 0,fc:!0})}Sc(e){return ik(e,this.settings.methodName,this.settings.bc||!1,this.path,this.settings.Dc)}contains(e){return void 0!==this.fieldMask.find(t=>e.isPrefixOf(t))||void 0!==this.fieldTransforms.find(t=>e.isPrefixOf(t.field))}Rc(){if(this.path)for(let e=0;e<this.path.length;e++)this.gc(this.path.get(e))}gc(e){if(0===e.length)throw this.Sc("Document fields must not be empty");if(iv(this.Ac)&&iy.test(e))throw this.Sc('Document fields cannot begin and end with "__"')}}class iE{constructor(e,t,r){this.databaseId=e,this.ignoreUndefinedProperties=t,this.serializer=r||nx(e)}Cc(e,t,r,n=!1){return new i_({Ac:e,methodName:t,Dc:r,path:Q.emptyPath(),fc:!1,bc:n},this.databaseId,this.serializer,this.ignoreUndefinedProperties)}}function iT(e){let t=e._freezeSettings(),r=nx(e._databaseId);return new iE(e._databaseId,!!t.ignoreUndefinedProperties,r)}class iI extends im{_toFieldTransform(e){return new tZ(e.path,new tG)}isEqual(e){return e instanceof iI}}function iS(e,t){if(iN(e=(0,h.Ku)(e)))return iA("Unsupported field value:",t,e),iC(e,t);if(e instanceof im)return function(e,t){if(!iv(t.Ac))throw t.Sc(`${e._methodName}() can only be used with update() and set()`);if(!t.path)throw t.Sc(`${e._methodName}() is not currently supported inside arrays`);let r=e._toFieldTransform(t);r&&t.fieldTransforms.push(r)}(e,t),null;if(void 0===e&&t.ignoreUndefinedProperties)return null;if(t.path&&t.fieldMask.push(t.path),e instanceof Array){if(t.settings.fc&&4!==t.Ac)throw t.Sc("Nested arrays are not supported");let r=[],n=0;for(let s of e){let e=iS(s,t.wc(n));null==e&&(e={nullValue:"NULL_VALUE"}),r.push(e),n++}return{arrayValue:{values:r}}}return function(e,t){var r,n,s;if(null===(e=(0,h.Ku)(e)))return{nullValue:"NULL_VALUE"};if("number"==typeof e)return r=t.serializer,"number"==typeof(s=n=e)&&Number.isInteger(s)&&!ed(s)&&s<=Number.MAX_SAFE_INTEGER&&s>=Number.MIN_SAFE_INTEGER?tB(n):t$(r,n);if("boolean"==typeof e)return{booleanValue:e};if("string"==typeof e)return{stringValue:e};if(e instanceof Date){let r=en.fromDate(e);return{timestampValue:rb(t.serializer,r)}}if(e instanceof en){let r=new en(e.seconds,1e3*Math.floor(e.nanoseconds/1e3));return{timestampValue:rb(t.serializer,r)}}if(e instanceof ig)return{geoPointValue:{latitude:e.latitude,longitude:e.longitude}};if(e instanceof ic)return{bytesValue:rD(t.serializer,e._byteString)};if(e instanceof ie){let r=t.databaseId,n=e.firestore._databaseId;if(!n.isEqual(r))throw t.Sc(`Document reference is for database ${n.projectId}/${n.database} but should be for database ${r.projectId}/${r.database}`);return{referenceValue:rx(e.firestore._databaseId||t.databaseId,e._key.path)}}if(e instanceof ip)return{mapValue:{fields:{[eP]:{stringValue:e$},[eB]:{arrayValue:{values:e.toArray().map(e=>{if("number"!=typeof e)throw t.Sc("VectorValues must only contain numeric values.");return t$(t.serializer,e)})}}}}};throw t.Sc(`Unsupported field value: ${Z(e)}`)}(e,t)}function iC(e,t){let r={};return eg(e)?t.path&&t.path.length>0&&t.fieldMask.push(t.path):em(e,(e,n)=>{let s=iS(n,t.mc(e));null!=s&&(r[e]=s)}),{mapValue:{fields:r}}}function iN(e){return!("object"!=typeof e||null===e||e instanceof Array||e instanceof Date||e instanceof en||e instanceof ig||e instanceof ic||e instanceof ie||e instanceof im||e instanceof ip)}function iA(e,t,r){if(!iN(r)||!J(r)){let n=Z(r);throw"an object"===n?t.Sc(e+" a custom object"):t.Sc(e+" "+n)}}let ib=RegExp("[~\\*/\\[\\]]");function iD(e,t,r){if(t.search(ib)>=0)throw ik(`Invalid field path (${t}). Paths must not contain '~', '*', '/', '[', or ']'`,e,!1,void 0,r);try{return new id(...t.split("."))._internalPath}catch(n){throw ik(`Invalid field path (${t}). Paths must not be empty, begin with '.', end with '.', or contain '..'`,e,!1,void 0,r)}}function ik(e,t,r,n,s){let i=n&&!n.isEmpty(),a=void 0!==s,o=`Function ${t}() called with invalid data`;r&&(o+=" (via `toFirestore()`)"),o+=". ";let l="";return(i||a)&&(l+=" (found",i&&(l+=` in field ${n}`),a&&(l+=` in document ${s}`),l+=")"),new b(A.INVALID_ARGUMENT,o+e+l)}class ix{constructor(e,t,r,n,s){this._firestore=e,this._userDataWriter=t,this._key=r,this._document=n,this._converter=s}get id(){return this._key.path.lastSegment()}get ref(){return new ie(this._firestore,this._converter,this._key)}exists(){return null!==this._document}data(){if(this._document){if(this._converter){let e=new iR(this._firestore,this._userDataWriter,this._key,this._document,null);return this._converter.fromFirestore(e)}return this._userDataWriter.convertValue(this._document.data.value)}}get(e){if(this._document){let t=this._document.data.field(iV("DocumentSnapshot.get",e));if(null!==t)return this._userDataWriter.convertValue(t)}}}class iR extends ix{data(){return super.data()}}function iV(e,t){return"string"==typeof t?iD(e,t):t instanceof id?t._internalPath:t._delegate._internalPath}class iL{}class iO extends iL{}function iM(e,t,...r){let n=[];for(let s of(t instanceof iL&&n.push(t),function(e){let t=e.filter(e=>e instanceof iU).length,r=e.filter(e=>e instanceof iF).length;if(t>1||t>0&&r>0)throw new b(A.INVALID_ARGUMENT,"InvalidQuery. When using composite filters, you cannot use more than one filter at the top level. Consider nesting the multiple filters within an `and(...)` statement. For example: change `query(query, where(...), or(...))` to `query(query, and(where(...), or(...)))`.")}(n=n.concat(r)),n))e=s._apply(e);return e}class iF extends iO{constructor(e,t,r){super(),this._field=e,this._op=t,this._value=r,this.type="where"}static _create(e,t,r){return new iF(e,t,r)}_apply(e){let t=this._parse(e);return ij(e._query,t),new s7(e.firestore,e.converter,tS(e._query,t))}_parse(e){let t=iT(e.firestore);return function(e,t,r,n,s,i,a){let o;if(s.isKeyField()){if("array-contains"===i||"array-contains-any"===i)throw new b(A.INVALID_ARGUMENT,`Invalid Query. You can't perform '${i}' queries on documentId().`);if("in"===i||"not-in"===i){iz(a,i);let t=[];for(let r of a)t.push(iB(n,e,r));o={arrayValue:{values:t}}}else o=iB(n,e,a)}else"in"!==i&&"not-in"!==i&&"array-contains-any"!==i||iz(a,i),o=function(e,t,r,n=!1){return iS(r,e.Cc(n?4:3,t))}(r,t,a,"in"===i||"not-in"===i);return tr.create(s,i,o)}(e._query,"where",t,e.firestore._databaseId,this._field,this._op,this._value)}}function iP(e,t,r){let n=iV("where",e);return iF._create(n,t,r)}class iU extends iL{constructor(e,t){super(),this.type=e,this._queryConstraints=t}static _create(e,t){return new iU(e,t)}_parse(e){let t=this._queryConstraints.map(t=>t._parse(e)).filter(e=>e.getFilters().length>0);return 1===t.length?t[0]:tn.create(t,this._getOperator())}_apply(e){let t=this._parse(e);return 0===t.getFilters().length?e:(function(e,t){let r=e;for(let e of t.getFlattenedFilters())ij(r,e),r=tS(r,e)}(e._query,t),new s7(e.firestore,e.converter,tS(e._query,t)))}_getQueryConstraints(){return this._queryConstraints}_getOperator(){return"and"===this.type?"and":"or"}}class iq extends iO{constructor(e,t){super(),this._field=e,this._direction=t,this.type="orderBy"}static _create(e,t){return new iq(e,t)}_apply(e){let t=function(e,t,r){if(null!==e.startAt)throw new b(A.INVALID_ARGUMENT,"Invalid query. You must not call startAt() or startAfter() before calling orderBy().");if(null!==e.endAt)throw new b(A.INVALID_ARGUMENT,"Invalid query. You must not call endAt() or endBefore() before calling orderBy().");return new te(t,r)}(e._query,this._field,this._direction);return new s7(e.firestore,e.converter,function(e,t){let r=e.explicitOrderBy.concat([t]);return new tv(e.path,e.collectionGroup,r,e.filters.slice(),e.limit,e.limitType,e.startAt,e.endAt)}(e._query,t))}}function i$(e,t="asc"){let r=iV("orderBy",e);return iq._create(r,t)}function iB(e,t,r){if("string"==typeof(r=(0,h.Ku)(r))){if(""===r)throw new b(A.INVALID_ARGUMENT,"Invalid query. When querying with documentId(), you must provide a valid document ID, but it was an empty string.");if(!tE(t)&&-1!==r.indexOf("/"))throw new b(A.INVALID_ARGUMENT,`Invalid query. When querying a collection by documentId(), you must provide a plain document ID, but '${r}' contains a '/' character.`);let n=t.path.child(G.fromString(r));if(!H.isDocumentKey(n))throw new b(A.INVALID_ARGUMENT,`Invalid query. When querying a collection group by documentId(), the value provided must result in a valid document path, but '${n}' is not because it has an odd number of segments (${n.length}).`);return eY(e,new H(n))}if(r instanceof ie)return eY(e,r._key);throw new b(A.INVALID_ARGUMENT,`Invalid query. When querying with documentId(), you must provide a valid string or a DocumentReference, but it was: ${Z(r)}.`)}function iz(e,t){if(!Array.isArray(e)||0===e.length)throw new b(A.INVALID_ARGUMENT,`Invalid Query. A non-empty array is required for '${t.toString()}' filters.`)}function ij(e,t){let r=function(e,t){for(let r of e)for(let e of r.getFlattenedFilters())if(t.indexOf(e.op)>=0)return e.op;return null}(e.filters,function(e){switch(e){case"!=":return["!=","not-in"];case"array-contains-any":case"in":return["not-in"];case"not-in":return["array-contains-any","in","not-in","!="];default:return[]}}(t.op));if(null!==r)throw r===t.op?new b(A.INVALID_ARGUMENT,`Invalid query. You cannot use more than one '${t.op.toString()}' filter.`):new b(A.INVALID_ARGUMENT,`Invalid query. You cannot use '${t.op.toString()}' filters with '${r.toString()}' filters.`)}class iG{convertValue(e,t="none"){switch(ez(e)){case 0:return null;case 1:return e.booleanValue;case 2:return eN(e.integerValue||e.doubleValue);case 3:return this.convertTimestamp(e.timestampValue);case 4:return this.convertServerTimestamp(e,t);case 5:return e.stringValue;case 6:return this.convertBytes(eA(e.bytesValue));case 7:return this.convertReference(e.referenceValue);case 8:return this.convertGeoPoint(e.geoPointValue);case 9:return this.convertArray(e.arrayValue,t);case 11:return this.convertObject(e.mapValue,t);case 10:return this.convertVectorValue(e.mapValue);default:throw S(62114,{value:e})}}convertObject(e,t){return this.convertObjectMap(e.fields,t)}convertObjectMap(e,t="none"){let r={};return em(e,(e,n)=>{r[e]=this.convertValue(n,t)}),r}convertVectorValue(e){return new ip(e.fields?.[eB].arrayValue?.values?.map(e=>eN(e.doubleValue)))}convertGeoPoint(e){return new ig(eN(e.latitude),eN(e.longitude))}convertArray(e,t){return(e.values||[]).map(e=>this.convertValue(e,t))}convertServerTimestamp(e,t){switch(t){case"previous":let r=eV(e);return null==r?null:this.convertValue(r,t);case"estimate":return this.convertTimestamp(eL(e));default:return null}}convertTimestamp(e){let t=eC(e);return new en(t.seconds,t.nanos)}convertDocumentKey(e,t){let r=G.fromString(e);N(rB(r),9688,{name:e});let n=new eF(r.get(1),r.get(3)),s=new H(r.popFirst(5));return n.isEqual(t)||E(`Document ${s} contains a document reference within a different database (${n.projectId}/${n.database}) which is not supported. It will be treated as a reference in the current database (${t.projectId}/${t.database}) instead.`),s}}class iK{constructor(e,t){this.hasPendingWrites=e,this.fromCache=t}isEqual(e){return this.hasPendingWrites===e.hasPendingWrites&&this.fromCache===e.fromCache}}class iQ extends ix{constructor(e,t,r,n,s,i){super(e,t,r,n,i),this._firestore=e,this._firestoreImpl=e,this.metadata=s}exists(){return super.exists()}data(e={}){if(this._document){if(this._converter){let t=new iH(this._firestore,this._userDataWriter,this._key,this._document,this.metadata,null);return this._converter.fromFirestore(t,e)}return this._userDataWriter.convertValue(this._document.data.value,e.serverTimestamps)}}get(e,t={}){if(this._document){let r=this._document.data.field(iV("DocumentSnapshot.get",e));if(null!==r)return this._userDataWriter.convertValue(r,t.serverTimestamps)}}toJSON(){if(this.metadata.hasPendingWrites)throw new b(A.FAILED_PRECONDITION,"DocumentSnapshot.toJSON() attempted to serialize a document with pending writes. Await waitForPendingWrites() before invoking toJSON().");let e=this._document,t={};return t.type=iQ._jsonSchemaVersion,t.bundle="",t.bundleSource="DocumentSnapshot",t.bundleName=this._key.toString(),e&&e.isValidDocument()&&e.isFoundDocument()&&(this._userDataWriter.convertObjectMap(e.data.value.mapValue.fields,"previous"),this._firestore,this.ref.path,t.bundle="NOT SUPPORTED"),t}}iQ._jsonSchemaVersion="firestore/documentSnapshot/1.0",iQ._jsonSchema={type:et("string",iQ._jsonSchemaVersion),bundleSource:et("string","DocumentSnapshot"),bundleName:et("string"),bundle:et("string")};class iH extends iQ{data(e={}){return super.data(e)}}class iW{constructor(e,t,r,n){this._firestore=e,this._userDataWriter=t,this._snapshot=n,this.metadata=new iK(n.hasPendingWrites,n.fromCache),this.query=r}get docs(){let e=[];return this.forEach(t=>e.push(t)),e}get size(){return this._snapshot.docs.size}get empty(){return 0===this.size}forEach(e,t){this._snapshot.docs.forEach(r=>{e.call(t,new iH(this._firestore,this._userDataWriter,r.key,r,new iK(this._snapshot.mutatedKeys.has(r.key),this._snapshot.fromCache),this.query.converter))})}docChanges(e={}){let t=!!e.includeMetadataChanges;if(t&&this._snapshot.excludesMetadataChanges)throw new b(A.INVALID_ARGUMENT,"To include metadata changes with your document changes, you must also pass { includeMetadataChanges:true } to onSnapshot().");return this._cachedChanges&&this._cachedChangesIncludeMetadataChanges===t||(this._cachedChanges=function(e,t){if(e._snapshot.oldDocs.isEmpty()){let t=0;return e._snapshot.docChanges.map(r=>{let n=new iH(e._firestore,e._userDataWriter,r.doc.key,r.doc,new iK(e._snapshot.mutatedKeys.has(r.doc.key),e._snapshot.fromCache),e.query.converter);return r.doc,{type:"added",doc:n,oldIndex:-1,newIndex:t++}})}{let r=e._snapshot.oldDocs;return e._snapshot.docChanges.filter(e=>t||3!==e.type).map(t=>{let n=new iH(e._firestore,e._userDataWriter,t.doc.key,t.doc,new iK(e._snapshot.mutatedKeys.has(t.doc.key),e._snapshot.fromCache),e.query.converter),s=-1,i=-1;return 0!==t.type&&(s=r.indexOf(t.doc.key),r=r.delete(t.doc.key)),1!==t.type&&(i=(r=r.add(t.doc)).indexOf(t.doc.key)),{type:function(e){switch(e){case 0:return"added";case 2:case 3:return"modified";case 1:return"removed";default:return S(61501,{type:e})}}(t.type),doc:n,oldIndex:s,newIndex:i}})}}(this,t),this._cachedChangesIncludeMetadataChanges=t),this._cachedChanges}toJSON(){if(this.metadata.hasPendingWrites)throw new b(A.FAILED_PRECONDITION,"QuerySnapshot.toJSON() attempted to serialize a document with pending writes. Await waitForPendingWrites() before invoking toJSON().");let e={};e.type=iW._jsonSchemaVersion,e.bundleSource="QuerySnapshot",e.bundleName=P.newId(),this._firestore._databaseId.database,this._firestore._databaseId.projectId;let t=[],r=[],n=[];return this.docs.forEach(e=>{null!==e._document&&(t.push(e._document),r.push(this._userDataWriter.convertObjectMap(e._document.data.value.mapValue.fields,"previous")),n.push(e.ref.path))}),this._firestore,this.query._query,e.bundleName,e.bundle="NOT SUPPORTED",e}}function iY(e){e=ee(e,ie);let t=ee(e.firestore,il);return(function(e,t,r={}){let n=new D;return e.asyncQueue.enqueueAndForget(async()=>(function(e,t,r,n,s){let i=new sY({next:o=>{i.Nu(),t.enqueueAndForget(()=>sf(e,a));let l=o.docs.has(r);!l&&o.fromCache?s.reject(new b(A.UNAVAILABLE,"Failed to get document because the client is offline.")):l&&o.fromCache&&n&&"server"===n.source?s.reject(new b(A.UNAVAILABLE,'Failed to get document from server. (However, this document does exist in the local cache. Run again without setting source to "server" to retrieve the cached document.)')):s.resolve(o)},error:e=>s.reject(e)}),a=new sy(new tv(r.path),i,{includeMetadataChanges:!0,qa:!0});return sd(e,a)})(await s3(e),e.asyncQueue,t,r,n)),n.promise})(ih(t),e._key).then(r=>(function(e,t,r){let n=r.docs.get(t._key),s=new iX(e);return new iQ(e,s,t._key,n,new iK(r.hasPendingWrites,r.fromCache),t.converter)})(t,e,r))}iW._jsonSchemaVersion="firestore/querySnapshot/1.0",iW._jsonSchema={type:et("string",iW._jsonSchemaVersion),bundleSource:et("string","QuerySnapshot"),bundleName:et("string"),bundle:et("string")};class iX extends iG{constructor(e){super(),this.firestore=e}convertBytes(e){return new ic(e)}convertReference(e){let t=this.convertDocumentKey(e,this.firestore._databaseId);return new ie(this.firestore,null,t)}}function iJ(e){e=ee(e,s7);let t=ee(e.firestore,il),r=ih(t),n=new iX(t);return function(e){if("L"===e.limitType&&0===e.explicitOrderBy.length)throw new b(A.UNIMPLEMENTED,"limitToLast() queries require specifying at least one orderBy() clause")}(e._query),(function(e,t,r={}){let n=new D;return e.asyncQueue.enqueueAndForget(async()=>(function(e,t,r,n,s){let i=new sY({next:r=>{i.Nu(),t.enqueueAndForget(()=>sf(e,a)),r.fromCache&&"server"===n.source?s.reject(new b(A.UNAVAILABLE,'Failed to get documents from server. (However, these documents may exist in the local cache. Run again without setting source to "server" to retrieve the cached documents.)')):s.resolve(r)},error:e=>s.reject(e)}),a=new sy(r,i,{includeMetadataChanges:!0,qa:!0});return sd(e,a)})(await s3(e),e.asyncQueue,t,r,n)),n.promise})(r,e._query).then(r=>new iW(t,n,e,r))}function iZ(e,t,r){var n;e=ee(e,ie);let s=ee(e.firestore,il),i=(n=e.converter,n?r&&(r.merge||r.mergeFields)?n.toFirestore(t,r):n.toFirestore(t):t);return i1(s,[(function(e,t,r,n,s,i={}){let a,o,l=e.Cc(i.merge||i.mergeFields?2:0,t,r,s);iA("Data must be an object, but it was:",l,n);let u=iC(n,l);if(i.merge)a=new eE(l.fieldMask),o=l.fieldTransforms;else if(i.mergeFields){let e=[];for(let n of i.mergeFields){let s=function(e,t,r){if((t=(0,h.Ku)(t))instanceof id)return t._internalPath;if("string"==typeof t)return iD(e,t);throw ik("Field path arguments must be of type string or ",e,!1,void 0,r)}(t,n,r);if(!l.contains(s))throw new b(A.INVALID_ARGUMENT,`Field '${s}' is specified in your field mask but missing from your input data.`);(function(e,t){return e.some(e=>e.isEqual(t))})(e,s)||e.push(s)}a=new eE(e),o=l.fieldTransforms.filter(e=>a.covers(e.field))}else a=null,o=l.fieldTransforms;return new iw(new e6(u),a,o)})(iT(s),"setDoc",e._key,i,null!==e.converter,r).toMutation(e._key,t1.none())])}function i0(e){return i1(ee(e.firestore,il),[new rr(e._key,t1.none())])}function i1(e,t){var r=ih(e);let n=new D;return r.asyncQueue.enqueueAndForget(async()=>sx(await s2(r).then(e=>e.syncEngine),t,n)),n.promise}function i2(){return new iI("serverTimestamp")}new WeakMap,function(e=!0){y=o.MF,(0,o.om)(new l.uA("firestore",(t,{instanceIdentifier:r,options:n})=>{let s=t.getProvider("app").getImmediate(),i=new il(new V(t.getProvider("auth-internal")),new F(s,t.getProvider("app-check-internal")),function(e,t){if(!Object.prototype.hasOwnProperty.apply(e.options,["projectId"]))throw new b(A.INVALID_ARGUMENT,'"projectId" not provided in firebase.initializeApp.');return new eF(e.options.projectId,t)}(s,r),s);return n={useFetchStreams:e,...n},i._setSettings(n),i},"PUBLIC").setMultipleInstances(!0)),(0,o.KO)(m,g,void 0),(0,o.KO)(m,g,"esm2020")}()}}]);