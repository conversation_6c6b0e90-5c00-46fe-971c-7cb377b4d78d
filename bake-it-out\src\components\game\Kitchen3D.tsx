'use client'

import { <PERSON>vas, useFrame } from '@react-three/fiber'
import { OrbitControls, Environment, ContactShadows, Text, Box, Sphere, Cylinder, Float, Html, Stars } from '@react-three/drei'
import { Physics, useBox } from '@react-three/cannon'
import { Suspense, useRef, useState, useEffect } from 'react'
import { useGame } from '@/contexts/GameContext'
import { useLanguage } from '@/contexts/LanguageContext'
import { FloatingPanel, Button3D, HUD3D } from '@/components/game/Game3DWorld'
import * as THREE from 'three'

// Enhanced 3D Equipment with Animations
function Oven3D({ position, onClick, isActive }: { 
  position: [number, number, number], 
  onClick: () => void, 
  isActive: boolean 
}) {
  const [ref] = useBox(() => ({ position, mass: 0 }))
  const [hovered, setHovered] = useState(false)
  const [doorOpen, setDoorOpen] = useState(false)
  const fireRef = useRef<THREE.Mesh>(null)

  useFrame((state) => {
    if (fireRef.current && isActive) {
      fireRef.current.scale.y = 1 + Math.sin(state.clock.elapsedTime * 8) * 0.3
      fireRef.current.scale.x = 1 + Math.sin(state.clock.elapsedTime * 6) * 0.2
    }
  })

  return (
    <Float speed={hovered ? 2 : 0.5} rotationIntensity={hovered ? 0.2 : 0} floatIntensity={hovered ? 0.2 : 0}>
      <group 
        ref={ref} 
        onClick={() => {
          onClick()
          setDoorOpen(!doorOpen)
        }}
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
      >
        {/* Oven Base */}
        <Box args={[2.5, 2, 2]} position={[0, 1, 0]}>
          <meshStandardMaterial 
            color={isActive ? "#ff6b35" : hovered ? "#A0522D" : "#8B4513"} 
            emissive={isActive ? "#ff2200" : "#000000"}
            emissiveIntensity={isActive ? 0.3 : 0}
            roughness={0.7}
            metalness={0.1}
          />
        </Box>
        
        {/* Oven Door */}
        <Box 
          args={[2.2, 1.5, 0.15]} 
          position={[0, 1, doorOpen ? 1.5 : 1.1]}
          rotation={doorOpen ? [-Math.PI / 3, 0, 0] : [0, 0, 0]}
        >
          <meshStandardMaterial color="#2C1810" transparent opacity={0.8} />
        </Box>
        
        {/* Oven Handle */}
        <Cylinder args={[0.08, 0.08, 1.5]} position={[0.8, 1, 1.2]} rotation={[0, 0, Math.PI / 2]}>
          <meshStandardMaterial color="#C0C0C0" metalness={0.9} roughness={0.1} />
        </Cylinder>
        
        {/* Control Panel */}
        <Box args={[1.5, 0.3, 0.1]} position={[0, 1.8, 1.1]}>
          <meshStandardMaterial color="#333333" />
        </Box>
        
        {/* Control Knobs */}
        <Cylinder args={[0.1, 0.1, 0.05]} position={[-0.4, 1.8, 1.15]}>
          <meshStandardMaterial color="#FFD700" metalness={0.8} roughness={0.2} />
        </Cylinder>
        <Cylinder args={[0.1, 0.1, 0.05]} position={[0, 1.8, 1.15]}>
          <meshStandardMaterial color="#FFD700" metalness={0.8} roughness={0.2} />
        </Cylinder>
        <Cylinder args={[0.1, 0.1, 0.05]} position={[0.4, 1.8, 1.15]}>
          <meshStandardMaterial color="#FFD700" metalness={0.8} roughness={0.2} />
        </Cylinder>
        
        {/* Fire Effect */}
        {isActive && (
          <group position={[0, 1.2, 0]}>
            <Sphere ref={fireRef} args={[0.4]}>
              <meshStandardMaterial 
                color="#ff4500" 
                emissive="#ff4500" 
                emissiveIntensity={0.8}
                transparent
                opacity={0.7}
              />
            </Sphere>
            <pointLight position={[0, 0, 0]} intensity={2} color="#ff4500" distance={5} />
          </group>
        )}
        
        {/* Equipment Label */}
        <Text
          position={[0, 2.8, 0]}
          fontSize={0.4}
          color={hovered ? "#FF6B35" : "#8B4513"}
          anchorX="center"
          anchorY="middle"
        >
          🔥 Professional Oven
        </Text>
        
        {/* Status Indicator */}
        <Sphere args={[0.1]} position={[1.2, 2.2, 0]}>
          <meshStandardMaterial 
            color={isActive ? "#00ff00" : "#ff0000"}
            emissive={isActive ? "#00ff00" : "#ff0000"}
            emissiveIntensity={0.5}
          />
        </Sphere>
        
        {/* Hover UI */}
        {hovered && (
          <Html position={[0, 3.2, 0]} center>
            <div className="bg-black/80 text-white px-3 py-2 rounded-lg text-sm">
              <p>🔥 Professional Oven</p>
              <p className="text-xs">Click to {doorOpen ? 'close' : 'open'} door</p>
              <p className="text-xs">Status: {isActive ? 'Active' : 'Idle'}</p>
            </div>
          </Html>
        )}
      </group>
    </Float>
  )
}

function Mixer3D({ position, onClick, isActive }: { 
  position: [number, number, number], 
  onClick: () => void, 
  isActive: boolean 
}) {
  const [ref] = useBox(() => ({ position, mass: 0 }))
  const [hovered, setHovered] = useState(false)
  const armRef = useRef<THREE.Group>(null)

  useFrame((state) => {
    if (armRef.current && isActive) {
      armRef.current.rotation.y = state.clock.elapsedTime * 6
    }
  })

  return (
    <Float speed={hovered ? 2 : 0.5} rotationIntensity={hovered ? 0.1 : 0} floatIntensity={hovered ? 0.1 : 0}>
      <group 
        ref={ref} 
        onClick={onClick}
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
      >
        {/* Mixer Base */}
        <Box args={[2, 1.5, 2]} position={[0, 0.75, 0]}>
          <meshStandardMaterial 
            color={hovered ? "#DDA0DD" : "#E6E6FA"} 
            roughness={0.3}
            metalness={0.2}
          />
        </Box>
        
        {/* Mixer Bowl */}
        <Sphere args={[0.8]} position={[0, 1.5, 0]}>
          <meshStandardMaterial 
            color="#F5F5DC" 
            metalness={0.3} 
            roughness={0.6}
          />
        </Sphere>
        
        {/* Mixer Arm */}
        <group ref={armRef} position={[0, 2.5, 0]}>
          <Cylinder args={[0.15, 0.15, 1.5]} position={[0, 0, 0]}>
            <meshStandardMaterial color="#C0C0C0" metalness={0.9} roughness={0.1} />
          </Cylinder>
          <Cylinder args={[0.08, 0.08, 0.4]} position={[0.3, -0.7, 0]}>
            <meshStandardMaterial color="#C0C0C0" metalness={0.9} roughness={0.1} />
          </Cylinder>
          <Sphere args={[0.1]} position={[0.3, -0.9, 0]}>
            <meshStandardMaterial color="#C0C0C0" metalness={0.9} roughness={0.1} />
          </Sphere>
        </group>
        
        {/* Control Panel */}
        <Box args={[1, 0.2, 0.1]} position={[0, 0.3, 1.05]}>
          <meshStandardMaterial color="#333333" />
        </Box>
        
        {/* Speed Control */}
        <Cylinder args={[0.08, 0.08, 0.03]} position={[0, 0.3, 1.1]}>
          <meshStandardMaterial color="#FFD700" metalness={0.8} roughness={0.2} />
        </Cylinder>
        
        {/* Mixing Particles */}
        {isActive && (
          <>
            <Float speed={3} rotationIntensity={0.5} floatIntensity={0.5}>
              <Sphere args={[0.08]} position={[0.3, 1.5, 0.2]}>
                <meshStandardMaterial 
                  color="#FFD700" 
                  emissive="#FFD700" 
                  emissiveIntensity={0.5}
                  transparent
                  opacity={0.8}
                />
              </Sphere>
            </Float>
            <Float speed={2.5} rotationIntensity={0.4} floatIntensity={0.4}>
              <Sphere args={[0.06]} position={[-0.2, 1.5, 0.3]}>
                <meshStandardMaterial 
                  color="#FFA500" 
                  emissive="#FFA500" 
                  emissiveIntensity={0.5}
                  transparent
                  opacity={0.8}
                />
              </Sphere>
            </Float>
          </>
        )}
        
        {/* Equipment Label */}
        <Text
          position={[0, 3.5, 0]}
          fontSize={0.4}
          color={hovered ? "#FF6B35" : "#8B4513"}
          anchorX="center"
          anchorY="middle"
        >
          🥄 Stand Mixer
        </Text>
        
        {/* Status Indicator */}
        <Sphere args={[0.1]} position={[1, 2.8, 0]}>
          <meshStandardMaterial 
            color={isActive ? "#00ff00" : "#ff0000"}
            emissive={isActive ? "#00ff00" : "#ff0000"}
            emissiveIntensity={0.5}
          />
        </Sphere>
        
        {/* Hover UI */}
        {hovered && (
          <Html position={[0, 4, 0]} center>
            <div className="bg-black/80 text-white px-3 py-2 rounded-lg text-sm">
              <p>🥄 Stand Mixer</p>
              <p className="text-xs">Click to start mixing</p>
              <p className="text-xs">Status: {isActive ? 'Mixing' : 'Ready'}</p>
            </div>
          </Html>
        )}
      </group>
    </Float>
  )
}

function WorkStation3D({ position, onClick }: { 
  position: [number, number, number], 
  onClick: () => void 
}) {
  const [ref] = useBox(() => ({ position, mass: 0 }))
  const [hovered, setHovered] = useState(false)

  return (
    <Float speed={hovered ? 1 : 0.3} rotationIntensity={0} floatIntensity={hovered ? 0.05 : 0}>
      <group 
        ref={ref} 
        onClick={onClick}
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
      >
        {/* Counter Surface */}
        <Box args={[4, 0.3, 2]} position={[0, 1.15, 0]}>
          <meshStandardMaterial 
            color={hovered ? "#DEB887" : "#D2B48C"} 
            roughness={0.8}
            metalness={0.1}
          />
        </Box>
        
        {/* Counter Base */}
        <Box args={[3.8, 1, 1.8]} position={[0, 0.5, 0]}>
          <meshStandardMaterial color={hovered ? "#A0522D" : "#8B4513"} />
        </Box>
        
        {/* Cutting Board */}
        <Box args={[1, 0.05, 0.8]} position={[-1, 1.3, 0]}>
          <meshStandardMaterial color="#DEB887" roughness={0.9} />
        </Box>
        
        {/* Kitchen Tools */}
        <Cylinder args={[0.03, 0.03, 0.4]} position={[1.2, 1.5, 0.4]} rotation={[Math.PI / 4, 0, 0]}>
          <meshStandardMaterial color="#C0C0C0" metalness={0.9} roughness={0.1} />
        </Cylinder>
        <Cylinder args={[0.03, 0.03, 0.35]} position={[0.8, 1.5, 0.3]} rotation={[Math.PI / 6, 0, Math.PI / 4]}>
          <meshStandardMaterial color="#8B4513" />
        </Cylinder>
        <Box args={[0.3, 0.05, 0.2]} position={[1.5, 1.35, 0.2]}>
          <meshStandardMaterial color="#FFD700" metalness={0.8} roughness={0.2} />
        </Box>
        
        {/* Ingredient Bowls */}
        <Cylinder args={[0.2, 0.15, 0.15]} position={[0.5, 1.35, -0.3]}>
          <meshStandardMaterial color="#F5F5DC" />
        </Cylinder>
        <Cylinder args={[0.18, 0.13, 0.12]} position={[0.5, 1.42, -0.3]}>
          <meshStandardMaterial color="#FFFACD" />
        </Cylinder>
        
        {/* Equipment Label */}
        <Text
          position={[0, 2.2, 0]}
          fontSize={0.4}
          color={hovered ? "#FF6B35" : "#8B4513"}
          anchorX="center"
          anchorY="middle"
        >
          🏪 Work Station
        </Text>
        
        {/* Hover UI */}
        {hovered && (
          <Html position={[0, 2.6, 0]} center>
            <div className="bg-black/80 text-white px-3 py-2 rounded-lg text-sm">
              <p>🏪 Work Station</p>
              <p className="text-xs">Click to prepare ingredients</p>
              <p className="text-xs">Tools: Knife, Spoon, Measuring Cup</p>
            </div>
          </Html>
        )}
      </group>
    </Float>
  )
}

// Kitchen Environment
function KitchenEnvironment() {
  return (
    <>
      {/* Floor with detailed pattern */}
      <Box args={[25, 0.1, 25]} position={[0, -0.05, 0]}>
        <meshStandardMaterial color="#F5DEB3" roughness={0.8} metalness={0.1} />
      </Box>
      
      {/* Floor tiles */}
      {Array.from({ length: 12 }).map((_, i) =>
        Array.from({ length: 12 }).map((_, j) => (
          <Box 
            key={`${i}-${j}`}
            args={[1.9, 0.08, 1.9]} 
            position={[-11 + i * 2, 0.04, -11 + j * 2]}
          >
            <meshStandardMaterial 
              color={(i + j) % 2 === 0 ? "#F5DEB3" : "#DEB887"} 
              roughness={0.9}
            />
          </Box>
        ))
      )}
      
      {/* Walls */}
      <Box args={[25, 5, 0.3]} position={[0, 2.5, -12.5]}>
        <meshStandardMaterial color="#FFEFD5" roughness={0.7} />
      </Box>
      <Box args={[0.3, 5, 25]} position={[-12.5, 2.5, 0]}>
        <meshStandardMaterial color="#FFEFD5" roughness={0.7} />
      </Box>
      <Box args={[0.3, 5, 25]} position={[12.5, 2.5, 0]}>
        <meshStandardMaterial color="#FFEFD5" roughness={0.7} />
      </Box>
      
      {/* Ceiling */}
      <Box args={[25, 0.3, 25]} position={[0, 5, 0]}>
        <meshStandardMaterial color="#F0F8FF" roughness={0.5} />
      </Box>
      
      {/* Kitchen Cabinets */}
      <Box args={[8, 2, 1]} position={[-8, 1, -11.5]}>
        <meshStandardMaterial color="#8B4513" />
      </Box>
      <Box args={[8, 1, 1]} position={[-8, 3.5, -11.5]}>
        <meshStandardMaterial color="#8B4513" />
      </Box>
      
      {/* Refrigerator */}
      <Box args={[1.5, 4, 1.2]} position={[10, 2, -11]}>
        <meshStandardMaterial color="#E6E6FA" metalness={0.3} roughness={0.2} />
      </Box>
      <Box args={[0.1, 3, 0.1]} position={[9.3, 2, -10.4]}>
        <meshStandardMaterial color="#C0C0C0" metalness={0.9} roughness={0.1} />
      </Box>
      
      {/* Ceiling Lights */}
      <Cylinder args={[0.4, 0.4, 0.15]} position={[-4, 4.8, -4]}>
        <meshStandardMaterial color="#FFFACD" emissive="#FFFACD" emissiveIntensity={0.6} />
      </Cylinder>
      <Cylinder args={[0.4, 0.4, 0.15]} position={[4, 4.8, -4]}>
        <meshStandardMaterial color="#FFFACD" emissive="#FFFACD" emissiveIntensity={0.6} />
      </Cylinder>
      <Cylinder args={[0.4, 0.4, 0.15]} position={[0, 4.8, 4]}>
        <meshStandardMaterial color="#FFFACD" emissive="#FFFACD" emissiveIntensity={0.6} />
      </Cylinder>
    </>
  )
}

// Main Kitchen 3D Component
export function Kitchen3D() {
  const { t } = useLanguage()
  const { equipment, inventory, player, orders } = useGame()
  const [selectedEquipment, setSelectedEquipment] = useState<string | null>(null)
  const [showOrders, setShowOrders] = useState(false)
  const [showInventory, setShowInventory] = useState(false)

  const handleEquipmentClick = (equipmentId: string) => {
    setSelectedEquipment(equipmentId)
  }

  return (
    <group>
      {/* Kitchen Environment */}
      <KitchenEnvironment />
      
      {/* Equipment */}
      <Oven3D 
        position={[-8, 0, -8]} 
        onClick={() => handleEquipmentClick('oven')}
        isActive={equipment.find(e => e.name === 'Oven')?.isActive || false}
      />
      <Mixer3D 
        position={[-3, 0, -8]} 
        onClick={() => handleEquipmentClick('mixer')}
        isActive={equipment.find(e => e.name === 'Mixer')?.isActive || false}
      />
      <WorkStation3D 
        position={[3, 0, -8]} 
        onClick={() => handleEquipmentClick('workstation')}
      />
      
      {/* HUD Elements */}
      <HUD3D>
        {/* Orders Panel */}
        <Button3D 
          position={[8, 4, 0]} 
          size={[2.5, 0.8, 0.3]}
          color="#FF6B35"
          onClick={() => setShowOrders(!showOrders)}
        >
          📋 Orders ({orders.length})
        </Button3D>
        
        {/* Inventory Panel */}
        <Button3D 
          position={[8, 2.5, 0]} 
          size={[2.5, 0.8, 0.3]}
          color="#4CAF50"
          onClick={() => setShowInventory(!showInventory)}
        >
          📦 Inventory
        </Button3D>
        
        {/* Player Stats */}
        <Text
          position={[0, 4.5, 10]}
          fontSize={0.5}
          color="#8B4513"
          anchorX="center"
          anchorY="middle"
        >
          Level {player.level} | ${player.money}
        </Text>
      </HUD3D>
      
      {/* Floating Panels */}
      {showOrders && (
        <FloatingPanel
          position={[6, 2, 5]}
          size={[4, 5]}
          title={t('orders.title', 'Current Orders')}
          onClose={() => setShowOrders(false)}
        >
          <div className="space-y-2">
            {orders.slice(0, 5).map((order, index) => (
              <div key={index} className="bg-orange-100 p-2 rounded">
                <p className="font-bold">{order.recipe}</p>
                <p className="text-sm">Quantity: {order.quantity}</p>
                <p className="text-sm">Time: {order.timeRemaining}s</p>
              </div>
            ))}
          </div>
        </FloatingPanel>
      )}
      
      {showInventory && (
        <FloatingPanel
          position={[-6, 2, 5]}
          size={[4, 5]}
          title={t('inventory.title', 'Inventory')}
          onClose={() => setShowInventory(false)}
        >
          <div className="grid grid-cols-2 gap-2">
            {inventory.slice(0, 8).map((item, index) => (
              <div key={index} className="bg-green-100 p-2 rounded text-center">
                <p className="text-lg">{item.icon}</p>
                <p className="text-xs">{item.name}</p>
                <p className="text-xs font-bold">{item.quantity}</p>
              </div>
            ))}
          </div>
        </FloatingPanel>
      )}
    </group>
  )
}
