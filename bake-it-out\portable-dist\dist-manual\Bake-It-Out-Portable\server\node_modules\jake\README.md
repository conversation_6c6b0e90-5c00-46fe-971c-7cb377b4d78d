### Jake -- the JavaScript build tool for Node.js

[![Build Status](https://travis-ci.org/jakejs/jake.svg?branch=master)](https://travis-ci.org/jakejs/jake)

Documentation site at [http://jakejs.com](http://jakejs.com/)

### Contributing
1. [Install node](http://nodejs.org/#download).
2. Clone this repository `$ <NAME_EMAIL>:jakejs/jake.git`.
3. Install dependencies `$ npm install`.
4. Run tests with `$ npm test`.
5. Start Hacking!

### License

Licensed under the Apache License, Version 2.0
(<http://www.apache.org/licenses/LICENSE-2.0>)
