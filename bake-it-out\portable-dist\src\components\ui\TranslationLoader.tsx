'use client'

import { useEffect, useState } from 'react'
import { useLanguage } from '@/contexts/LanguageContext'

interface TranslationLoaderProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

/**
 * TranslationLoader ensures that the translation system is fully loaded
 * before rendering children components. This prevents translation keys
 * from showing as raw strings during initial load.
 */
export function TranslationLoader({ children, fallback }: TranslationLoaderProps) {
  const { t, language } = useLanguage()
  const [isReady, setIsReady] = useState(false)

  useEffect(() => {
    // Test if translations are working by checking a known key
    const testTranslation = t('game.title')
    
    // If we get back the actual translation (not the key), we're ready
    if (testTranslation && testTranslation !== 'game.title') {
      setIsReady(true)
    } else {
      // If not ready, try again after a short delay
      const timer = setTimeout(() => {
        const retestTranslation = t('game.title')
        if (retestTranslation && retestTranslation !== 'game.title') {
          setIsReady(true)
        } else {
          // Force ready after timeout to prevent infinite loading
          console.warn('Translation system not fully loaded, proceeding anyway')
          setIsReady(true)
        }
      }, 100)

      return () => clearTimeout(timer)
    }
  }, [t, language])

  if (!isReady) {
    return (
      <>
        {fallback || (
          <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-orange-50 to-amber-50">
            <div className="text-center">
              <div className="text-4xl mb-4">🧁</div>
              <div className="text-lg font-medium text-orange-800">Loading translations...</div>
              <div className="text-sm text-orange-600 mt-2">Načítání překladů...</div>
            </div>
          </div>
        )}
      </>
    )
  }

  return <>{children}</>
}
