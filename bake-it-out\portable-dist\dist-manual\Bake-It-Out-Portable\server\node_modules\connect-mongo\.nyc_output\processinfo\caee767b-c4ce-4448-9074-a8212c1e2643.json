{"parent": null, "pid": 7505, "argv": ["/Users/<USER>/.nvm/versions/node/v18.16.0/bin/node", "/Users/<USER>/jdesboeufs/connect-mongo/node_modules/.bin/ava"], "execArgv": [], "cwd": "/Users/<USER>/jdesboeufs/connect-mongo", "time": 1697308499074, "ppid": 7502, "coverageFilename": "/Users/<USER>/jdesboeufs/connect-mongo/.nyc_output/caee767b-c4ce-4448-9074-a8212c1e2643.json", "externalId": "", "uuid": "caee767b-c4ce-4448-9074-a8212c1e2643", "files": []}