@echo off
title Bake It Out - Super Simple Setup

echo.
echo ===============================================
echo        BAKE IT OUT - Super Simple Setup
echo ===============================================
echo.

REM Check if we're in the right directory
if not exist "package.json" (
    echo ERROR: package.json not found!
    echo Please run this script from the server directory.
    pause
    exit /b 1
)

echo Step 1: Installing dependencies...
npm install
if %errorlevel% neq 0 (
    echo ERROR: npm install failed
    echo Make sure Node.js is installed: https://nodejs.org/
    pause
    exit /b 1
)

echo Step 2: Creating .env file...
if not exist ".env" (
    if exist ".env.example" (
        copy ".env.example" ".env" >nul
        echo SUCCESS: .env file created from template
    ) else (
        echo NODE_ENV=development > .env
        echo PORT=3001 >> .env
        echo MONGODB_URI=mongodb://localhost:27017/bake-it-out >> .env
        echo JWT_SECRET=super-secret-key-change-this-in-production >> .env
        echo JWT_EXPIRES_IN=7d >> .env
        echo ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3002 >> .env
        echo BCRYPT_ROUNDS=12 >> .env
        echo RATE_LIMIT_WINDOW_MS=900000 >> .env
        echo RATE_LIMIT_MAX_REQUESTS=100 >> .env
        echo AUTH_RATE_LIMIT_MAX=10 >> .env
        echo SUCCESS: Basic .env file created
    )
) else (
    echo SUCCESS: .env file already exists
)

echo Step 3: Creating directories...
if not exist "logs" mkdir logs
if not exist "uploads" mkdir uploads
if not exist "backups" mkdir backups
echo SUCCESS: Directories created

echo.
echo ===============================================
echo                SETUP COMPLETE!
echo ===============================================
echo.
echo Your server is ready to start!
echo.
echo To start the server:
echo   npm start
echo.
echo Then visit:
echo   http://localhost:3001/health (health check)
echo   http://localhost:3001/dashboard (admin panel)
echo.
echo IMPORTANT: Make sure MongoDB is running!
echo - Install Docker: docker run -d -p 27017:27017 mongo:6.0
echo - Or install MongoDB locally
echo - Or use MongoDB Atlas (cloud)
echo.
echo Press any key to exit...
pause >nul
