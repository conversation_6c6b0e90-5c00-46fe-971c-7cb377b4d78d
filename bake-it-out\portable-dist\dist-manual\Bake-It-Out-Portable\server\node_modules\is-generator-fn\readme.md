# is-generator-fn [![Build Status](https://travis-ci.org/sindresorhus/is-generator-fn.svg?branch=master)](https://travis-ci.org/sindresorhus/is-generator-fn)

> Check if something is a [generator function](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function*)


## Install

```
$ npm install is-generator-fn
```


## Usage

```js
const isGeneratorFn = require('is-generator-fn');

isGeneratorFn(function * () {});
//=> true

isGeneratorFn(function () {});
//=> false
```


## Related

- [is](https://github.com/sindresorhus/is) - Type check values


## License

MIT © [Sindre Sorhus](https://sindresorhus.com)
