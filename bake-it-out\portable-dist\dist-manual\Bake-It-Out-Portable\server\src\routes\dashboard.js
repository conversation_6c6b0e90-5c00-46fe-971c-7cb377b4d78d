const express = require('express');
const User = require('../models/User');
const CloudSave = require('../models/CloudSave');
const GameRoom = require('../models/GameRoom');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Dashboard authentication middleware (admin only for now)
const requireAdmin = async (req, res, next) => {
  try {
    // For now, allow any authenticated user to access dashboard
    // In production, you'd check for admin role
    const token = req.headers.authorization?.split(' ')[1] || req.query.token;
    
    if (!token) {
      return res.redirect('/dashboard/login');
    }

    const jwt = require('jsonwebtoken');
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production');
    const user = await User.findById(decoded.userId);
    
    if (!user || !user.isActive) {
      return res.redirect('/dashboard/login');
    }

    req.user = user;
    next();
  } catch (error) {
    res.redirect('/dashboard/login');
  }
};

// Dashboard login page
router.get('/login', (req, res) => {
  res.render('dashboard/login', {
    title: 'Bake It Out - Dashboard Login',
    error: req.query.error
  });
});

// Dashboard login handler
router.post('/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    const user = await User.findOne({
      $or: [{ username }, { email: username }],
      isActive: true
    });

    if (!user || !(await user.comparePassword(password))) {
      return res.redirect('/dashboard/login?error=Invalid credentials');
    }

    const jwt = require('jsonwebtoken');
    const token = jwt.sign(
      { userId: user._id },
      process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production',
      { expiresIn: '24h' }
    );

    res.redirect(`/dashboard?token=${token}`);
  } catch (error) {
    console.error('Dashboard login error:', error);
    res.redirect('/dashboard/login?error=Login failed');
  }
});

// Main dashboard
router.get('/', requireAdmin, async (req, res) => {
  try {
    // Get server statistics
    const stats = await getServerStats();
    
    res.render('dashboard/index', {
      title: 'Bake It Out - Server Dashboard',
      user: req.user,
      stats,
      currentPage: 'dashboard'
    });
  } catch (error) {
    console.error('Dashboard error:', error);
    res.status(500).render('dashboard/error', {
      title: 'Dashboard Error',
      error: 'Failed to load dashboard'
    });
  }
});

// Users management
router.get('/users', requireAdmin, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = 20;
    const skip = (page - 1) * limit;

    const users = await User.find({ isActive: true })
      .sort({ createdAt: -1 })
      .limit(limit)
      .skip(skip);

    const totalUsers = await User.countDocuments({ isActive: true });
    const totalPages = Math.ceil(totalUsers / limit);

    res.render('dashboard/users', {
      title: 'User Management',
      user: req.user,
      users,
      currentPage: 'users',
      pagination: {
        current: page,
        total: totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });
  } catch (error) {
    console.error('Users page error:', error);
    res.status(500).render('dashboard/error', {
      title: 'Users Error',
      error: 'Failed to load users'
    });
  }
});

// Game rooms management
router.get('/rooms', requireAdmin, async (req, res) => {
  try {
    const rooms = await GameRoom.find({ isActive: true })
      .populate('host', 'username profile.displayName')
      .populate('players', 'username profile.displayName')
      .sort({ lastActivity: -1 });

    res.render('dashboard/rooms', {
      title: 'Game Rooms',
      user: req.user,
      rooms,
      currentPage: 'rooms'
    });
  } catch (error) {
    console.error('Rooms page error:', error);
    res.status(500).render('dashboard/error', {
      title: 'Rooms Error',
      error: 'Failed to load rooms'
    });
  }
});

// Cloud saves management
router.get('/saves', requireAdmin, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = 20;
    const skip = (page - 1) * limit;

    const saves = await CloudSave.find({ isActive: true })
      .populate('userId', 'username profile.displayName')
      .sort({ updatedAt: -1 })
      .limit(limit)
      .skip(skip);

    const totalSaves = await CloudSave.countDocuments({ isActive: true });
    const totalPages = Math.ceil(totalSaves / limit);

    res.render('dashboard/saves', {
      title: 'Cloud Saves',
      user: req.user,
      saves,
      currentPage: 'saves',
      pagination: {
        current: page,
        total: totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });
  } catch (error) {
    console.error('Saves page error:', error);
    res.status(500).render('dashboard/error', {
      title: 'Saves Error',
      error: 'Failed to load saves'
    });
  }
});

// Analytics page
router.get('/analytics', requireAdmin, async (req, res) => {
  try {
    const analytics = await getAnalyticsData();
    
    res.render('dashboard/analytics', {
      title: 'Analytics',
      user: req.user,
      analytics,
      currentPage: 'analytics'
    });
  } catch (error) {
    console.error('Analytics page error:', error);
    res.status(500).render('dashboard/error', {
      title: 'Analytics Error',
      error: 'Failed to load analytics'
    });
  }
});

// API endpoints for real-time data
router.get('/api/stats', requireAdmin, async (req, res) => {
  try {
    const stats = await getServerStats();
    res.json(stats);
  } catch (error) {
    res.status(500).json({ error: 'Failed to get stats' });
  }
});

router.get('/api/analytics', requireAdmin, async (req, res) => {
  try {
    const analytics = await getAnalyticsData();
    res.json(analytics);
  } catch (error) {
    res.status(500).json({ error: 'Failed to get analytics' });
  }
});

// Helper functions
async function getServerStats() {
  const [
    totalUsers,
    activeUsers,
    totalSaves,
    totalRooms,
    activeRooms,
    totalSaveSize
  ] = await Promise.all([
    User.countDocuments({ isActive: true }),
    User.countDocuments({ 
      isActive: true, 
      lastLoginAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
    }),
    CloudSave.countDocuments({ isActive: true }),
    GameRoom.countDocuments({}),
    GameRoom.countDocuments({ isActive: true }),
    CloudSave.aggregate([
      { $match: { isActive: true } },
      { $group: { _id: null, totalSize: { $sum: '$size' } } }
    ])
  ]);

  return {
    users: {
      total: totalUsers,
      active: activeUsers,
      inactive: totalUsers - activeUsers
    },
    saves: {
      total: totalSaves,
      totalSize: totalSaveSize[0]?.totalSize || 0
    },
    rooms: {
      total: totalRooms,
      active: activeRooms,
      inactive: totalRooms - activeRooms
    },
    server: {
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: process.env.npm_package_version || '1.0.0'
    }
  };
}

async function getAnalyticsData() {
  const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
  
  const [
    userGrowth,
    saveActivity,
    roomActivity,
    topUsers
  ] = await Promise.all([
    User.aggregate([
      { $match: { createdAt: { $gte: thirtyDaysAgo } } },
      {
        $group: {
          _id: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
          count: { $sum: 1 }
        }
      },
      { $sort: { _id: 1 } }
    ]),
    CloudSave.aggregate([
      { $match: { createdAt: { $gte: thirtyDaysAgo } } },
      {
        $group: {
          _id: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
          count: { $sum: 1 }
        }
      },
      { $sort: { _id: 1 } }
    ]),
    GameRoom.aggregate([
      { $match: { createdAt: { $gte: thirtyDaysAgo } } },
      {
        $group: {
          _id: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
          count: { $sum: 1 }
        }
      },
      { $sort: { _id: 1 } }
    ]),
    User.find({ isActive: true })
      .sort({ 'gameStats.totalPlayTime': -1 })
      .limit(10)
      .select('username profile.displayName gameStats')
  ]);

  return {
    userGrowth,
    saveActivity,
    roomActivity,
    topUsers
  };
}

module.exports = router;
