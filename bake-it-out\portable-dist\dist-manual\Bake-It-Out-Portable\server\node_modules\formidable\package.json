{"name": "formidable", "version": "2.1.5", "license": "MIT", "description": "A node.js module for parsing form data, especially file uploads.", "homepage": "https://github.com/node-formidable/formidable", "funding": "https://ko-fi.com/tunnckoCore/commissions", "repository": "node-formidable/formidable", "main": "./src/index.js", "files": ["src"], "publishConfig": {"access": "public", "tag": "v2-latest"}, "scripts": {"bench": "node benchmark", "fmt": "yarn run fmt:prepare '**/*'", "fmt:prepare": "prettier --write", "lint": "yarn run lint:prepare .", "lint:prepare": "eslint --cache --fix --quiet --format codeframe", "reinstall": "rm -rf ./node_modules ./yarn.lock", "postreinstall": "yarn setup", "setup": "yarn", "pretest": "rm -rf ./test/tmp && mkdir -p ./test/tmp", "test:local": "jest --coverage", "pretest:ci": "yarn run pretest", "test": "nyc jest --coverage"}, "dependencies": {"@paralleldrive/cuid2": "^2.2.2", "dezalgo": "^1.0.4", "once": "^1.4.0", "qs": "^6.11.0"}, "devDependencies": {"@tunnckocore/prettier-config": "1.3.8", "eslint": "6.8.0", "eslint-config-airbnb-base": "14.1.0", "eslint-config-prettier": "6.11.0", "eslint-plugin-import": "2.20.2", "eslint-plugin-prettier": "3.1.3", "express": "4.17.1", "husky": "4.2.5", "jest": "27.2.4", "koa": "2.11.0", "nyc": "15.0.1", "prettier": "2.0.5", "prettier-plugin-pkgjson": "0.2.8", "request": "2.88.2", "supertest": "4.0.2"}, "jest": {"verbose": true}, "keywords": ["multipart", "form", "data", "querystring", "www", "json", "ulpoad", "file"]}