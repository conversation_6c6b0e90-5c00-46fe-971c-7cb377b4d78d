'use client'

import { Canvas, use<PERSON>rame } from '@react-three/fiber'
import { OrbitControls, Environment, ContactShadows, Text, Box, Sphere, Cylinder, Float, Html, Stars, PerspectiveCamera } from '@react-three/drei'
import { Physics } from '@react-three/cannon'
import { Suspense, useRef, useState, useEffect } from 'react'
import { useGame } from '@/contexts/GameContext'
import { useLanguage } from '@/contexts/LanguageContext'
import * as THREE from 'three'

// 3D Scene Types
export type Game3DScene = 'kitchen' | 'dining' | 'storage' | 'shop' | 'office' | 'overview'

// 3D UI Components
export function FloatingPanel({ 
  position, 
  size = [4, 3], 
  children, 
  title,
  onClose 
}: {
  position: [number, number, number]
  size?: [number, number]
  children: React.ReactNode
  title?: string
  onClose?: () => void
}) {
  const [hovered, setHovered] = useState(false)

  return (
    <Float speed={1} rotationIntensity={0.1} floatIntensity={0.1}>
      <group position={position}>
        {/* Panel Background */}
        <Box 
          args={[size[0], size[1], 0.1]} 
          onPointerOver={() => setHovered(true)}
          onPointerOut={() => setHovered(false)}
        >
          <meshStandardMaterial 
            color={hovered ? "#f0f8ff" : "#ffffff"} 
            transparent 
            opacity={0.95}
            roughness={0.1}
            metalness={0.1}
          />
        </Box>
        
        {/* Panel Border */}
        <Box args={[size[0] + 0.1, size[1] + 0.1, 0.05]} position={[0, 0, -0.05]}>
          <meshStandardMaterial color="#8B4513" />
        </Box>

        {/* Title */}
        {title && (
          <Text
            position={[0, size[1]/2 - 0.3, 0.1]}
            fontSize={0.3}
            color="#8B4513"
            anchorX="center"
            anchorY="middle"

          >
            {title}
          </Text>
        )}

        {/* Close Button */}
        {onClose && (
          <group position={[size[0]/2 - 0.3, size[1]/2 - 0.3, 0.1]}>
            <Sphere args={[0.15]} onClick={onClose}>
              <meshStandardMaterial color="#ff4444" />
            </Sphere>
            <Text
              position={[0, 0, 0.16]}
              fontSize={0.2}
              color="white"
              anchorX="center"
              anchorY="middle"
            >
              ×
            </Text>
          </group>
        )}

        {/* Content Area */}
        <Html
          position={[0, 0, 0.11]}
          transform
          occlude
          style={{
            width: `${size[0] * 100}px`,
            height: `${size[1] * 100}px`,
            pointerEvents: 'auto'
          }}
        >
          <div className="w-full h-full p-4 overflow-auto">
            {children}
          </div>
        </Html>
      </group>
    </Float>
  )
}

export function Button3D({ 
  position, 
  size = [2, 0.8, 0.3], 
  color = "#4CAF50",
  hoverColor = "#45a049",
  children,
  onClick 
}: {
  position: [number, number, number]
  size?: [number, number, number]
  color?: string
  hoverColor?: string
  children: React.ReactNode
  onClick?: () => void
}) {
  const [hovered, setHovered] = useState(false)
  const [pressed, setPressed] = useState(false)

  return (
    <Float speed={hovered ? 2 : 1} rotationIntensity={0.1} floatIntensity={hovered ? 0.2 : 0.1}>
      <group 
        position={position}
        scale={pressed ? [0.95, 0.95, 0.95] : [1, 1, 1]}
      >
        <Box 
          args={size}
          onPointerOver={() => setHovered(true)}
          onPointerOut={() => setHovered(false)}
          onPointerDown={() => setPressed(true)}
          onPointerUp={() => setPressed(false)}
          onClick={onClick}
        >
          <meshStandardMaterial 
            color={hovered ? hoverColor : color}
            roughness={0.3}
            metalness={0.1}
          />
        </Box>
        
        <Text
          position={[0, 0, size[2]/2 + 0.01]}
          fontSize={0.3}
          color="white"
          anchorX="center"
          anchorY="middle"

        >
          {children}
        </Text>
      </group>
    </Float>
  )
}

export function HUD3D({ children }: { children: React.ReactNode }) {
  return (
    <group position={[0, 0, 0]}>
      {children}
    </group>
  )
}

// Scene Transition System
export function SceneTransition({ 
  fromScene, 
  toScene, 
  progress 
}: { 
  fromScene: Game3DScene
  toScene: Game3DScene
  progress: number 
}) {
  const transitionRef = useRef<THREE.Group>(null)

  useFrame(() => {
    if (transitionRef.current) {
      transitionRef.current.rotation.y = progress * Math.PI * 2
      transitionRef.current.scale.setScalar(1 + progress * 0.5)
    }
  })

  return (
    <group ref={transitionRef}>
      <Sphere args={[10]} position={[0, 0, 0]}>
        <meshStandardMaterial 
          color="#FFD700" 
          transparent 
          opacity={progress * 0.3}
          emissive="#FFD700"
          emissiveIntensity={progress * 0.5}
        />
      </Sphere>
    </group>
  )
}

// Loading Screen 3D
export function Loading3D() {
  const loadingRef = useRef<THREE.Group>(null)

  useFrame((state) => {
    if (loadingRef.current) {
      loadingRef.current.rotation.y = state.clock.elapsedTime
    }
  })

  return (
    <group>
      <ambientLight intensity={0.5} />
      <pointLight position={[10, 10, 10]} />
      
      <group ref={loadingRef}>
        <Sphere args={[1]} position={[0, 0, 0]}>
          <meshStandardMaterial color="#FFD700" emissive="#FFD700" emissiveIntensity={0.5} />
        </Sphere>
        <Sphere args={[0.5]} position={[2, 0, 0]}>
          <meshStandardMaterial color="#FF6B35" emissive="#FF6B35" emissiveIntensity={0.3} />
        </Sphere>
        <Sphere args={[0.3]} position={[-2, 0, 0]}>
          <meshStandardMaterial color="#4CAF50" emissive="#4CAF50" emissiveIntensity={0.3} />
        </Sphere>
      </group>
      
      <Text
        position={[0, -3, 0]}
        fontSize={0.5}
        color="#8B4513"
        anchorX="center"
        anchorY="middle"
      >
        🧁 Loading Bake It Out...
      </Text>
    </group>
  )
}

// Main 3D Game World Manager
export function Game3DWorld({ 
  currentScene = 'kitchen',
  onSceneChange 
}: {
  currentScene: Game3DScene
  onSceneChange: (scene: Game3DScene) => void
}) {
  const { t } = useLanguage()
  const [isTransitioning, setIsTransitioning] = useState(false)
  const [transitionProgress, setTransitionProgress] = useState(0)

  const handleSceneTransition = (newScene: Game3DScene) => {
    if (newScene === currentScene) return
    
    setIsTransitioning(true)
    setTransitionProgress(0)
    
    // Animate transition
    const duration = 1000 // 1 second
    const startTime = Date.now()
    
    const animate = () => {
      const elapsed = Date.now() - startTime
      const progress = Math.min(elapsed / duration, 1)
      
      setTransitionProgress(progress)
      
      if (progress < 1) {
        requestAnimationFrame(animate)
      } else {
        setIsTransitioning(false)
        onSceneChange(newScene)
      }
    }
    
    requestAnimationFrame(animate)
  }

  return (
    <div className="w-full h-screen bg-gradient-to-b from-blue-900 via-purple-900 to-indigo-900">
      <Canvas
        camera={{ position: [0, 5, 10], fov: 75 }}
        shadows
        dpr={[1, 2]}
        gl={{ antialias: true, alpha: false }}
      >
        <Suspense fallback={<Loading3D />}>
          <Physics gravity={[0, -9.8, 0]}>
            {/* Global Lighting */}
            <ambientLight intensity={0.4} color="#FFF8DC" />
            <directionalLight
              position={[10, 10, 5]}
              intensity={1}
              castShadow
              shadow-mapSize-width={2048}
              shadow-mapSize-height={2048}
            />
            <pointLight position={[0, 10, 0]} intensity={0.5} color="#FFE4B5" />
            
            {/* Environment */}
            <Environment preset="sunset" background />
            <Stars radius={100} depth={50} count={1000} factor={4} saturation={0} fade speed={1} />
            
            {/* Scene Navigation HUD */}
            <HUD3D>
              <group position={[-8, 4, 0]}>
                <Button3D 
                  position={[0, 2, 0]} 
                  onClick={() => handleSceneTransition('kitchen')}
                  color={currentScene === 'kitchen' ? "#FF6B35" : "#4CAF50"}
                >
                  🏪 {t('game.scene.kitchen', 'Kitchen')}
                </Button3D>
                
                <Button3D 
                  position={[0, 0.5, 0]} 
                  onClick={() => handleSceneTransition('dining')}
                  color={currentScene === 'dining' ? "#FF6B35" : "#4CAF50"}
                >
                  🍽️ {t('game.scene.dining', 'Dining')}
                </Button3D>
                
                <Button3D 
                  position={[0, -1, 0]} 
                  onClick={() => handleSceneTransition('storage')}
                  color={currentScene === 'storage' ? "#FF6B35" : "#4CAF50"}
                >
                  📦 {t('game.scene.storage', 'Storage')}
                </Button3D>
                
                <Button3D 
                  position={[0, -2.5, 0]} 
                  onClick={() => handleSceneTransition('shop')}
                  color={currentScene === 'shop' ? "#FF6B35" : "#4CAF50"}
                >
                  🛒 {t('game.scene.shop', 'Shop')}
                </Button3D>
              </group>
            </HUD3D>
            
            {/* Transition Effect */}
            {isTransitioning && (
              <SceneTransition 
                fromScene={currentScene} 
                toScene={currentScene} 
                progress={transitionProgress} 
              />
            )}
            
            {/* Ground */}
            <Box args={[50, 0.1, 50]} position={[0, -0.05, 0]}>
              <meshStandardMaterial color="#2F4F2F" />
            </Box>
            
            {/* Camera Controls */}
            <OrbitControls
              enablePan={true}
              enableZoom={true}
              enableRotate={true}
              minDistance={5}
              maxDistance={30}
              minPolarAngle={Math.PI / 6}
              maxPolarAngle={Math.PI / 2.2}
              enableDamping={true}
              dampingFactor={0.05}
            />
          </Physics>
        </Suspense>
      </Canvas>
    </div>
  )
}
