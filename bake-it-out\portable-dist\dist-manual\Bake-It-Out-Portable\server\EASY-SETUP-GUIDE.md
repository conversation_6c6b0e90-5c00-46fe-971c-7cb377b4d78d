# 🧁 Bake It Out - Easy Setup Guide

**Get your server running in under 2 minutes!**

## ⚡ **Quick Start (Choose One)**

### **🎯 Option 1: Super Simple (Always Works)**
```bash
# Just run this - no complexity!
SIMPLE-SETUP.bat
```
**Perfect for:** First-time users, quick testing, guaranteed to work

### **🚀 Option 2: Node.js Command**
```bash
npm install
npm run quick-setup
```
**Perfect for:** Developers, command-line users

### **🔧 Option 3: Manual (Full Control)**
```bash
npm install
copy .env.example .env
mkdir logs uploads backups
```
**Perfect for:** Advanced users, custom configurations

---

## 📋 **What Each Setup Does**

### **SIMPLE-SETUP.bat**
✅ **Installs dependencies** - Downloads all required packages  
✅ **Creates .env file** - Configuration with secure defaults  
✅ **Creates directories** - logs/, uploads/, backups/  
✅ **Clear instructions** - Tells you exactly what to do next  
✅ **Always works** - No complex automation that can fail  

### **npm run quick-setup**
✅ **Creates .env file** - Basic configuration  
✅ **Creates directories** - Required folders  
✅ **Generates secure keys** - Random JWT secrets  
✅ **Validation** - Checks if everything is set up correctly  

---

## 🗄️ **Database Setup (Required)**

After running setup, you need MongoDB. **Choose one option:**

### **Option A: Docker (Easiest)**
```bash
# Install Docker, then run:
docker run -d --name bake-it-out-mongo -p 27017:27017 mongo:6.0
```

### **Option B: Local MongoDB**
1. Download from: https://www.mongodb.com/try/download/community
2. Install and start MongoDB service
3. Default connection: `mongodb://localhost:27017/bake-it-out`

### **Option C: MongoDB Atlas (Cloud)**
1. Create account: https://cloud.mongodb.com/
2. Create free cluster
3. Get connection string
4. Update `MONGODB_URI` in `.env` file

---

## 🚀 **Starting Your Server**

After setup and database:

```bash
# Start the server
npm start
```

**Your server will be available at:**
- 🌐 **API**: http://localhost:3001
- 📊 **Dashboard**: http://localhost:3001/dashboard  
- 🔍 **Health Check**: http://localhost:3001/health

---

## ✅ **Verification Steps**

### **1. Check Server Health**
```bash
# Visit in browser or use curl:
curl http://localhost:3001/health
```
**Expected response:** `{"status":"OK","timestamp":"..."}`

### **2. Test Dashboard**
- Visit: http://localhost:3001/dashboard
- Should show login page
- Use any game account to login

### **3. Test API**
- Visit: http://localhost:3001/
- Should show API information

---

## 🆘 **Troubleshooting**

### **"npm install failed"**
```bash
# Clear cache and retry
npm cache clean --force
npm install
```

### **"Port 3001 already in use"**
```bash
# Change port in .env file
PORT=3002
```

### **"MongoDB connection failed"**
```bash
# Check if MongoDB is running
# For Docker:
docker ps | grep mongo

# For local:
# Make sure MongoDB service is running
```

### **"Module not found"**
```bash
# Reinstall dependencies
rm -rf node_modules
npm install
```

### **"Permission denied"**
```bash
# Run as administrator (Windows)
# Or check file permissions
```

---

## 🔧 **Configuration Options**

### **Environment Variables (.env)**
```env
# Server settings
NODE_ENV=development
PORT=3001

# Database (choose one)
MONGODB_URI=mongodb://localhost:27017/bake-it-out
# MONGODB_URI=mongodb+srv://user:<EMAIL>/bake-it-out

# Security
JWT_SECRET=your-secure-secret-here
JWT_EXPIRES_IN=7d

# CORS (add your game client URLs)
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3002
```

### **Optional Settings**
```env
# Rate limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security
BCRYPT_ROUNDS=12
```

---

## 📊 **Features Available**

### **After Setup You Get:**
✅ **Cloud Saves** - Players can save games to the cloud  
✅ **User Accounts** - Registration and login system  
✅ **Multiplayer** - Real-time multiplayer game rooms  
✅ **Web Dashboard** - Professional admin interface  
✅ **Analytics** - Game statistics and metrics  
✅ **API Endpoints** - Complete REST API  
✅ **Real-time Updates** - Socket.IO communication  

### **Dashboard Features:**
✅ **User Management** - View and manage players  
✅ **Game Rooms** - Monitor multiplayer sessions  
✅ **Cloud Saves** - Manage save files  
✅ **Analytics** - Charts and statistics  
✅ **Server Monitoring** - Real-time metrics  

---

## 🎮 **Connecting Your Game**

### **Update Game Client**
Make sure your game points to the server:
```javascript
// In your game's environment
NEXT_PUBLIC_API_URL=http://localhost:3001
```

### **Test Connection**
1. Start your game client
2. Register/login in the game
3. Try saving to cloud
4. Check dashboard for your save

---

## 📞 **Getting Help**

### **Validation Commands**
```bash
# Check if setup is correct
npm run validate

# Test server startup
test-setup.bat  # (if available)
```

### **Debug Mode**
```bash
# Start with detailed logging
DEBUG=* npm start
```

### **Log Files**
```bash
# Check server logs
tail -f logs/combined.log
```

---

## 🎉 **Success Indicators**

### **Your setup is working when:**
✅ **npm start** runs without errors  
✅ **Health check** returns OK  
✅ **Dashboard loads** at /dashboard  
✅ **Can create accounts** in the game  
✅ **Cloud saves work** from the game  
✅ **Multiplayer rooms** can be created  

---

## 🚀 **Next Steps**

### **After Setup:**
1. **Start server**: `npm start`
2. **Start game client**: `npx serve out -p 3002`
3. **Play**: http://localhost:3002
4. **Manage**: http://localhost:3001/dashboard

### **For Production:**
1. **Change JWT_SECRET** in .env
2. **Use production database** (MongoDB Atlas)
3. **Set NODE_ENV=production**
4. **Configure proper CORS origins**
5. **Set up SSL/HTTPS**

---

**🎮 Ready to start your bakery empire? Run the setup and start baking! 🧁**
