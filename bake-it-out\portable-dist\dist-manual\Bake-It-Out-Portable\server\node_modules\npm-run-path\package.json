{"name": "npm-run-path", "version": "4.0.1", "description": "Get your PATH prepended with locally installed binaries", "license": "MIT", "repository": "sindresorhus/npm-run-path", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["npm", "run", "path", "package", "bin", "binary", "binaries", "script", "cli", "command-line", "execute", "executable"], "dependencies": {"path-key": "^3.0.0"}, "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}