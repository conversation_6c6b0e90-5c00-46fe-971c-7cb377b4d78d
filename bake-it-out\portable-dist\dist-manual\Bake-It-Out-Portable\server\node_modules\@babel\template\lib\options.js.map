{"version": 3, "names": ["merge", "a", "b", "placeholder<PERSON><PERSON><PERSON><PERSON>", "placeholder<PERSON><PERSON><PERSON>", "preserveComments", "syntacticPlaceholders", "parser", "Object", "assign", "validate", "opts", "Error", "_ref", "_objectWithoutPropertiesLoose", "_excluded", "Set", "RegExp", "undefined", "normalizeReplacements", "replacements", "Array", "isArray", "reduce", "acc", "replacement", "i"], "sources": ["../src/options.ts"], "sourcesContent": ["import type { ParserOptions as ParserOpts } from \"@babel/parser\";\n\nexport type { ParserOpts };\n\n/**\n * These are the options that 'babel-template' actually accepts and typechecks\n * when called. All other options are passed through to the parser.\n */\nexport type PublicOpts = {\n  /**\n   * A set of placeholder names to automatically accept, ignoring the given\n   * pattern entirely.\n   *\n   * This option can be used when using %%foo%% style placeholders.\n   */\n  placeholderWhitelist?: Set<string>;\n  /**\n   * A pattern to search for when looking for Identifier and StringLiteral\n   * nodes that can be replaced.\n   *\n   * 'false' will disable placeholder searching entirely, leaving only the\n   * 'placeholderWhitelist' value to find replacements.\n   *\n   * Defaults to /^[_$A-Z0-9]+$/.\n   *\n   * This option can be used when using %%foo%% style placeholders.\n   */\n  placeholderPattern?: RegExp | false;\n  /**\n   * 'true' to pass through comments from the template into the resulting AST,\n   * or 'false' to automatically discard comments. Defaults to 'false'.\n   */\n  preserveComments?: boolean;\n  /**\n   * 'true' to use %%foo%% style placeholders, 'false' to use legacy placeholders\n   * described by placeholderPattern or placeholderWhitelist.\n   * When it is not set, it behaves as 'true' if there are syntactic placeholders,\n   * otherwise as 'false'.\n   */\n  syntacticPlaceholders?: boolean | null;\n} & ParserOpts;\n\nexport type TemplateOpts = {\n  parser: ParserOpts;\n  placeholderWhitelist?: Set<string>;\n  placeholderPattern?: RegExp | false;\n  preserveComments?: boolean;\n  syntacticPlaceholders?: boolean;\n};\n\nexport function merge(a: TemplateOpts, b: TemplateOpts): TemplateOpts {\n  const {\n    placeholderWhitelist = a.placeholderWhitelist,\n    placeholderPattern = a.placeholderPattern,\n    preserveComments = a.preserveComments,\n    syntacticPlaceholders = a.syntacticPlaceholders,\n  } = b;\n\n  return {\n    parser: {\n      ...a.parser,\n      ...b.parser,\n    },\n    placeholderWhitelist,\n    placeholderPattern,\n    preserveComments,\n    syntacticPlaceholders,\n  };\n}\n\nexport function validate(opts: unknown): TemplateOpts {\n  if (opts != null && typeof opts !== \"object\") {\n    throw new Error(\"Unknown template options.\");\n  }\n\n  const {\n    placeholderWhitelist,\n    placeholderPattern,\n    preserveComments,\n    syntacticPlaceholders,\n    ...parser\n  } = opts || ({} as any);\n\n  if (placeholderWhitelist != null && !(placeholderWhitelist instanceof Set)) {\n    throw new Error(\n      \"'.placeholderWhitelist' must be a Set, null, or undefined\",\n    );\n  }\n\n  if (\n    placeholderPattern != null &&\n    !(placeholderPattern instanceof RegExp) &&\n    placeholderPattern !== false\n  ) {\n    throw new Error(\n      \"'.placeholderPattern' must be a RegExp, false, null, or undefined\",\n    );\n  }\n\n  if (preserveComments != null && typeof preserveComments !== \"boolean\") {\n    throw new Error(\n      \"'.preserveComments' must be a boolean, null, or undefined\",\n    );\n  }\n\n  if (\n    syntacticPlaceholders != null &&\n    typeof syntacticPlaceholders !== \"boolean\"\n  ) {\n    throw new Error(\n      \"'.syntacticPlaceholders' must be a boolean, null, or undefined\",\n    );\n  }\n  if (\n    syntacticPlaceholders === true &&\n    (placeholderWhitelist != null || placeholderPattern != null)\n  ) {\n    throw new Error(\n      \"'.placeholderWhitelist' and '.placeholderPattern' aren't compatible\" +\n        \" with '.syntacticPlaceholders: true'\",\n    );\n  }\n\n  return {\n    parser,\n    placeholderWhitelist: placeholderWhitelist || undefined,\n    placeholderPattern:\n      placeholderPattern == null ? undefined : placeholderPattern,\n    preserveComments: preserveComments == null ? undefined : preserveComments,\n    syntacticPlaceholders:\n      syntacticPlaceholders == null ? undefined : syntacticPlaceholders,\n  };\n}\n\nexport type PublicReplacements = { [x: string]: unknown } | Array<unknown>;\nexport type TemplateReplacements = { [x: string]: unknown } | void;\n\nexport function normalizeReplacements(\n  replacements: unknown,\n): TemplateReplacements {\n  if (Array.isArray(replacements)) {\n    return replacements.reduce((acc, replacement, i) => {\n      acc[\"$\" + i] = replacement;\n      return acc;\n    }, {});\n  } else if (typeof replacements === \"object\" || replacements == null) {\n    return (replacements as any) || undefined;\n  }\n\n  throw new Error(\n    \"Template replacements must be an array, object, null, or undefined\",\n  );\n}\n"], "mappings": ";;;;;;;;;;AAkDO,SAASA,KAAKA,CAACC,CAAe,EAAEC,CAAe,EAAgB;EACpE,MAAM;IACJC,oBAAoB,GAAGF,CAAC,CAACE,oBAAoB;IAC7CC,kBAAkB,GAAGH,CAAC,CAACG,kBAAkB;IACzCC,gBAAgB,GAAGJ,CAAC,CAACI,gBAAgB;IACrCC,qBAAqB,GAAGL,CAAC,CAACK;EAC5B,CAAC,GAAGJ,CAAC;EAEL,OAAO;IACLK,MAAM,EAAAC,MAAA,CAAAC,MAAA,KACDR,CAAC,CAACM,MAAM,EACRL,CAAC,CAACK,MAAM,CACZ;IACDJ,oBAAoB;IACpBC,kBAAkB;IAClBC,gBAAgB;IAChBC;EACF,CAAC;AACH;AAEO,SAASI,QAAQA,CAACC,IAAa,EAAgB;EACpD,IAAIA,IAAI,IAAI,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC5C,MAAM,IAAIC,KAAK,CAAC,2BAA2B,CAAC;EAC9C;EAEA,MAAAC,IAAA,GAMIF,IAAI,IAAK,CAAC,CAAS;IANjB;MACJR,oBAAoB;MACpBC,kBAAkB;MAClBC,gBAAgB;MAChBC;IAEF,CAAC,GAAAO,IAAA;IADIN,MAAM,GAAAO,6BAAA,CAAAD,IAAA,EAAAE,SAAA;EAGX,IAAIZ,oBAAoB,IAAI,IAAI,IAAI,EAAEA,oBAAoB,YAAYa,GAAG,CAAC,EAAE;IAC1E,MAAM,IAAIJ,KAAK,CACb,2DACF,CAAC;EACH;EAEA,IACER,kBAAkB,IAAI,IAAI,IAC1B,EAAEA,kBAAkB,YAAYa,MAAM,CAAC,IACvCb,kBAAkB,KAAK,KAAK,EAC5B;IACA,MAAM,IAAIQ,KAAK,CACb,mEACF,CAAC;EACH;EAEA,IAAIP,gBAAgB,IAAI,IAAI,IAAI,OAAOA,gBAAgB,KAAK,SAAS,EAAE;IACrE,MAAM,IAAIO,KAAK,CACb,2DACF,CAAC;EACH;EAEA,IACEN,qBAAqB,IAAI,IAAI,IAC7B,OAAOA,qBAAqB,KAAK,SAAS,EAC1C;IACA,MAAM,IAAIM,KAAK,CACb,gEACF,CAAC;EACH;EACA,IACEN,qBAAqB,KAAK,IAAI,KAC7BH,oBAAoB,IAAI,IAAI,IAAIC,kBAAkB,IAAI,IAAI,CAAC,EAC5D;IACA,MAAM,IAAIQ,KAAK,CACb,qEAAqE,GACnE,sCACJ,CAAC;EACH;EAEA,OAAO;IACLL,MAAM;IACNJ,oBAAoB,EAAEA,oBAAoB,IAAIe,SAAS;IACvDd,kBAAkB,EAChBA,kBAAkB,IAAI,IAAI,GAAGc,SAAS,GAAGd,kBAAkB;IAC7DC,gBAAgB,EAAEA,gBAAgB,IAAI,IAAI,GAAGa,SAAS,GAAGb,gBAAgB;IACzEC,qBAAqB,EACnBA,qBAAqB,IAAI,IAAI,GAAGY,SAAS,GAAGZ;EAChD,CAAC;AACH;AAKO,SAASa,qBAAqBA,CACnCC,YAAqB,EACC;EACtB,IAAIC,KAAK,CAACC,OAAO,CAACF,YAAY,CAAC,EAAE;IAC/B,OAAOA,YAAY,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,WAAW,EAAEC,CAAC,KAAK;MAClDF,GAAG,CAAC,GAAG,GAAGE,CAAC,CAAC,GAAGD,WAAW;MAC1B,OAAOD,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EACR,CAAC,MAAM,IAAI,OAAOJ,YAAY,KAAK,QAAQ,IAAIA,YAAY,IAAI,IAAI,EAAE;IACnE,OAAQA,YAAY,IAAYF,SAAS;EAC3C;EAEA,MAAM,IAAIN,KAAK,CACb,oEACF,CAAC;AACH", "ignoreList": []}