<!-- <PERSON> Header -->
<div class="flex justify-between items-center mb-6">
    <div>
        <h1 class="text-2xl font-bold text-gray-800">Game Rooms</h1>
        <p class="text-gray-600">Monitor and manage multiplayer game rooms</p>
    </div>
    <div class="flex space-x-3">
        <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition duration-200 flex items-center">
            <i class="fas fa-sync mr-2"></i>
            Refresh
        </button>
        <button class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition duration-200 flex items-center">
            <i class="fas fa-trash mr-2"></i>
            Cleanup Inactive
        </button>
    </div>
</div>

<!-- Room Statistics -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
                <i class="fas fa-gamepad text-blue-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm text-gray-600">Total Rooms</p>
                <p class="text-2xl font-bold text-gray-800"><%= rooms.length %></p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
                <i class="fas fa-play text-green-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm text-gray-600">Active Games</p>
                <p class="text-2xl font-bold text-gray-800">
                    <%= rooms.filter(room => room.status === 'playing').length %>
                </p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
            <div class="p-2 bg-orange-100 rounded-lg">
                <i class="fas fa-users text-orange-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm text-gray-600">Total Players</p>
                <p class="text-2xl font-bold text-gray-800">
                    <%= rooms.reduce((sum, room) => sum + room.players.length, 0) %>
                </p>
            </div>
        </div>
    </div>
    
    <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
                <i class="fas fa-clock text-purple-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm text-gray-600">Waiting Rooms</p>
                <p class="text-2xl font-bold text-gray-800">
                    <%= rooms.filter(room => room.status === 'waiting').length %>
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="bg-white rounded-lg shadow-sm p-6 mb-6">
    <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
        <div class="flex space-x-3">
            <select class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" id="status-filter">
                <option value="">All Statuses</option>
                <option value="waiting">Waiting</option>
                <option value="playing">Playing</option>
                <option value="paused">Paused</option>
                <option value="finished">Finished</option>
            </select>
            <select class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" id="mode-filter">
                <option value="">All Modes</option>
                <option value="cooperative">Cooperative</option>
                <option value="competitive">Competitive</option>
                <option value="sandbox">Sandbox</option>
            </select>
        </div>
        <div class="flex space-x-3">
            <div class="flex items-center space-x-2">
                <input type="checkbox" id="private-filter" class="rounded">
                <label for="private-filter" class="text-sm text-gray-600">Include Private</label>
            </div>
        </div>
    </div>
</div>

<!-- Rooms Grid -->
<div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
    <% rooms.forEach(room => { %>
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200" data-status="<%= room.status %>" data-mode="<%= room.gameMode %>" data-private="<%= room.isPrivate %>">
        <!-- Room Header -->
        <div class="p-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                    <h3 class="text-lg font-semibold text-gray-800"><%= room.name %></h3>
                    <% if (room.isPrivate) { %>
                        <i class="fas fa-lock text-gray-400 text-sm" title="Private Room"></i>
                    <% } %>
                </div>
                <div class="flex items-center space-x-2">
                    <% if (room.status === 'waiting') { %>
                        <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">Waiting</span>
                    <% } else if (room.status === 'playing') { %>
                        <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">Playing</span>
                    <% } else if (room.status === 'paused') { %>
                        <span class="px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full">Paused</span>
                    <% } else { %>
                        <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full">Finished</span>
                    <% } %>
                </div>
            </div>
            <div class="mt-2 flex items-center space-x-4 text-sm text-gray-600">
                <span><i class="fas fa-gamepad mr-1"></i><%= room.gameMode %></span>
                <span><i class="fas fa-users mr-1"></i><%= room.players.length %>/<%= room.maxPlayers %></span>
                <% if (room.isPrivate) { %>
                    <span><i class="fas fa-key mr-1"></i><%= room.roomCode %></span>
                <% } %>
            </div>
        </div>

        <!-- Room Content -->
        <div class="p-4">
            <!-- Host Information -->
            <div class="mb-4">
                <h4 class="text-sm font-medium text-gray-700 mb-2">Host</h4>
                <div class="flex items-center space-x-2">
                    <div class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                        <span class="text-white text-xs font-medium">
                            <%= room.host.profile?.displayName?.charAt(0) || room.host.username.charAt(0) %>
                        </span>
                    </div>
                    <span class="text-sm text-gray-800">
                        <%= room.host.profile?.displayName || room.host.username %>
                    </span>
                </div>
            </div>

            <!-- Players List -->
            <div class="mb-4">
                <h4 class="text-sm font-medium text-gray-700 mb-2">Players</h4>
                <div class="space-y-1">
                    <% room.players.forEach(player => { %>
                    <div class="flex items-center space-x-2 text-sm">
                        <div class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-xs">
                                <%= player.profile?.displayName?.charAt(0) || player.username.charAt(0) %>
                            </span>
                        </div>
                        <span class="text-gray-700">
                            <%= player.profile?.displayName || player.username %>
                        </span>
                    </div>
                    <% }); %>
                </div>
            </div>

            <!-- Room Stats -->
            <div class="grid grid-cols-2 gap-4 mb-4 text-sm">
                <div>
                    <span class="text-gray-600">Created:</span>
                    <div class="font-medium"><%= new Date(room.createdAt).toLocaleDateString() %></div>
                </div>
                <div>
                    <span class="text-gray-600">Last Activity:</span>
                    <div class="font-medium"><%= new Date(room.lastActivity).toLocaleTimeString() %></div>
                </div>
            </div>

            <!-- Statistics -->
            <% if (room.statistics) { %>
            <div class="bg-gray-50 rounded-lg p-3 mb-4">
                <h5 class="text-xs font-medium text-gray-600 mb-2">Game Statistics</h5>
                <div class="grid grid-cols-2 gap-2 text-xs">
                    <div>
                        <span class="text-gray-500">Games:</span>
                        <span class="font-medium ml-1"><%= room.statistics.gamesPlayed %></span>
                    </div>
                    <div>
                        <span class="text-gray-500">Best Score:</span>
                        <span class="font-medium ml-1"><%= room.statistics.bestScore %></span>
                    </div>
                </div>
            </div>
            <% } %>
        </div>

        <!-- Room Actions -->
        <div class="px-4 py-3 bg-gray-50 border-t border-gray-200 flex justify-between">
            <div class="flex space-x-2">
                <button class="text-blue-600 hover:text-blue-800 text-sm" title="View Details">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="text-green-600 hover:text-green-800 text-sm" title="Monitor">
                    <i class="fas fa-desktop"></i>
                </button>
                <button class="text-orange-600 hover:text-orange-800 text-sm" title="Send Message">
                    <i class="fas fa-comment"></i>
                </button>
            </div>
            <div class="flex space-x-2">
                <% if (room.status === 'playing') { %>
                    <button class="text-orange-600 hover:text-orange-800 text-sm" title="Pause Game">
                        <i class="fas fa-pause"></i>
                    </button>
                <% } %>
                <button class="text-red-600 hover:text-red-800 text-sm" title="Close Room">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>
    <% }); %>
</div>

<% if (rooms.length === 0) { %>
<div class="text-center py-12">
    <i class="fas fa-gamepad text-gray-300 text-6xl mb-4"></i>
    <h3 class="text-lg font-medium text-gray-900 mb-2">No Active Rooms</h3>
    <p class="text-gray-500">There are currently no active game rooms.</p>
</div>
<% } %>

<script>
    // Filter functionality
    const statusFilter = document.getElementById('status-filter');
    const modeFilter = document.getElementById('mode-filter');
    const privateFilter = document.getElementById('private-filter');
    const roomCards = document.querySelectorAll('[data-status]');

    function filterRooms() {
        const statusValue = statusFilter.value;
        const modeValue = modeFilter.value;
        const includePrivate = privateFilter.checked;

        roomCards.forEach(card => {
            const status = card.dataset.status;
            const mode = card.dataset.mode;
            const isPrivate = card.dataset.private === 'true';

            let show = true;

            if (statusValue && status !== statusValue) show = false;
            if (modeValue && mode !== modeValue) show = false;
            if (!includePrivate && isPrivate) show = false;

            card.style.display = show ? 'block' : 'none';
        });
    }

    statusFilter.addEventListener('change', filterRooms);
    modeFilter.addEventListener('change', filterRooms);
    privateFilter.addEventListener('change', filterRooms);

    // Auto-refresh every 30 seconds
    setInterval(() => {
        location.reload();
    }, 30000);

    // Action buttons
    document.querySelectorAll('button[title]').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const action = this.title;
            const roomCard = this.closest('[data-status]');
            const roomName = roomCard.querySelector('h3').textContent;
            
            console.log(`${action} for room: ${roomName}`);
            // Implement actual actions here
        });
    });
</script>
