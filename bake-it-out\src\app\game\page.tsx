'use client'

import { useState } from 'react'
import { useLanguage } from '@/contexts/LanguageContext'
import { GameProvider, useGame } from '@/contexts/GameContext'
import { Bakery3D } from '@/components/game/Bakery3D'
import { CustomerManager } from '@/components/game/CustomerManager'
import { RecipeModal } from '@/components/game/RecipeModal'
import { ShopModal } from '@/components/game/ShopModal'
import { EquipmentShopModal } from '@/components/game/EquipmentShopModal'
import { GameToolbar } from '@/components/ui/GameToolbar'
import { ClientOnly } from '@/components/ui/ClientOnly'

function GameContent() {
  const { t } = useLanguage()
  const {
    player,
    equipment,
    inventory,
    orders,
    acceptOrder,
    completeOrder,
    declineOrder,
    generateNewOrder,
    spendMoney
  } = useGame()

  // 3D Scene State
  const [gameView, setGameView] = useState<'3d' | '3d-dining' | 'customer-manager'>('3d')
  
  // Modal States
  const [showRecipeModal, setShowRecipeModal] = useState(false)
  const [showShopModal, setShowShopModal] = useState(false)
  const [showEquipmentShopModal, setShowEquipmentShopModal] = useState(false)
  const [showCustomerManager, setShowCustomerManager] = useState(false)

  // Order handlers
  const handleOrderAccept = (orderId: string) => {
    acceptOrder(orderId)
  }

  const handleOrderDecline = (orderId: string) => {
    declineOrder(orderId)
  }

  const handleOrderComplete = (orderId: string) => {
    completeOrder(orderId)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-yellow-50">
      {/* Game Toolbar */}
      <GameToolbar />

      {/* 3D Scene Selector */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="text-center mb-4">
            <h2 className="text-2xl font-bold text-gray-800 mb-2">🎮 Immersive 3D Bakery Experience</h2>
            <p className="text-gray-600">Navigate your bakery in stunning 3D • Drag to rotate • Scroll to zoom • Click to interact</p>
          </div>
          
          <div className="flex justify-center space-x-4">
            <button
              onClick={() => setGameView('3d')}
              className={`px-8 py-4 rounded-xl font-medium transition-all duration-200 ${
                gameView === '3d'
                  ? 'bg-gradient-to-r from-orange-500 to-red-500 text-white shadow-xl transform scale-105'
                  : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300 hover:shadow-lg'
              }`}
            >
              🏪 {t('game.view.3d', '3D Kitchen')}
            </button>
            <button
              onClick={() => setGameView('3d-dining')}
              className={`px-8 py-4 rounded-xl font-medium transition-all duration-200 ${
                gameView === '3d-dining'
                  ? 'bg-gradient-to-r from-orange-500 to-red-500 text-white shadow-xl transform scale-105'
                  : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300 hover:shadow-lg'
              }`}
            >
              🍽️ {t('game.view.3d-dining', '3D Dining Room')}
            </button>
            <button
              onClick={() => setGameView('customer-manager')}
              className={`px-8 py-4 rounded-xl font-medium transition-all duration-200 ${
                gameView === 'customer-manager'
                  ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-xl transform scale-105'
                  : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300 hover:shadow-lg'
              }`}
            >
              👥 {t('game.view.customers', 'Customer Manager')}
            </button>
          </div>
        </div>
      </div>

      {/* Full 3D Experience */}
      <div className="w-full" style={{ height: 'calc(100vh - 200px)' }}>
        {gameView === '3d' && (
          <ClientOnly fallback={<div className="w-full h-full bg-gradient-to-br from-orange-100 to-yellow-100 flex items-center justify-center text-2xl font-bold text-orange-800">Loading 3D Kitchen...</div>}>
            <Bakery3D 
              orders={orders}
              onOrderAccept={handleOrderAccept}
              onOrderDecline={handleOrderDecline}
              onOrderComplete={handleOrderComplete}
              onGenerateNewOrder={generateNewOrder}
              onOpenIngredientShop={() => setShowShopModal(true)}
              onOpenRecipeBook={() => setShowRecipeModal(true)}
              onOpenEquipmentShop={() => setShowEquipmentShopModal(true)}
            />
          </ClientOnly>
        )}

        {gameView === '3d-dining' && (
          <div className="w-full h-full bg-gradient-to-br from-blue-100 to-purple-100 flex items-center justify-center">
            <div className="text-center">
              <h3 className="text-4xl font-bold mb-4 text-blue-800">🍽️ 3D Dining Room</h3>
              <p className="text-xl text-blue-600">Coming soon! The 3D dining room is being prepared.</p>
              <button
                onClick={() => setGameView('3d')}
                className="mt-6 px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                ← Back to 3D Kitchen
              </button>
            </div>
          </div>
        )}

        {gameView === 'customer-manager' && (
          <div className="w-full h-full bg-white">
            <CustomerManager
              onClose={() => setGameView('3d')}
            />
          </div>
        )}
      </div>

      {/* Modals */}
      <RecipeModal
        isOpen={showRecipeModal}
        onClose={() => setShowRecipeModal(false)}
      />

      <ShopModal
        isOpen={showShopModal}
        onClose={() => setShowShopModal(false)}
        onPurchase={(ingredient, quantity) => {
          // Handle ingredient purchase
          console.log('Purchase:', ingredient, quantity)
        }}
      />

      <EquipmentShopModal
        isOpen={showEquipmentShopModal}
        onClose={() => setShowEquipmentShopModal(false)}
        onPurchase={(equipment) => {
          // Handle equipment purchase
          console.log('Equipment purchase:', equipment)
        }}
      />
    </div>
  )
}

export default function GamePage() {
  return (
    <GameProvider>
      <GameContent />
    </GameProvider>
  )
}
