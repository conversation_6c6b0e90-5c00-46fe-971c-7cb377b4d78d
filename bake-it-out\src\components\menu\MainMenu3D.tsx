'use client'

import { <PERSON>vas, useFrame } from '@react-three/fiber'
import { OrbitControls, Environment, ContactShadows, Text, Box, Sphere, Cylinder, Float, Html, Stars, useGLTF } from '@react-three/drei'
import { Physics, useBox } from '@react-three/cannon'
import { Suspense, useRef, useState, useEffect } from 'react'
import { useLanguage } from '@/contexts/LanguageContext'
import { Button3D, FloatingPanel } from '@/components/game/Game3DWorld'
import * as THREE from 'three'
import { useRouter } from 'next/navigation'

// 3D Bakery Storefront
function BakeryStorefront() {
  const buildingRef = useRef<THREE.Group>(null)

  useFrame((state) => {
    if (buildingRef.current) {
      buildingRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.2) * 0.1
    }
  })

  return (
    <group ref={buildingRef} position={[0, 0, -5]}>
      {/* Main Building */}
      <Box args={[8, 6, 4]} position={[0, 3, 0]}>
        <meshStandardMaterial color="#DEB887" roughness={0.8} />
      </Box>
      
      {/* Roof */}
      <Box args={[9, 1, 5]} position={[0, 6.5, 0]}>
        <meshStandardMaterial color="#8B4513" />
      </Box>
      
      {/* Storefront Window */}
      <Box args={[6, 3, 0.2]} position={[0, 2, 2.1]}>
        <meshStandardMaterial color="#87CEEB" transparent opacity={0.7} />
      </Box>
      
      {/* Door */}
      <Box args={[1.5, 4, 0.3]} position={[2.5, 2, 2.1]}>
        <meshStandardMaterial color="#8B4513" />
      </Box>
      
      {/* Door Handle */}
      <Sphere args={[0.1]} position={[3, 2, 2.3]}>
        <meshStandardMaterial color="#FFD700" metalness={0.8} roughness={0.2} />
      </Sphere>
      
      {/* Bakery Sign */}
      <Float speed={1} rotationIntensity={0.1} floatIntensity={0.2}>
        <group position={[0, 5, 2.5]}>
          <Box args={[4, 1, 0.2]}>
            <meshStandardMaterial color="#FF6B35" />
          </Box>
          <Text
            position={[0, 0, 0.15]}
            fontSize={0.4}
            color="white"
            anchorX="center"
            anchorY="middle"

          >
            🧁 BAKE IT OUT
          </Text>
        </group>
      </Float>
      
      {/* Chimney with Smoke */}
      <Cylinder args={[0.5, 0.5, 2]} position={[-2, 7.5, -1]}>
        <meshStandardMaterial color="#696969" />
      </Cylinder>
      
      {/* Animated Smoke */}
      <Float speed={2} rotationIntensity={0.3} floatIntensity={0.5}>
        <Sphere args={[0.3]} position={[-2, 9, -1]}>
          <meshStandardMaterial color="#F5F5F5" transparent opacity={0.6} />
        </Sphere>
      </Float>
      <Float speed={1.5} rotationIntensity={0.2} floatIntensity={0.4}>
        <Sphere args={[0.4]} position={[-1.8, 10, -0.8]}>
          <meshStandardMaterial color="#F5F5F5" transparent opacity={0.4} />
        </Sphere>
      </Float>
      
      {/* Window Displays */}
      <group position={[-2, 2, 2.2]}>
        <Sphere args={[0.3]}>
          <meshStandardMaterial color="#DEB887" />
        </Sphere>
        <Text
          position={[0, -0.5, 0]}
          fontSize={0.2}
          color="#8B4513"
          anchorX="center"
          anchorY="middle"
        >
          🥖
        </Text>
      </group>
      
      <group position={[2, 2, 2.2]}>
        <Sphere args={[0.3]}>
          <meshStandardMaterial color="#FFD700" />
        </Sphere>
        <Text
          position={[0, -0.5, 0]}
          fontSize={0.2}
          color="#8B4513"
          anchorX="center"
          anchorY="middle"
        >
          🧁
        </Text>
      </group>
    </group>
  )
}

// Floating Menu Buttons
function MenuButtons({ onNavigate }: { onNavigate: (path: string) => void }) {
  const { t } = useLanguage()

  return (
    <group position={[6, 2, 0]}>
      <Float speed={1} rotationIntensity={0.1} floatIntensity={0.2}>
        <Button3D 
          position={[0, 3, 0]} 
          size={[3, 1, 0.4]}
          color="#4CAF50"
          hoverColor="#45a049"
          onClick={() => onNavigate('/game')}
        >
          🎮 {t('menu.play', 'Play Game')}
        </Button3D>
      </Float>
      
      <Float speed={1.2} rotationIntensity={0.1} floatIntensity={0.2}>
        <Button3D 
          position={[0, 1.5, 0]} 
          size={[3, 1, 0.4]}
          color="#2196F3"
          hoverColor="#1976D2"
          onClick={() => onNavigate('/multiplayer')}
        >
          👥 {t('menu.multiplayer', 'Multiplayer')}
        </Button3D>
      </Float>
      
      <Float speed={0.8} rotationIntensity={0.1} floatIntensity={0.2}>
        <Button3D 
          position={[0, 0, 0]} 
          size={[3, 1, 0.4]}
          color="#FF9800"
          hoverColor="#F57C00"
          onClick={() => onNavigate('/achievements')}
        >
          🏆 {t('menu.achievements', 'Achievements')}
        </Button3D>
      </Float>
      
      <Float speed={1.1} rotationIntensity={0.1} floatIntensity={0.2}>
        <Button3D 
          position={[0, -1.5, 0]} 
          size={[3, 1, 0.4]}
          color="#9C27B0"
          hoverColor="#7B1FA2"
          onClick={() => onNavigate('/settings')}
        >
          ⚙️ {t('menu.settings', 'Settings')}
        </Button3D>
      </Float>
    </group>
  )
}

// Floating Particles for Atmosphere
function MenuParticles() {
  const particlesRef = useRef<THREE.Group>(null)

  useFrame((state) => {
    if (particlesRef.current) {
      particlesRef.current.rotation.y = state.clock.elapsedTime * 0.1
    }
  })

  return (
    <group ref={particlesRef}>
      {Array.from({ length: 30 }).map((_, i) => (
        <Float key={i} speed={0.5 + Math.random()} rotationIntensity={0.3} floatIntensity={0.3}>
          <Sphere 
            args={[0.05]} 
            position={[
              (Math.random() - 0.5) * 20,
              Math.random() * 10,
              (Math.random() - 0.5) * 20
            ]}
          >
            <meshStandardMaterial 
              color={Math.random() > 0.5 ? "#FFD700" : "#FF6B35"} 
              emissive={Math.random() > 0.5 ? "#FFD700" : "#FF6B35"}
              emissiveIntensity={0.3}
              transparent
              opacity={0.7}
            />
          </Sphere>
        </Float>
      ))}
    </group>
  )
}

// Game Title
function GameTitle() {
  const { t } = useLanguage()
  const titleRef = useRef<THREE.Group>(null)

  useFrame((state) => {
    if (titleRef.current) {
      titleRef.current.position.y = 8 + Math.sin(state.clock.elapsedTime) * 0.2
    }
  })

  return (
    <group ref={titleRef} position={[0, 8, 0]}>
      <Float speed={1} rotationIntensity={0.1} floatIntensity={0.3}>
        <Text
          fontSize={1.5}
          color="#FF6B35"
          anchorX="center"
          anchorY="middle"

        >
          🧁 BAKE IT OUT
        </Text>
        
        <Text
          position={[0, -1, 0]}
          fontSize={0.5}
          color="#8B4513"
          anchorX="center"
          anchorY="middle"
        >
          {t('game.subtitle', 'The Ultimate Bakery Management Experience')}
        </Text>
      </Float>
    </group>
  )
}

// Main 3D Menu Component
export function MainMenu3D() {
  const { t } = useLanguage()
  const router = useRouter()
  const [showCredits, setShowCredits] = useState(false)

  const handleNavigate = (path: string) => {
    router.push(path)
  }

  return (
    <div className="w-full h-screen bg-gradient-to-b from-orange-200 via-yellow-100 to-orange-300">
      <Canvas
        camera={{ position: [0, 6, 10], fov: 75 }}
        shadows
        dpr={[1, 2]}
        gl={{ antialias: true, alpha: false }}
      >
        <Suspense fallback={null}>
          <Physics gravity={[0, -9.8, 0]}>
            {/* Lighting */}
            <ambientLight intensity={0.6} color="#FFF8DC" />
            <directionalLight
              position={[10, 10, 5]}
              intensity={1.2}
              castShadow
              shadow-mapSize-width={2048}
              shadow-mapSize-height={2048}
              color="#FFE4B5"
            />
            <pointLight position={[-5, 8, 5]} intensity={0.8} color="#FF6B35" />
            <pointLight position={[5, 8, 5]} intensity={0.8} color="#FFD700" />
            
            {/* Environment */}
            <Environment preset="sunset" background />
            <Stars radius={100} depth={50} count={500} factor={4} saturation={0} fade speed={0.5} />
            
            {/* Ground */}
            <Box args={[30, 0.2, 30]} position={[0, -0.1, 0]}>
              <meshStandardMaterial color="#90EE90" />
            </Box>
            
            {/* Game Title */}
            <GameTitle />
            
            {/* Bakery Storefront */}
            <BakeryStorefront />
            
            {/* Menu Buttons */}
            <MenuButtons onNavigate={handleNavigate} />
            
            {/* Atmospheric Particles */}
            <MenuParticles />
            
            {/* Credits Panel */}
            {showCredits && (
              <FloatingPanel
                position={[-6, 2, 0]}
                size={[4, 5]}
                title={t('menu.credits', 'Credits')}
                onClose={() => setShowCredits(false)}
              >
                <div className="text-center space-y-2">
                  <h3 className="font-bold text-lg">🧁 Bake It Out</h3>
                  <p className="text-sm">Created with ❤️</p>
                  <p className="text-xs">Built with React Three Fiber</p>
                  <p className="text-xs">& Next.js</p>
                </div>
              </FloatingPanel>
            )}
            
            {/* Credits Button */}
            <Float speed={0.7} rotationIntensity={0.1} floatIntensity={0.1}>
              <Button3D 
                position={[-8, -2, 0]} 
                size={[2, 0.8, 0.3]}
                color="#607D8B"
                hoverColor="#455A64"
                onClick={() => setShowCredits(true)}
              >
                ℹ️ {t('menu.credits', 'Credits')}
              </Button3D>
            </Float>
            
            {/* Ground Shadows */}
            <ContactShadows
              position={[0, 0, 0]}
              opacity={0.3}
              scale={30}
              blur={1}
              far={10}
              resolution={256}
              color="#000000"
            />
            
            {/* Camera Controls */}
            <OrbitControls
              enablePan={false}
              enableZoom={true}
              enableRotate={true}
              minDistance={8}
              maxDistance={20}
              minPolarAngle={Math.PI / 6}
              maxPolarAngle={Math.PI / 2.5}
              enableDamping={true}
              dampingFactor={0.05}
              autoRotate={true}
              autoRotateSpeed={0.5}
            />
          </Physics>
        </Suspense>
      </Canvas>
    </div>
  )
}
