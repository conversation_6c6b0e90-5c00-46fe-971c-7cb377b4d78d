{"version": 3, "names": ["_classApplyDescriptorGet", "require", "_assert<PERSON>lassBrand", "_classCheckPrivateStaticFieldDescriptor", "_classStaticPrivateFieldSpecGet", "receiver", "classConstructor", "descriptor", "assertClassBrand", "classCheckPrivateStaticFieldDescriptor", "classApplyDescriptorGet"], "sources": ["../../src/helpers/classStaticPrivateFieldSpecGet.js"], "sourcesContent": ["/* @minVersion 7.0.2 */\n/* @onlyBabel7 */\n\nimport classApplyDescriptorGet from \"classApplyDescriptorGet\";\nimport assertClassBrand from \"assertClassBrand\";\nimport classCheckPrivateStaticFieldDescriptor from \"classCheckPrivateStaticFieldDescriptor\";\nexport default function _classStaticPrivateFieldSpecGet(\n  receiver,\n  classConstructor,\n  descriptor,\n) {\n  assertClassBrand(classConstructor, receiver);\n  classCheckPrivateStaticFieldDescriptor(descriptor, \"get\");\n  return classApplyDescriptorGet(receiver, descriptor);\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,wBAAA,GAAAC,OAAA;AACA,IAAAC,iBAAA,GAAAD,OAAA;AACA,IAAAE,uCAAA,GAAAF,OAAA;AACe,SAASG,+BAA+BA,CACrDC,QAAQ,EACRC,gBAAgB,EAChBC,UAAU,EACV;EACAC,iBAAgB,CAACF,gBAAgB,EAAED,QAAQ,CAAC;EAC5CI,uCAAsC,CAACF,UAAU,EAAE,KAAK,CAAC;EACzD,OAAOG,wBAAuB,CAACL,QAAQ,EAAEE,UAAU,CAAC;AACtD", "ignoreList": []}