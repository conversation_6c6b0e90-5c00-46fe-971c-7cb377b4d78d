{"version": 3, "names": ["_index", "require", "removeComments", "node", "COMMENT_KEYS", "for<PERSON>ach", "key"], "sources": ["../../src/comments/removeComments.ts"], "sourcesContent": ["import { COMMENT_KEYS } from \"../constants/index.ts\";\nimport type * as t from \"../index.ts\";\n\n/**\n * Remove comment properties from a node.\n */\nexport default function removeComments<T extends t.Node>(node: T): T {\n  COMMENT_KEYS.forEach(key => {\n    node[key] = null;\n  });\n\n  return node;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAMe,SAASC,cAAcA,CAAmBC,IAAO,EAAK;EACnEC,mBAAY,CAACC,OAAO,CAACC,GAAG,IAAI;IAC1BH,IAAI,CAACG,GAAG,CAAC,GAAG,IAAI;EAClB,CAAC,CAAC;EAEF,OAAOH,IAAI;AACb", "ignoreList": []}