[{"/Users/<USER>/jdesboeufs/connect-mongo/src/index.ts": "1", "/Users/<USER>/jdesboeufs/connect-mongo/src/lib/MongoStore.spec.ts": "2", "/Users/<USER>/jdesboeufs/connect-mongo/src/lib/MongoStore.ts": "3", "/Users/<USER>/jdesboeufs/connect-mongo/src/test/integration.spec.ts": "4", "/Users/<USER>/jdesboeufs/connect-mongo/src/test/testHelper.ts": "5", "/Users/<USER>/jdesboeufs/connect-mongo/src/types/kruptein.d.ts": "6"}, {"size": 62, "mtime": 1697277963559, "results": "7", "hashOfConfig": "8"}, {"size": 10670, "mtime": 1697277963560, "results": "9", "hashOfConfig": "8"}, {"size": 15474, "mtime": 1697277963560, "results": "10", "hashOfConfig": "8"}, {"size": 2547, "mtime": 1697277963560, "results": "11", "hashOfConfig": "8"}, {"size": 1641, "mtime": 1697277963560, "results": "12", "hashOfConfig": "8"}, {"size": 1146, "mtime": 1697277963560, "results": "13", "hashOfConfig": "8"}, {"filePath": "14", "messages": "15", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "2sls2i", {"filePath": "16", "messages": "17", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "18", "messages": "19", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "20", "messages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "22", "messages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/jdesboeufs/connect-mongo/src/index.ts", [], "/Users/<USER>/jdesboeufs/connect-mongo/src/lib/MongoStore.spec.ts", [], "/Users/<USER>/jdesboeufs/connect-mongo/src/lib/MongoStore.ts", ["26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50", "51", "52"], "/Users/<USER>/jdesboeufs/connect-mongo/src/test/integration.spec.ts", ["53"], "/Users/<USER>/jdesboeufs/connect-mongo/src/test/testHelper.ts", [], "/Users/<USER>/jdesboeufs/connect-mongo/src/types/kruptein.d.ts", ["54"], {"ruleId": "55", "severity": 1, "message": "56", "line": 20, "column": 3, "nodeType": "57", "messageId": "58", "endLine": 20, "endColumn": 11}, {"ruleId": "55", "severity": 1, "message": "59", "line": 21, "column": 3, "nodeType": "57", "messageId": "58", "endLine": 21, "endColumn": 10}, {"ruleId": "55", "severity": 1, "message": "60", "line": 22, "column": 3, "nodeType": "57", "messageId": "58", "endLine": 22, "endColumn": 10}, {"ruleId": "61", "severity": 1, "message": "62", "line": 39, "column": 19, "nodeType": "63", "messageId": "64", "endLine": 39, "endColumn": 22, "suggestions": "65"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 39, "column": 27, "nodeType": "63", "messageId": "64", "endLine": 39, "endColumn": 30, "suggestions": "66"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 40, "column": 21, "nodeType": "63", "messageId": "64", "endLine": 40, "endColumn": 24, "suggestions": "67"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 40, "column": 29, "nodeType": "63", "messageId": "64", "endLine": 40, "endColumn": 32, "suggestions": "68"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 42, "column": 21, "nodeType": "63", "messageId": "64", "endLine": 42, "endColumn": 24, "suggestions": "69"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 42, "column": 29, "nodeType": "63", "messageId": "64", "endLine": 42, "endColumn": 32, "suggestions": "70"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 62, "column": 19, "nodeType": "63", "messageId": "64", "endLine": 62, "endColumn": 22, "suggestions": "71"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 62, "column": 27, "nodeType": "63", "messageId": "64", "endLine": 62, "endColumn": 30, "suggestions": "72"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 63, "column": 21, "nodeType": "63", "messageId": "64", "endLine": 63, "endColumn": 24, "suggestions": "73"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 63, "column": 29, "nodeType": "63", "messageId": "64", "endLine": 63, "endColumn": 32, "suggestions": "74"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 65, "column": 21, "nodeType": "63", "messageId": "64", "endLine": 65, "endColumn": 24, "suggestions": "75"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 65, "column": 29, "nodeType": "63", "messageId": "64", "endLine": 65, "endColumn": 32, "suggestions": "76"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 72, "column": 12, "nodeType": "63", "messageId": "64", "endLine": 72, "endColumn": 15, "suggestions": "77"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 134, "column": 20, "nodeType": "63", "messageId": "64", "endLine": 134, "endColumn": 23, "suggestions": "78"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 134, "column": 28, "nodeType": "63", "messageId": "64", "endLine": 134, "endColumn": 31, "suggestions": "79"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 135, "column": 22, "nodeType": "63", "messageId": "64", "endLine": 135, "endColumn": 25, "suggestions": "80"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 135, "column": 30, "nodeType": "63", "messageId": "64", "endLine": 135, "endColumn": 33, "suggestions": "81"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 301, "column": 21, "nodeType": "63", "messageId": "64", "endLine": 301, "endColumn": 24, "suggestions": "82"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 340, "column": 21, "nodeType": "63", "messageId": "64", "endLine": 340, "endColumn": 24, "suggestions": "83"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 407, "column": 21, "nodeType": "63", "messageId": "64", "endLine": 407, "endColumn": 24, "suggestions": "84"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 463, "column": 12, "nodeType": "63", "messageId": "64", "endLine": 463, "endColumn": 15, "suggestions": "85"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 499, "column": 40, "nodeType": "63", "messageId": "64", "endLine": 499, "endColumn": 43, "suggestions": "86"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 518, "column": 26, "nodeType": "63", "messageId": "64", "endLine": 518, "endColumn": 29, "suggestions": "87"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 530, "column": 25, "nodeType": "63", "messageId": "64", "endLine": 530, "endColumn": 28, "suggestions": "88"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 10, "column": 20, "nodeType": "63", "messageId": "64", "endLine": 10, "endColumn": 23, "suggestions": "89"}, {"ruleId": "61", "severity": 1, "message": "62", "line": 25, "column": 27, "nodeType": "63", "messageId": "64", "endLine": 25, "endColumn": 30, "suggestions": "90"}, "camelcase", "Identifier 'key_size' is not in camel case.", "Identifier", "notCamelCase", "Identifier 'iv_size' is not in camel case.", "Identifier 'at_size' is not in camel case.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["91", "92"], ["93", "94"], ["95", "96"], ["97", "98"], ["99", "100"], ["101", "102"], ["103", "104"], ["105", "106"], ["107", "108"], ["109", "110"], ["111", "112"], ["113", "114"], ["115", "116"], ["117", "118"], ["119", "120"], ["121", "122"], ["123", "124"], ["125", "126"], ["127", "128"], ["129", "130"], ["131", "132"], ["133", "134"], ["135", "136"], ["137", "138"], ["139", "140"], ["141", "142"], {"messageId": "143", "fix": "144", "desc": "145"}, {"messageId": "146", "fix": "147", "desc": "148"}, {"messageId": "143", "fix": "149", "desc": "145"}, {"messageId": "146", "fix": "150", "desc": "148"}, {"messageId": "143", "fix": "151", "desc": "145"}, {"messageId": "146", "fix": "152", "desc": "148"}, {"messageId": "143", "fix": "153", "desc": "145"}, {"messageId": "146", "fix": "154", "desc": "148"}, {"messageId": "143", "fix": "155", "desc": "145"}, {"messageId": "146", "fix": "156", "desc": "148"}, {"messageId": "143", "fix": "157", "desc": "145"}, {"messageId": "146", "fix": "158", "desc": "148"}, {"messageId": "143", "fix": "159", "desc": "145"}, {"messageId": "146", "fix": "160", "desc": "148"}, {"messageId": "143", "fix": "161", "desc": "145"}, {"messageId": "146", "fix": "162", "desc": "148"}, {"messageId": "143", "fix": "163", "desc": "145"}, {"messageId": "146", "fix": "164", "desc": "148"}, {"messageId": "143", "fix": "165", "desc": "145"}, {"messageId": "146", "fix": "166", "desc": "148"}, {"messageId": "143", "fix": "167", "desc": "145"}, {"messageId": "146", "fix": "168", "desc": "148"}, {"messageId": "143", "fix": "169", "desc": "145"}, {"messageId": "146", "fix": "170", "desc": "148"}, {"messageId": "143", "fix": "171", "desc": "145"}, {"messageId": "146", "fix": "172", "desc": "148"}, {"messageId": "143", "fix": "173", "desc": "145"}, {"messageId": "146", "fix": "174", "desc": "148"}, {"messageId": "143", "fix": "175", "desc": "145"}, {"messageId": "146", "fix": "176", "desc": "148"}, {"messageId": "143", "fix": "177", "desc": "145"}, {"messageId": "146", "fix": "178", "desc": "148"}, {"messageId": "143", "fix": "179", "desc": "145"}, {"messageId": "146", "fix": "180", "desc": "148"}, {"messageId": "143", "fix": "181", "desc": "145"}, {"messageId": "146", "fix": "182", "desc": "148"}, {"messageId": "143", "fix": "183", "desc": "145"}, {"messageId": "146", "fix": "184", "desc": "148"}, {"messageId": "143", "fix": "185", "desc": "145"}, {"messageId": "146", "fix": "186", "desc": "148"}, {"messageId": "143", "fix": "187", "desc": "145"}, {"messageId": "146", "fix": "188", "desc": "148"}, {"messageId": "143", "fix": "189", "desc": "145"}, {"messageId": "146", "fix": "190", "desc": "148"}, {"messageId": "143", "fix": "191", "desc": "145"}, {"messageId": "146", "fix": "192", "desc": "148"}, {"messageId": "143", "fix": "193", "desc": "145"}, {"messageId": "146", "fix": "194", "desc": "148"}, {"messageId": "143", "fix": "195", "desc": "145"}, {"messageId": "146", "fix": "196", "desc": "148"}, {"messageId": "143", "fix": "197", "desc": "145"}, {"messageId": "146", "fix": "198", "desc": "148"}, "suggestUnknown", {"range": "199", "text": "200"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "199", "text": "201"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "202", "text": "200"}, {"range": "202", "text": "201"}, {"range": "203", "text": "200"}, {"range": "203", "text": "201"}, {"range": "204", "text": "200"}, {"range": "204", "text": "201"}, {"range": "205", "text": "200"}, {"range": "205", "text": "201"}, {"range": "206", "text": "200"}, {"range": "206", "text": "201"}, {"range": "207", "text": "200"}, {"range": "207", "text": "201"}, {"range": "208", "text": "200"}, {"range": "208", "text": "201"}, {"range": "209", "text": "200"}, {"range": "209", "text": "201"}, {"range": "210", "text": "200"}, {"range": "210", "text": "201"}, {"range": "211", "text": "200"}, {"range": "211", "text": "201"}, {"range": "212", "text": "200"}, {"range": "212", "text": "201"}, {"range": "213", "text": "200"}, {"range": "213", "text": "201"}, {"range": "214", "text": "200"}, {"range": "214", "text": "201"}, {"range": "215", "text": "200"}, {"range": "215", "text": "201"}, {"range": "216", "text": "200"}, {"range": "216", "text": "201"}, {"range": "217", "text": "200"}, {"range": "217", "text": "201"}, {"range": "218", "text": "200"}, {"range": "218", "text": "201"}, {"range": "219", "text": "200"}, {"range": "219", "text": "201"}, {"range": "220", "text": "200"}, {"range": "220", "text": "201"}, {"range": "221", "text": "200"}, {"range": "221", "text": "201"}, {"range": "222", "text": "200"}, {"range": "222", "text": "201"}, {"range": "223", "text": "200"}, {"range": "223", "text": "201"}, {"range": "224", "text": "200"}, {"range": "224", "text": "201"}, {"range": "225", "text": "200"}, {"range": "225", "text": "201"}, {"range": "226", "text": "200"}, {"range": "226", "text": "201"}, [890, 893], "unknown", "never", [898, 901], [922, 925], [930, 933], [1001, 1004], [1009, 1012], [1503, 1506], [1511, 1514], [1535, 1538], [1543, 1546], [1614, 1617], [1622, 1625], [1743, 1746], [3399, 3402], [3407, 3410], [3432, 3435], [3440, 3443], [8018, 8021], [9222, 9225], [11439, 11442], [13268, 13271], [14300, 14303], [14788, 14791], [15114, 15117], [309, 312], [1000, 1003]]