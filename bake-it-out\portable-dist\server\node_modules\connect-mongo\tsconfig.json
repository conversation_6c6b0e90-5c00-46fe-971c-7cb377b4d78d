{
  "compilerOptions": {
    "incremental": true,
    "target": "es2018",
    "outDir": "build/main",
    "rootDir": "src",
    "moduleResolution": "node",
    "module": "commonjs",
    "declaration": true,
    "inlineSourceMap": true,
    "esModuleInterop": true /* Enables emit interoperability between CommonJS and ES Modules via creation of namespace objects for all imports. Implies 'allowSyntheticDefaultImports'. */,
    "resolveJsonModule": true /* Include modules imported with .json extension. */,

    "strict": true /* Enable all strict type-checking options. */,

    /* Strict Type-Checking Options */
    // "noImplicitAny": true /* Raise error on expressions and declarations with an implied 'any' type. */,
    // "strictNullChecks": true /* Enable strict null checks. */,
    "strictFunctionTypes": false /* Enable strict checking of function types. */,
    // "strictPropertyInitialization": true /* Enable strict checking of property initialization in classes. */,
    // "noImplicitThis": true /* Raise error on 'this' expressions with an implied 'any' type. */,
    // "alwaysStrict": true /* Parse in strict mode and emit "use strict" for each source file. */,

    /* Additional Checks */
    // FIXME: turn on these error
    "noUnusedLocals": false /* Report errors on unused locals. */,
    "noUnusedParameters": false /* Report errors on unused parameters. */,
    "noImplicitReturns": true /* Report error when not all code paths in function return a value. */,
    "noFallthroughCasesInSwitch": true /* Report errors for fallthrough cases in switch statement. */,

    /* Debugging Options */
    "traceResolution": false /* Report module resolution log messages. */,
    "listEmittedFiles": false /* Print names of generated files part of the compilation. */,
    "listFiles": false /* Print names of files part of the compilation. */,
    "pretty": true /* Stylize errors and messages using color and context. */,

    /* Experimental Options */
    // "experimentalDecorators": true /* Enables experimental support for ES7 decorators. */,
    // "emitDecoratorMetadata": true /* Enables experimental support for emitting type metadata for decorators. */,

    "lib": ["es2017"],
    "types": ["node"],
    "typeRoots": ["node_modules/@types", "src/types"]
  },
  "include": ["src/**/*.ts"],
  "exclude": ["node_modules/**", "example/**"],
  "compileOnSave": false
}
