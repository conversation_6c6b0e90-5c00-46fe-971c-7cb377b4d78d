@echo off
echo 🚀 Building Bake It Out Executable and Installer
echo ================================================

echo.
echo 📦 Step 1: Building Next.js application...
call npm run build
if %errorlevel% neq 0 (
    echo ❌ Failed to build Next.js application
    pause
    exit /b 1
)

echo.
echo ✅ Next.js build completed successfully!

echo.
echo 🔧 Step 2: Installing Electron dependencies...
call npm install electron-builder --save-dev
if %errorlevel% neq 0 (
    echo ⚠️ Warning: Could not install electron-builder, trying to continue...
)

echo.
echo 📱 Step 3: Building Windows executable...
call npx electron-builder --win --publish=never
if %errorlevel% neq 0 (
    echo ❌ Failed to build Windows executable
    echo.
    echo 💡 Troubleshooting tips:
    echo - Make sure no Electron processes are running
    echo - Try running as administrator
    echo - Check if antivirus is blocking the build
    pause
    exit /b 1
)

echo.
echo ✅ Windows executable built successfully!

echo.
echo 📦 Step 4: Building portable version...
call npx electron-builder --win --config.win.target=portable --publish=never
if %errorlevel% neq 0 (
    echo ⚠️ Warning: Could not build portable version
)

echo.
echo 🎉 Build process completed!
echo.
echo 📁 Output files are located in the 'dist' folder:
echo    - Installer: Bake It Out Setup.exe
echo    - Portable: Bake It Out.exe
echo.
echo 🧁 Your Bake It Out game is ready to distribute!

pause
