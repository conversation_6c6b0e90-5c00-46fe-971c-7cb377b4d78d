export default class PolarAreaController extends DatasetController {
    static id: string;
    /**
     * @type {any}
     */
    static overrides: any;
    constructor(chart: any, datasetIndex: any);
    innerRadius: number;
    outerRadius: number;
    getLabelAndValue(index: any): {
        label: any;
        value: string;
    };
    parseObjectData(meta: any, data: any, start: any, count: any): {
        r: unknown;
    }[];
    update(mode: any): void;
    /**
     * @protected
     */
    protected getMinMax(): {
        min: number;
        max: number;
    };
    /**
       * @private
       */
    private _updateRadius;
    countVisibleElements(): number;
    /**
       * @private
       */
    private _computeAngle;
}
import DatasetController from "../core/core.datasetController.js";
