@echo off
title Bake It Out - Browser Version

echo.
echo ===============================================
echo        BAKE IT OUT - Browser Version
echo ===============================================
echo.

REM Check if Node.js is available
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found!
    echo Please install Node.js from: https://nodejs.org/
    pause
    exit /b 1
)

REM Check if the build exists
if not exist "out\index.html" (
    echo Building the game...
    npm run build
    if %errorlevel% neq 0 (
        echo ERROR: Build failed
        echo Try running: npm install
        pause
        exit /b 1
    )
)

echo Starting browser version...
echo.
echo The game will be available at:
echo   http://localhost:3000
echo.
echo Press Ctrl+C to stop the server
echo.

REM Try different methods to serve the files
echo Trying to start server...

REM Method 1: Try npx serve
npx serve out -p 3000 2>nul
if %errorlevel% neq 0 (
    echo npx serve failed, trying Node.js server...

    REM Method 2: Use our custom Node.js server
    node serve-browser.js
    if %errorlevel% neq 0 (
        echo Node.js server failed, trying Python...

        REM Method 3: Try Python if available
        cd out
        python -m http.server 3000 2>nul
        if %errorlevel% neq 0 (
            echo All methods failed. Please install serve:
            echo npm install -g serve
            echo Then run: serve out -p 3000
        )
    )
)

echo.
echo Server stopped. Thanks for playing Bake It Out!
pause
