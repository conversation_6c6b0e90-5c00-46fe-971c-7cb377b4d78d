{
  "version": "0.2.0",
  "configurations": [
    // To debug, make sure a *.spec.ts file is active in the editor, then run a configuration
    {
      "type": "node",
      "request": "launch",
      "name": "Debug Active Spec",
      "runtimeExecutable": "${workspaceFolder}/node_modules/.bin/ava",
      "runtimeArgs": ["debug", "--break", "--serial", "${file}"],
      "port": 9229,
      "outputCapture": "std",
      "skipFiles": ["<node_internals>/**/*.js"],
      "preLaunchTask": "npm: build"
      // "smartStep": true
    },
    {
      // Use this one if you're already running `yarn watch`
      "type": "node",
      "request": "launch",
      "name": "Debug Active Spec (no build)",
      "runtimeExecutable": "${workspaceFolder}/node_modules/.bin/ava",
      "runtimeArgs": ["debug", "--break", "--serial", "${file}"],
      "port": 9229,
      "outputCapture": "std",
      "skipFiles": ["<node_internals>/**/*.js"]
      // "smartStep": true
    }]
}
