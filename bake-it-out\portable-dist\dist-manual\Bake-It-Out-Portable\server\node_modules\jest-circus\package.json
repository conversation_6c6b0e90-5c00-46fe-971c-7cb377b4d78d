{"name": "jest-circus", "version": "29.7.0", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-circus"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json", "./runner": "./runner.js"}, "dependencies": {"@jest/environment": "^29.7.0", "@jest/expect": "^29.7.0", "@jest/test-result": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "co": "^4.6.0", "dedent": "^1.0.0", "is-generator-fn": "^2.0.0", "jest-each": "^29.7.0", "jest-matcher-utils": "^29.7.0", "jest-message-util": "^29.7.0", "jest-runtime": "^29.7.0", "jest-snapshot": "^29.7.0", "jest-util": "^29.7.0", "p-limit": "^3.1.0", "pretty-format": "^29.7.0", "pure-rand": "^6.0.0", "slash": "^3.0.0", "stack-utils": "^2.0.3"}, "devDependencies": {"@babel/core": "^7.11.6", "@babel/register": "^7.0.0", "@types/co": "^4.6.2", "@types/graceful-fs": "^4.1.3", "@types/stack-utils": "^2.0.0", "execa": "^5.0.0", "graceful-fs": "^4.2.9", "tempy": "^1.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "4e56991693da7cd4c3730dc3579a1dd1403ee630"}