"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[81],{946:(e,s,a)=>{a.d(s,{w:()=>d});var t=a(5155),r=a(2115),n=a(9283),i=a(5084),l=a(3741);function c(e){let{isOpen:s,onClose:a,mode:c,onModeChange:d}=e,{t:o}=(0,n.o)(),{login:u,register:m,isLoading:x,error:h,clearError:g}=(0,i.y)(),[p,v]=(0,r.useState)({username:"",email:"",password:"",confirmPassword:""}),[b,y]=(0,r.useState)({});if(!s)return null;let f=async e=>{if(e.preventDefault(),(()=>{let e={};return p.username.trim()?p.username.length<3&&(e.username=o("cloud.auth.username_too_short","Username must be at least 3 characters")):e.username=o("cloud.auth.username_required","Username is required"),"register"===c&&(p.email.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(p.email)||(e.email=o("cloud.auth.email_invalid","Please enter a valid email address")):e.email=o("cloud.auth.email_required","Email is required"),p.password!==p.confirmPassword&&(e.confirmPassword=o("cloud.auth.passwords_dont_match","Passwords do not match"))),p.password?p.password.length<6&&(e.password=o("cloud.auth.password_too_short","Password must be at least 6 characters")):e.password=o("cloud.auth.password_required","Password is required"),y(e),0===Object.keys(e).length})()){g();try{("login"===c?await u(p.username,p.password):await m(p.username,p.email,p.password))&&(a(),v({username:"",email:"",password:"",confirmPassword:""}),y({}))}catch(e){console.error("Auth error:",e)}}},j=(e,s)=>{v(a=>({...a,[e]:s})),b[e]&&y(s=>({...s,[e]:""}))};return(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-800",children:"login"===c?o("cloud.auth.login_title","Login to Cloud Save"):o("cloud.auth.register_title","Create Cloud Save Account")}),(0,t.jsx)("button",{onClick:a,className:"text-gray-500 hover:text-gray-700 text-2xl",children:"\xd7"})]}),h&&(0,t.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:h}),(0,t.jsxs)("form",{onSubmit:f,className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:o("cloud.auth.username","Username")}),(0,t.jsx)("input",{type:"text",value:p.username,onChange:e=>j("username",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 ".concat(b.username?"border-red-500":"border-gray-300"),placeholder:o("cloud.auth.username_placeholder","Enter your username"),disabled:x}),b.username&&(0,t.jsx)("p",{className:"text-red-500 text-sm mt-1",children:b.username})]}),"register"===c&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:o("cloud.auth.email","Email")}),(0,t.jsx)("input",{type:"email",value:p.email,onChange:e=>j("email",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 ".concat(b.email?"border-red-500":"border-gray-300"),placeholder:o("cloud.auth.email_placeholder","Enter your email"),disabled:x}),b.email&&(0,t.jsx)("p",{className:"text-red-500 text-sm mt-1",children:b.email})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:o("cloud.auth.password","Password")}),(0,t.jsx)("input",{type:"password",value:p.password,onChange:e=>j("password",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 ".concat(b.password?"border-red-500":"border-gray-300"),placeholder:o("cloud.auth.password_placeholder","Enter your password"),disabled:x}),b.password&&(0,t.jsx)("p",{className:"text-red-500 text-sm mt-1",children:b.password})]}),"register"===c&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:o("cloud.auth.confirm_password","Confirm Password")}),(0,t.jsx)("input",{type:"password",value:p.confirmPassword,onChange:e=>j("confirmPassword",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 ".concat(b.confirmPassword?"border-red-500":"border-gray-300"),placeholder:o("cloud.auth.confirm_password_placeholder","Confirm your password"),disabled:x}),b.confirmPassword&&(0,t.jsx)("p",{className:"text-red-500 text-sm mt-1",children:b.confirmPassword})]}),(0,t.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,t.jsx)(l.$,{type:"submit",variant:"primary",className:"flex-1",disabled:x,children:x?o("cloud.auth.loading","Loading..."):"login"===c?o("cloud.auth.login_button","Login"):o("cloud.auth.register_button","Create Account")}),(0,t.jsx)(l.$,{type:"button",variant:"secondary",onClick:a,disabled:x,children:o("cloud.auth.cancel","Cancel")})]})]}),(0,t.jsx)("div",{className:"mt-6 text-center",children:(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["login"===c?o("cloud.auth.no_account","Don't have an account?"):o("cloud.auth.have_account","Already have an account?"),(0,t.jsx)("button",{onClick:()=>d("login"===c?"register":"login"),className:"ml-2 text-orange-600 hover:text-orange-800 font-medium",disabled:x,children:"login"===c?o("cloud.auth.register_link","Create one"):o("cloud.auth.login_link","Login")})]})})]})})}function d(e){let{isOpen:s,onClose:a,mode:d,currentGameData:o,onLoadGame:u}=e,{t:m}=(0,n.o)(),{isAuthenticated:x,user:h,cloudSaves:g,isLoadingSaves:p,saveToCloud:v,loadFromCloud:b,deleteCloudSave:y,refreshCloudSaves:f,syncStatus:j,lastSyncTime:N,error:w,clearError:k}=(0,i.y)(),[C,D]=(0,r.useState)(!1),[S,_]=(0,r.useState)("login"),[P,E]=(0,r.useState)(""),[L,A]=(0,r.useState)(null),[R,$]=(0,r.useState)(!1);if((0,r.useEffect)(()=>{s&&x&&f()},[s,x]),(0,r.useEffect)(()=>{if(w){let e=setTimeout(()=>{k()},5e3);return()=>clearTimeout(e)}},[w,k]),!s)return null;let F=async()=>{if(P.trim()&&o){$(!0);try{await v(P,o)&&(E(""),a())}catch(e){console.error("Save failed:",e)}finally{$(!1)}}},B=async()=>{if(L&&u){$(!0);try{let e=await b(L);e&&(u(e),a())}catch(e){console.error("Load failed:",e)}finally{$(!1)}}},M=async e=>{confirm(m("cloud.save.confirm_delete","Are you sure you want to delete this save?"))&&await y(e)&&L===e&&A(null)},T=e=>new Date(e).toLocaleString();return x?(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-xl p-6 w-full max-w-4xl mx-4 max-h-[90vh] overflow-hidden flex flex-col",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-800",children:"save"===d?m("cloud.save.save_title","Save to Cloud"):m("cloud.save.load_title","Load from Cloud")}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[m("cloud.save.logged_in_as","Logged in as"),": ",null==h?void 0:h.username]})]}),(0,t.jsx)("button",{onClick:a,className:"text-gray-500 hover:text-gray-700 text-2xl",children:"\xd7"})]}),w&&(0,t.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:w}),(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4 p-3 bg-gray-50 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full ".concat("syncing"===j?"bg-yellow-500 animate-pulse":"success"===j?"bg-green-500":"error"===j?"bg-red-500":"bg-gray-400")}),(0,t.jsxs)("span",{className:"text-sm text-gray-600",children:["syncing"===j&&m("cloud.save.syncing","Syncing..."),"success"===j&&m("cloud.save.sync_success","Synced"),"error"===j&&m("cloud.save.sync_error","Sync Error"),"idle"===j&&m("cloud.save.sync_idle","Ready")]})]}),N&&(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:[m("cloud.save.last_sync","Last sync"),": ",T(N.toISOString())]})]}),"save"===d&&(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:m("cloud.save.save_name","Save Name")}),(0,t.jsx)("input",{type:"text",value:P,onChange:e=>E(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500",placeholder:m("cloud.save.save_name_placeholder","Enter a name for your save"),maxLength:100})]}),(0,t.jsxs)("div",{className:"flex-1 overflow-hidden",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-800",children:[m("cloud.save.your_saves","Your Cloud Saves")," (",g.length,")"]}),(0,t.jsx)(l.$,{variant:"secondary",size:"sm",onClick:f,disabled:p,children:p?m("cloud.save.refreshing","Refreshing..."):m("cloud.save.refresh","Refresh")})]}),(0,t.jsx)("div",{className:"overflow-y-auto max-h-96 border border-gray-200 rounded-lg",children:p?(0,t.jsx)("div",{className:"p-8 text-center text-gray-500",children:m("cloud.save.loading_saves","Loading saves...")}):0===g.length?(0,t.jsx)("div",{className:"p-8 text-center text-gray-500",children:m("cloud.save.no_saves","No cloud saves found")}):(0,t.jsx)("div",{className:"divide-y divide-gray-200",children:g.map(e=>(0,t.jsx)("div",{className:"p-4 hover:bg-gray-50 cursor-pointer ".concat(L===e.id?"bg-orange-50 border-l-4 border-orange-500":""),onClick:()=>A(e.id),children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h4",{className:"font-medium text-gray-800",children:e.saveName}),(0,t.jsxs)("div",{className:"text-sm text-gray-600 mt-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("span",{children:[m("cloud.save.level","Level")," ",e.metadata.level]}),(0,t.jsxs)("span",{children:["$",e.metadata.money]}),(0,t.jsx)("span",{className:"px-2 py-1 rounded text-xs ".concat("manual"===e.saveType?"bg-blue-100 text-blue-800":"auto"===e.saveType?"bg-green-100 text-green-800":"bg-purple-100 text-purple-800"),children:e.saveType})]}),(0,t.jsxs)("div",{className:"mt-1 text-xs text-gray-500",children:[T(e.updatedAt)," • ",(e=>{if(0===e)return"0 B";let s=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,s)).toFixed(2))+" "+["B","KB","MB","GB"][s]})(e.size)]})]})]}),(0,t.jsx)("button",{onClick:s=>{s.stopPropagation(),M(e.id)},className:"text-red-500 hover:text-red-700 ml-4",title:m("cloud.save.delete","Delete"),children:"\uD83D\uDDD1️"})]})},e.id))})})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,t.jsx)(l.$,{variant:"secondary",onClick:a,disabled:R,children:m("cloud.save.cancel","Cancel")}),"save"===d?(0,t.jsx)(l.$,{variant:"primary",onClick:F,disabled:!P.trim()||R,children:R?m("cloud.save.saving","Saving..."):m("cloud.save.save_button","Save to Cloud")}):(0,t.jsx)(l.$,{variant:"primary",onClick:B,disabled:!L||R,children:R?m("cloud.save.loading","Loading..."):m("cloud.save.load_button","Load Game")})]})]})}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:m("cloud.save.auth_required","Cloud Save Account Required")}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:m("cloud.save.auth_description","You need to login or create an account to use cloud saves.")}),(0,t.jsxs)("div",{className:"flex space-x-3",children:[(0,t.jsx)(l.$,{variant:"primary",onClick:()=>{_("login"),D(!0)},className:"flex-1",children:m("cloud.auth.login","Login")}),(0,t.jsx)(l.$,{variant:"secondary",onClick:()=>{_("register"),D(!0)},className:"flex-1",children:m("cloud.auth.register","Register")})]}),(0,t.jsx)(l.$,{variant:"secondary",onClick:a,className:"w-full mt-3",children:m("cloud.auth.cancel","Cancel")})]})})}),(0,t.jsx)(c,{isOpen:C,onClose:()=>D(!1),mode:S,onModeChange:_})]})}},2163:(e,s,a)=>{a.d(s,{p:()=>i});var t=a(5155),r=a(3741),n=a(9283);function i(e){let{order:s,onAccept:a,onDecline:i,onComplete:l}=e,{t:c}=(0,n.o)();return(0,t.jsxs)("div",{className:"p-4 rounded-lg border ".concat((()=>{switch(s.status){case"pending":return"border-yellow-300 bg-yellow-50";case"accepted":case"in_progress":return"border-blue-300 bg-blue-50";case"completed":return"border-green-300 bg-green-50";case"failed":return"border-red-300 bg-red-50";default:return"border-gray-300 bg-gray-50"}})()),children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-lg",children:(()=>{let e=["\uD83D\uDC69","\uD83D\uDC68","\uD83D\uDC75","\uD83D\uDC74","\uD83D\uDC67","\uD83D\uDC66"],a=s.customerName.length%e.length;return e[a]})()}),(0,t.jsx)("h3",{className:"font-medium text-gray-800",children:s.customerName})]}),(0,t.jsx)("span",{className:"text-sm font-semibold text-green-600",children:c("orders.reward",{amount:s.reward.toString()})})]}),(0,t.jsx)("div",{className:"text-sm text-gray-600 mb-2",children:s.items.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:"\uD83E\uDDC1"}),(0,t.jsx)("span",{children:e})]},s))}),(0,t.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:["⏰ ",c("orders.time","Time"),": ",(e=>{let s=Math.floor(e/60);return"".concat(s,":").concat((e%60).toString().padStart(2,"0"))})(s.timeLimit)]}),(0,t.jsx)("div",{className:"text-xs",title:"Difficulty: ".concat(s.difficulty,"/5"),children:"⭐".repeat(s.difficulty)+"☆".repeat(5-s.difficulty)})]}),"pending"===s.status&&(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsxs)(r.$,{size:"sm",variant:"success",onClick:()=>a(s.id),className:"flex-1",children:["✅ ",c("orders.accept","Accept")]}),(0,t.jsxs)(r.$,{size:"sm",variant:"danger",onClick:()=>i(s.id),className:"flex-1",children:["❌ ",c("orders.reject","Reject")]})]}),"accepted"===s.status&&(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("div",{className:"text-blue-600 text-sm font-medium mb-2",children:["\uD83D\uDCCB ",c("orders.accepted","Order Accepted")]}),(0,t.jsxs)(r.$,{size:"sm",variant:"primary",onClick:()=>l&&l(s.id),className:"w-full",children:["\uD83C\uDFAF ",c("orders.complete")]})]}),"in_progress"===s.status&&(0,t.jsxs)("div",{className:"text-center text-orange-600 text-sm font-medium",children:["\uD83D\uDD04 ",c("orders.inProgress")]}),"completed"===s.status&&(0,t.jsx)("div",{className:"text-center text-green-600 text-sm font-medium",children:"✅ Completed!"}),"failed"===s.status&&(0,t.jsx)("div",{className:"text-center text-red-600 text-sm font-medium",children:"❌ Failed"})]})}},2785:(e,s,a)=>{a.d(s,{b:()=>u});var t=a(5155),r=a(2115),n=a(3741),i=a(9283),l=a(2148),c=a(5084),d=a(5877),o=a(946);function u(e){let{isOpen:s,onClose:a,settings:u,onSettingsChange:m}=e,{language:x,setLanguage:h,t:g}=(0,i.o)(),{isEnabled:p,isConnected:v,setEnabled:b}=(0,l.l)(),{isAuthenticated:y,user:f}=(0,c.y)(),[j,N]=(0,r.useState)("general"),[w,k]=(0,r.useState)(!1);if(!s)return null;let C=(e,s)=>{m({[e]:s})},D=e=>{h(e),C("language",e)},S=[{id:"general",name:g("settings.general")||"General",icon:"⚙️"},{id:"audio",name:g("settings.audio")||"Audio",icon:"\uD83D\uDD0A"},{id:"graphics",name:g("settings.graphics")||"Graphics",icon:"\uD83C\uDFA8"},{id:"save",name:g("settings.save")||"Save & Data",icon:"\uD83D\uDCBE"},{id:"discord",name:g("settings.discord")||"Discord",icon:"\uD83C\uDFAE"}];return(0,t.jsxs)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden",children:[(0,t.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:g("settings.title")||"⚙️ Settings"}),(0,t.jsx)(n.$,{variant:"secondary",onClick:a,children:g("game.close")||"✕ Close"})]})}),(0,t.jsx)("div",{className:"border-b border-gray-200",children:(0,t.jsx)("div",{className:"flex space-x-0",children:S.map(e=>(0,t.jsxs)("button",{onClick:()=>N(e.id),className:"px-4 py-3 font-medium text-sm border-b-2 transition-colors ".concat(j===e.id?"border-orange-500 text-orange-600 bg-orange-50":"border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50"),children:[e.icon," ",e.name]},e.id))})}),(0,t.jsxs)("div",{className:"p-6 max-h-[60vh] overflow-y-auto",children:["general"===j&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-800 mb-3",children:g("settings.language")||"\uD83C\uDF0D Language"}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(n.$,{variant:"en"===x?"primary":"secondary",size:"sm",onClick:()=>D("en"),children:"\uD83C\uDDFA\uD83C\uDDF8 English"}),(0,t.jsx)(n.$,{variant:"cs"===x?"primary":"secondary",size:"sm",onClick:()=>D("cs"),children:"\uD83C\uDDE8\uD83C\uDDFF Čeština"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-800 mb-3",children:g("settings.gameplay")||"\uD83C\uDFAE Gameplay"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("label",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{children:g("settings.notifications")||"Enable Notifications"}),(0,t.jsx)("input",{type:"checkbox",checked:u.notificationsEnabled,onChange:e=>C("notificationsEnabled",e.target.checked),className:"rounded"})]}),(0,t.jsxs)("label",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{children:g("settings.tutorials")||"Show Tutorials"}),(0,t.jsx)("input",{type:"checkbox",checked:u.showTutorials,onChange:e=>C("showTutorials",e.target.checked),className:"rounded"})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[g("settings.animationSpeed")||"Animation Speed",": ",u.animationSpeed,"x"]}),(0,t.jsx)("input",{type:"range",min:"0.5",max:"2",step:"0.1",value:u.animationSpeed,onChange:e=>C("animationSpeed",parseFloat(e.target.value)),className:"w-full"})]})]})]})]}),"audio"===j&&(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("label",{className:"flex items-center justify-between",children:[(0,t.jsxs)("span",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{children:"\uD83D\uDD0A"}),(0,t.jsx)("span",{children:g("settings.sound")||"Sound Effects"})]}),(0,t.jsx)("input",{type:"checkbox",checked:u.soundEnabled,onChange:e=>C("soundEnabled",e.target.checked),className:"rounded"})]}),(0,t.jsxs)("label",{className:"flex items-center justify-between",children:[(0,t.jsxs)("span",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{children:"\uD83C\uDFB5"}),(0,t.jsx)("span",{children:g("settings.music")||"Background Music"})]}),(0,t.jsx)("input",{type:"checkbox",checked:u.musicEnabled,onChange:e=>C("musicEnabled",e.target.checked),className:"rounded"})]})]})}),"graphics"===j&&(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-800 mb-3",children:g("settings.quality")||"\uD83C\uDFA8 Graphics Quality"}),(0,t.jsx)("div",{className:"space-x-2",children:["low","medium","high"].map(e=>(0,t.jsx)(n.$,{variant:u.graphicsQuality===e?"primary":"secondary",size:"sm",onClick:()=>C("graphicsQuality",e),children:e.charAt(0).toUpperCase()+e.slice(1)},e))})]})}),"save"===j&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-800 mb-3",children:g("settings.autoSave")||"\uD83D\uDCBE Auto-Save"}),(0,t.jsxs)("label",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{children:g("settings.enableAutoSave")||"Enable Auto-Save"}),(0,t.jsx)("input",{type:"checkbox",checked:u.autoSaveEnabled,onChange:e=>C("autoSaveEnabled",e.target.checked),className:"rounded"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-800 mb-3",children:g("settings.dataManagement")||"\uD83D\uDCC1 Data Management"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(n.$,{variant:"secondary",onClick:()=>{let e={version:"1.0.0",timestamp:Date.now(),player:{},equipment:[],inventory:[],achievements:[],skills:[],automationSettings:{},gameSettings:u,bakeries:[],currentBakeryId:"main"},s=new Blob([d.B.exportSave(e)],{type:"application/json"}),a=URL.createObjectURL(s),t=document.createElement("a");t.href=a,t.download="bake-it-out-save-".concat(Date.now(),".json"),t.click(),URL.revokeObjectURL(a)},className:"w-full",children:g("settings.exportSave")||"\uD83D\uDCE4 Export Save"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("input",{type:"file",accept:".json",onChange:e=>{var s;let a=null==(s=e.target.files)?void 0:s[0];if(!a)return;let t=new FileReader;t.onload=e=>{try{var s;let a=null==(s=e.target)?void 0:s.result,t=d.B.importSave(a);t?(console.log("Save imported successfully:",t),alert("Save imported successfully!")):alert("Failed to import save file")}catch(e){alert("Invalid save file")}},t.readAsText(a)},className:"hidden",id:"import-save"}),(0,t.jsx)(n.$,{variant:"secondary",onClick:()=>{var e;return null==(e=document.getElementById("import-save"))?void 0:e.click()},className:"w-full",children:g("settings.importSave")||"\uD83D\uDCE5 Import Save"})]})]})]}),(0,t.jsxs)("div",{className:"p-4 rounded-lg ".concat(y?"bg-green-50":"bg-blue-50"),children:[(0,t.jsx)("h4",{className:"font-medium mb-2 ".concat(y?"text-green-800":"text-blue-800"),children:g("settings.cloudSync")||"☁️ Cloud Sync"}),(0,t.jsx)("p",{className:"text-sm mb-3 ".concat(y?"text-green-700":"text-blue-700"),children:g("settings.cloudSyncDescription")||"Cloud sync allows you to save your progress online and play across multiple devices."}),y?(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"text-sm text-green-700",children:["✅ ",g("cloud.save.logged_in_as","Logged in as"),": ",(0,t.jsx)("strong",{children:null==f?void 0:f.username})]}),(0,t.jsx)("div",{className:"flex space-x-2",children:(0,t.jsx)(n.$,{variant:"primary",size:"sm",onClick:()=>k(!0),children:g("cloud.save.manage_saves","Manage Cloud Saves")})})]}):(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{className:"text-sm text-blue-700",children:g("cloud.save.login_required","Please login to use cloud saves")}),(0,t.jsx)(n.$,{variant:"primary",size:"sm",onClick:()=>k(!0),children:g("cloud.save.login","Login / Register")})]})]})]}),"discord"===j&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h3",{className:"font-semibold text-gray-800 mb-3",children:["\uD83C\uDFAE ",g("settings.discordRichPresence")||"Discord Rich Presence"]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:g("settings.discordDescription")||"Show your current game status and activity in Discord."}),(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium text-gray-800",children:g("settings.enableDiscordRPC")||"Enable Discord Rich Presence"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:v?g("settings.discordConnected")||"✅ Connected to Discord":g("settings.discordDisconnected")||"❌ Not connected to Discord"})]}),(0,t.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,t.jsx)("input",{type:"checkbox",checked:p,onChange:e=>b(e.target.checked),className:"sr-only peer"}),(0,t.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]})]}),(0,t.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,t.jsxs)("h4",{className:"font-medium text-blue-800 mb-2",children:["ℹ️ ",g("settings.discordInfo")||"What is Discord Rich Presence?"]}),(0,t.jsxs)("div",{className:"text-sm text-blue-700 space-y-2",children:[(0,t.jsx)("p",{children:g("settings.discordInfoDesc1")||"Discord Rich Presence shows your friends what you're doing in Bake It Out:"}),(0,t.jsxs)("ul",{className:"list-disc list-inside ml-4 space-y-1",children:[(0,t.jsx)("li",{children:g("settings.discordFeature1")||"Your current level and money"}),(0,t.jsx)("li",{children:g("settings.discordFeature2")||"What you're currently baking"}),(0,t.jsx)("li",{children:g("settings.discordFeature3")||"Multiplayer room information"}),(0,t.jsx)("li",{children:g("settings.discordFeature4")||"How long you've been playing"})]}),(0,t.jsx)("p",{className:"mt-2",children:g("settings.discordInfoDesc2")||"Your friends can even join your multiplayer games directly from Discord!"})]})]}),p&&!v&&(0,t.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[(0,t.jsxs)("h4",{className:"font-medium text-yellow-800 mb-2",children:["⚠️ ",g("settings.discordTroubleshooting")||"Discord Not Connected"]}),(0,t.jsxs)("div",{className:"text-sm text-yellow-700 space-y-2",children:[(0,t.jsx)("p",{children:g("settings.discordTrouble1")||"Make sure Discord is running on your computer."}),(0,t.jsx)("p",{children:g("settings.discordTrouble2")||"Discord Rich Presence only works in the desktop version of the game."}),(0,t.jsx)("p",{children:g("settings.discordTrouble3")||"Try restarting both Discord and the game if the connection fails."})]})]}),(0,t.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg",children:[(0,t.jsxs)("h4",{className:"font-medium text-green-800 mb-2",children:["\uD83D\uDD12 ",g("settings.discordPrivacy")||"Privacy Information"]}),(0,t.jsxs)("div",{className:"text-sm text-green-700 space-y-2",children:[(0,t.jsx)("p",{children:g("settings.discordPrivacyDesc1")||"Discord Rich Presence only shares:"}),(0,t.jsxs)("ul",{className:"list-disc list-inside ml-4 space-y-1",children:[(0,t.jsx)("li",{children:g("settings.discordPrivacy1")||"Your current game activity (public)"}),(0,t.jsx)("li",{children:g("settings.discordPrivacy2")||"Your player level and progress (public)"}),(0,t.jsx)("li",{children:g("settings.discordPrivacy3")||"Multiplayer room codes (for joining)"})]}),(0,t.jsx)("p",{className:"mt-2",children:g("settings.discordPrivacyDesc2")||"No personal information or save data is shared with Discord."})]})]})]})]})]}),w&&(0,t.jsx)(o.w,{isOpen:w,onClose:()=>k(!1),mode:"save",currentGameData:void 0,onLoadGame:()=>{}})]})}},3741:(e,s,a)=>{a.d(s,{$:()=>r});var t=a(5155);a(2115);let r=e=>{let{variant:s="primary",size:a="md",className:r="",children:n,...i}=e,l=["font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2",{primary:"bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500",secondary:"bg-gray-200 hover:bg-gray-300 text-gray-900 focus:ring-gray-500",danger:"bg-red-600 hover:bg-red-700 text-white focus:ring-red-500",success:"bg-green-600 hover:bg-green-700 text-white focus:ring-green-500"}[s],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-base",lg:"px-6 py-3 text-lg"}[a],r].join(" ");return(0,t.jsx)("button",{className:l,...i,children:n})}},9419:(e,s,a)=>{a.d(s,{$:()=>n});var t=a(5155),r=a(9283);function n(e){let{equipment:s,onClick:a}=e,{t:n}=(0,r.o)();return(0,t.jsx)("div",{className:"p-4 rounded-lg border-2 cursor-pointer transition-all ".concat(s.isActive?"border-green-400 bg-green-50":"border-gray-200 bg-gray-50 hover:border-orange-300 hover:bg-orange-50"),onClick:()=>!s.isActive&&a(s.id,s.name),children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-3xl mb-2",children:(e=>{switch(e){case"oven":return"\uD83D\uDD25";case"mixer":return"\uD83E\uDD44";case"counter":return"\uD83C\uDF7D️";default:return"⚙️"}})(s.type)}),(0,t.jsx)("h3",{className:"font-medium text-gray-800",children:n("equipment.".concat(s.name),s.name)}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:[n("game.level","Level")," ",s.level]}),s.isActive&&s.timeRemaining?(0,t.jsxs)("div",{className:"mt-2",children:[(0,t.jsx)("div",{className:"text-sm text-green-600",children:n("kitchen.making",{recipe:s.currentRecipe||""})}),(0,t.jsx)("div",{className:"text-lg font-mono text-green-700",children:(e=>{let s=Math.floor(e/60);return"".concat(s,":").concat((e%60).toString().padStart(2,"0"))})(s.timeRemaining)}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-2",children:(0,t.jsx)("div",{className:"bg-green-500 h-2 rounded-full transition-all duration-1000",style:{width:"".concat(100-s.timeRemaining/60*100,"%")}})})]}):(0,t.jsx)("div",{className:"text-sm text-gray-500 mt-2",children:s.isActive?n("equipment.status.active","Busy"):n("click_to_use","Click to use")})]})})}}}]);