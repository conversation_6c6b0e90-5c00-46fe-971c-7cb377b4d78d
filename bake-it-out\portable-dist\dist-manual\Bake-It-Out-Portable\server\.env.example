# ============================================
# 🧁 BAKE IT OUT - SERVER CONFIGURATION
# ============================================
# Copy this file to .env and customize as needed

# ============================================
# 🌐 SERVER SETTINGS
# ============================================
NODE_ENV=development
PORT=3001

# ============================================
# 💾 DATABASE SETTINGS
# ============================================
# Local MongoDB (default)
MONGODB_URI=mongodb://localhost:27017/bake-it-out

# Or use MongoDB Atlas (cloud):
# MONGODB_URI=mongodb+srv://username:<EMAIL>/bake-it-out

# Or use Docker MongoDB:
# MONGODB_URI=mongodb://localhost:27017/bake-it-out

# ============================================
# 🔐 SECURITY SETTINGS
# ============================================
# IMPORTANT: Change this secret in production!
JWT_SECRET=your-super-secret-jwt-key-change-in-production-make-it-very-long-and-random
JWT_EXPIRES_IN=7d
BCRYPT_ROUNDS=12

# ============================================
# 🌍 CORS SETTINGS
# ============================================
# Add your game client URLs here
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3002

# ============================================
# 🛡️ RATE LIMITING
# ============================================
# General API rate limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Authentication rate limiting
AUTH_RATE_LIMIT_MAX=10

# ============================================
# 📊 OPTIONAL SETTINGS
# ============================================
# Uncomment and configure as needed:

# File upload limits
# MAX_FILE_SIZE=10485760

# Session settings
# SESSION_SECRET=your-session-secret

# Email settings (for notifications)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-app-password

# ============================================
# 🚀 QUICK START TIPS
# ============================================
# 1. Run: npm run setup-easy (for automatic setup)
# 2. Or run: npm run quick-setup (for manual setup)
# 3. Start server: npm start
# 4. Open dashboard: http://localhost:3001/dashboard

# Logging
LOG_LEVEL=info

# File Upload (if needed)
MAX_FILE_SIZE=10485760

# Cloud Storage (optional)
# AWS_ACCESS_KEY_ID=your-aws-access-key
# AWS_SECRET_ACCESS_KEY=your-aws-secret-key
# AWS_REGION=us-east-1
# S3_BUCKET_NAME=bake-it-out-saves

# Email (optional, for notifications)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-app-password

# Redis (optional, for session storage)
# REDIS_URL=redis://localhost:6379

# Monitoring (optional)
# SENTRY_DSN=your-sentry-dsn
