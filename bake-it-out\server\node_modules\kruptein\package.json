{"name": "kruptein", "version": "3.0.8", "description": "crypto; from kruptein to hide or conceal", "keywords": ["crypto", "cryptography", "cryptr", "crypter", "encryption", "decryption", "encrypt", "decrypt", "AES", "GCM", "authenticated", "authenticate", "unicode", "symmetric", "cipher", "key derivation", "scrypt", "pbkdf2", "security", "asn.1"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jas-/kruptein.git"}, "bugs": {"url": "https://github.com/jas-/kruptein/issues"}, "devDependencies": {"expect.js": "0.3.1", "mocha": "11.5.0", "nyc": "17.1.0"}, "scripts": {"test": "nyc mocha test/*.js"}, "engines": {"node": ">8"}, "dependencies": {"asn1.js": "^5.4.1"}}