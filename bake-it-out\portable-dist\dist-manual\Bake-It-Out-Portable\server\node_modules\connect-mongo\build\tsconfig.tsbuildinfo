{"program": {"fileNames": ["../node_modules/typescript/lib/lib.es5.d.ts", "../node_modules/typescript/lib/lib.es2015.d.ts", "../node_modules/typescript/lib/lib.es2016.d.ts", "../node_modules/typescript/lib/lib.es2017.d.ts", "../node_modules/typescript/lib/lib.es2018.d.ts", "../node_modules/typescript/lib/lib.es2015.core.d.ts", "../node_modules/typescript/lib/lib.es2015.collection.d.ts", "../node_modules/typescript/lib/lib.es2015.generator.d.ts", "../node_modules/typescript/lib/lib.es2015.iterable.d.ts", "../node_modules/typescript/lib/lib.es2015.promise.d.ts", "../node_modules/typescript/lib/lib.es2015.proxy.d.ts", "../node_modules/typescript/lib/lib.es2015.reflect.d.ts", "../node_modules/typescript/lib/lib.es2015.symbol.d.ts", "../node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "../node_modules/typescript/lib/lib.es2016.array.include.d.ts", "../node_modules/typescript/lib/lib.es2017.object.d.ts", "../node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "../node_modules/typescript/lib/lib.es2017.string.d.ts", "../node_modules/typescript/lib/lib.es2017.intl.d.ts", "../node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "../node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "../node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "../node_modules/typescript/lib/lib.es2018.intl.d.ts", "../node_modules/typescript/lib/lib.es2018.promise.d.ts", "../node_modules/typescript/lib/lib.es2018.regexp.d.ts", "../node_modules/typescript/lib/lib.es2020.bigint.d.ts", "../node_modules/typescript/lib/lib.es2020.intl.d.ts", "../node_modules/typescript/lib/lib.esnext.intl.d.ts", "../node_modules/@types/node/assert.d.ts", "../node_modules/@types/node/globals.d.ts", "../node_modules/@types/node/async_hooks.d.ts", "../node_modules/@types/node/buffer.d.ts", "../node_modules/@types/node/child_process.d.ts", "../node_modules/@types/node/cluster.d.ts", "../node_modules/@types/node/console.d.ts", "../node_modules/@types/node/constants.d.ts", "../node_modules/@types/node/crypto.d.ts", "../node_modules/@types/node/dgram.d.ts", "../node_modules/@types/node/dns.d.ts", "../node_modules/@types/node/domain.d.ts", "../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/fs.d.ts", "../node_modules/@types/node/fs/promises.d.ts", "../node_modules/@types/node/http.d.ts", "../node_modules/@types/node/http2.d.ts", "../node_modules/@types/node/https.d.ts", "../node_modules/@types/node/inspector.d.ts", "../node_modules/@types/node/module.d.ts", "../node_modules/@types/node/net.d.ts", "../node_modules/@types/node/os.d.ts", "../node_modules/@types/node/path.d.ts", "../node_modules/@types/node/perf_hooks.d.ts", "../node_modules/@types/node/process.d.ts", "../node_modules/@types/node/punycode.d.ts", "../node_modules/@types/node/querystring.d.ts", "../node_modules/@types/node/readline.d.ts", "../node_modules/@types/node/repl.d.ts", "../node_modules/@types/node/stream.d.ts", "../node_modules/@types/node/string_decoder.d.ts", "../node_modules/@types/node/timers.d.ts", "../node_modules/@types/node/tls.d.ts", "../node_modules/@types/node/trace_events.d.ts", "../node_modules/@types/node/tty.d.ts", "../node_modules/@types/node/url.d.ts", "../node_modules/@types/node/util.d.ts", "../node_modules/@types/node/v8.d.ts", "../node_modules/@types/node/vm.d.ts", "../node_modules/@types/node/wasi.d.ts", "../node_modules/@types/node/worker_threads.d.ts", "../node_modules/@types/node/zlib.d.ts", "../node_modules/@types/node/globals.global.d.ts", "../node_modules/@types/node/index.d.ts", "../node_modules/@types/range-parser/index.d.ts", "../node_modules/@types/qs/index.d.ts", "../node_modules/@types/express-serve-static-core/index.d.ts", "../node_modules/@types/mime/mime.d.ts", "../node_modules/@types/mime/index.d.ts", "../node_modules/@types/serve-static/index.d.ts", "../node_modules/@types/connect/index.d.ts", "../node_modules/@types/body-parser/index.d.ts", "../node_modules/@types/express/index.d.ts", "../node_modules/@types/express-session/index.d.ts", "../node_modules/bson/bson.d.ts", "../node_modules/mongodb/mongodb.d.ts", "../node_modules/@types/ms/index.d.ts", "../node_modules/@types/debug/index.d.ts", "../src/lib/mongostore.ts", "../src/index.ts", "../node_modules/ava/index.d.ts", "../src/test/testhelper.ts", "../src/lib/mongostore.spec.ts", "../node_modules/@types/cookiejar/index.d.ts", "../node_modules/buffer/index.d.ts", "../node_modules/@types/superagent/index.d.ts", "../node_modules/@types/supertest/index.d.ts", "../src/test/integration.spec.ts", "../src/types/kruptein.d.ts"], "fileInfos": [{"version": "8730f4bf322026ff5229336391a18bcaa1f94d4f82416c8b2f3954e2ccaae2ba", "affectsGlobalScope": true}, "dc47c4fa66b9b9890cf076304de2a9c5201e94b740cffdf09f87296d877d71f6", "7a387c58583dfca701b6c85e0adaf43fb17d590fb16d5b2dc0a2fbd89f35c467", "8a12173c586e95f4433e0c6dc446bc88346be73ffe9ca6eec7aa63c8f3dca7f9", "5f4e733ced4e129482ae2186aae29fde948ab7182844c3a5a51dd346182c7b06", {"version": "adb996790133eb33b33aadb9c09f15c2c575e71fb57a62de8bf74dbf59ec7dfb", "affectsGlobalScope": true}, {"version": "8cc8c5a3bac513368b0157f3d8b31cfdcfe78b56d3724f30f80ed9715e404af8", "affectsGlobalScope": true}, {"version": "cdccba9a388c2ee3fd6ad4018c640a471a6c060e96f1232062223063b0a5ac6a", "affectsGlobalScope": true}, {"version": "c5c05907c02476e4bde6b7e76a79ffcd948aedd14b6a8f56e4674221b0417398", "affectsGlobalScope": true}, {"version": "5f406584aef28a331c36523df688ca3650288d14f39c5d2e555c95f0d2ff8f6f", "affectsGlobalScope": true}, {"version": "22f230e544b35349cfb3bd9110b6ef37b41c6d6c43c3314a31bd0d9652fcec72", "affectsGlobalScope": true}, {"version": "7ea0b55f6b315cf9ac2ad622b0a7813315bb6e97bf4bb3fbf8f8affbca7dc695", "affectsGlobalScope": true}, {"version": "3013574108c36fd3aaca79764002b3717da09725a36a6fc02eac386593110f93", "affectsGlobalScope": true}, {"version": "eb26de841c52236d8222f87e9e6a235332e0788af8c87a71e9e210314300410a", "affectsGlobalScope": true}, {"version": "3be5a1453daa63e031d266bf342f3943603873d890ab8b9ada95e22389389006", "affectsGlobalScope": true}, {"version": "17bb1fc99591b00515502d264fa55dc8370c45c5298f4a5c2083557dccba5a2a", "affectsGlobalScope": true}, {"version": "7ce9f0bde3307ca1f944119f6365f2d776d281a393b576a18a2f2893a2d75c98", "affectsGlobalScope": true}, {"version": "6a6b173e739a6a99629a8594bfb294cc7329bfb7b227f12e1f7c11bc163b8577", "affectsGlobalScope": true}, {"version": "81cac4cbc92c0c839c70f8ffb94eb61e2d32dc1c3cf6d95844ca099463cf37ea", "affectsGlobalScope": true}, {"version": "b0124885ef82641903d232172577f2ceb5d3e60aed4da1153bab4221e1f6dd4e", "affectsGlobalScope": true}, {"version": "0eb85d6c590b0d577919a79e0084fa1744c1beba6fd0d4e951432fa1ede5510a", "affectsGlobalScope": true}, {"version": "da233fc1c8a377ba9e0bed690a73c290d843c2c3d23a7bd7ec5cd3d7d73ba1e0", "affectsGlobalScope": true}, {"version": "d154ea5bb7f7f9001ed9153e876b2d5b8f5c2bb9ec02b3ae0d239ec769f1f2ae", "affectsGlobalScope": true}, {"version": "bb2d3fb05a1d2ffbca947cc7cbc95d23e1d053d6595391bd325deb265a18d36c", "affectsGlobalScope": true}, {"version": "c80df75850fea5caa2afe43b9949338ce4e2de086f91713e9af1a06f973872b8", "affectsGlobalScope": true}, {"version": "09aa50414b80c023553090e2f53827f007a301bc34b0495bfb2c3c08ab9ad1eb", "affectsGlobalScope": true}, {"version": "2768ef564cfc0689a1b76106c421a2909bdff0acbe87da010785adab80efdd5c", "affectsGlobalScope": true}, {"version": "52d1bb7ab7a3306fd0375c8bff560feed26ed676a5b0457fa8027b563aecb9a4", "affectsGlobalScope": true}, "4c2c4f53e8eedd970f8afa369d7371544fb6231bf95e659f8602e09abe74d5a5", {"version": "5540267409ab1444c73c786b8ae4caa37d5f0ea41f9255c6123338da790ce5cc", "affectsGlobalScope": true}, "c2b5085f47e41d6940bbc5b0d3bd7cc0037c752efb18aecd243c9cf83ad0c0b7", "3143a5add0467b83150961ecd33773b561a1207aec727002aa1d70333068eb1b", "9deb3a5eaf187df1428f0fee83c8c51eedb74f6da3442410bad9688e42a7e2b5", "d0fc76a91c828fbe3f0be5d683273634b7b101068333ceed975a8a9ac464137b", {"version": "1a048ff164b8d9609f5de3139d4e37f6e8a82af82087ac414b9208f52ef8aac7", "affectsGlobalScope": true}, "3111079f3cb5f2b9c812ca3f46161562bce5bfb355e915f46ed46c41714dc1c3", "db86f82fac051ae344b47e8fe7ac7990174b41db79b2b220a49dc5a47c71a9b5", "b32b6b16cb0bda68199582ad6f22242d07ee75fac9b1f28a98cd838afc5eea45", "4441ee4119824bfaebc49308559edd7545978f9cb41a40f115074e1031dde75f", {"version": "60693a88462d0e97900123b5bf7c73e146ce0cc94da46a61fe6775b430d2ff05", "affectsGlobalScope": true}, {"version": "588c69eda58b9202676ec7ca11a72c3762819b46a0ed72462c769846153c447c", "affectsGlobalScope": true}, "ae064ed4f855716b7ff348639ddcd6a6d354a72fae82f506608a7dc9266aa24c", "92f019c55b21c939616f6a48f678e714ac7b109444cbbf23ad69310ce66ecbdc", "cf0a69c71aedf2f8fe45925abd554fd3dc7301ce66d6ab7521fb8c3471c24dd8", "56e6722c6013609b3e5e6ed4a8a7e01f41da6c5e3d6f0ecff3d09ef7a81414cf", "139fd681eff7771a38d0c025d13c7a11c5474f6aab61e01c41511d71496df173", "f614c3f61e46ccc2cb58702d5a158338ea57ee09099fde5db4cfc63ed0ce4d74", "44e42ed6ec9c4451ebe89524e80ac8564e9dd0988c56e6c58f393c810730595d", "a504c109b872b0e653549bd258eb06584c148c98d79406c7516995865a6d5089", "155865f5f76db0996cd5e20cc5760613ea170ee5ad594c1f3d76fcaa05382161", "e92852d673c836fc64e10c38640abcd67c463456e5df55723ac699b8e6ab3a8a", "4455c78d226d061b1203c7614c6c6eb5f4f9db5f00d44ff47d0112de8766fbc4", {"version": "ec369bb9d97c4dc09dd2a4093b7ca3ba69ad284831fccac8a1977785e9e38ce5", "affectsGlobalScope": true}, "4465a636f5f6e9665a90e30691862c9e0a3ac2edc0e66296704f10865e924f2a", "9af781f03d44f5635ed7844be0ce370d9d595d4b4ec67cad88f0fac03255257e", "f9fd4c3ef6de27fa0e256f4e75b61711c4be05a3399f7714621d3edc832e36b0", "e49290b7a927995c0d7e6b2b9c8296284b68a9036d9966531de65185269258d7", "c3689f70ce7563c2299f2dcb3c72efdf6f87ae510e7456fa6223c767d0ca99fc", "874ca809b79276460011480a2829f4c8d4db29416dd411f71efbf8f497f0ac09", "6c903bceaf3f3bc04f2d4c7dcd89ce9fb148b3ba0a5f5408d8f6de2b7eecc7ea", "504d049d9e550a65466b73ca39da6469ab41786074ea1d16d37c8853f9f6ab2e", "23a28f834a078986bbf58f4e3705956983ff81c3c2493f3db3e5f0e8a9507779", "4febdf7f3ec92706c58e0b4e8159cd6de718284ef384260b07c9641c13fc70ce", {"version": "eabefc2999c1489cf870e0c85af908900462fa245822d9a4616780a1a129945d", "affectsGlobalScope": true}, "7335933d9f30dcfd2c4b6080a8b78e81912a7fcefb1dafccb67ca4cb4b3ac23d", "a6bfe9de9adef749010c118104b071d14943802ff0614732b47ce4f1c3e383cd", "4c3d0e10396646db4a1e917fb852077ee77ae62e512913bef9cccc2bb0f8bd0e", "3b220849d58140dcc6718f5b52dcd29fdb79c45bc28f561cbd29eb1cac6cce13", "0ee22fce41f7417a24c808d266e91b850629113c104713a35854393d55994beb", "22d1b1d965baba05766613e2e6c753bb005d4386c448cafd72c309ba689e8c24", {"version": "2708349d5a11a5c2e5f3a0765259ebe7ee00cdcc8161cb9990cb4910328442a1", "affectsGlobalScope": true}, "c6c0bd221bb1e94768e94218f8298e47633495529d60cae7d8da9374247a1cf5", "16d51f964ec125ad2024cf03f0af444b3bc3ec3614d9345cc54d09bab45c9a4c", "ba601641fac98c229ccd4a303f747de376d761babb33229bb7153bed9356c9cc", {"version": "c5dd1fef4cd4aaffc78786047bed5ae6fc1200d19a1946cbc4e2d3ed4d62c8fa", "affectsGlobalScope": true}, "5b9ecf7da4d71cf3832dbb8336150fa924631811f488ad4690c2dfec2b4fb1d7", "951c85f75aac041dddbedfedf565886a7b494e29ec1532e2a9b4a6180560b50e", "e6f0cb9d8cb2e38bec66e032e73caa3e7c6671f21ed7196acb821aec462051f2", "6d829824ead8999f87b6df21200df3c6150391b894b4e80662caa462bd48d073", "afc559c1b93df37c25aef6b3dfa2d64325b0e112e887ee18bf7e6f4ec383fc90", "43cdd474c5aa3340da4816bb8f1ae7f3b1bcf9e70d997afc36a0f2c432378c84", {"version": "ac95a3079883f7bde32a463e28af2c8e6d61d6220a1062c4e736df5215f92f77", "affectsGlobalScope": true}, "de3367f8a20cddb78a9405a7c8836c6e9c1c66d1c585e3aea1fa75237df73df6", "15c9f5021b7c9f4ca4674afa26d870dc05da9d7095f107546b92b1480fed436f", "6a9c5127096b35264eb7cd21b2417bfc1d42cceca9ba4ce2bb0c3410b7816042", "78828b06c0d3b586954015e9ebde5480b009e166c71244763bda328ec0920f41", {"version": "698fdb729cd894c34be04858cb4a5cd338bbaa64c4850f0446aea0bf0c440db5", "signature": "3852325cf6d9977f766e4624909b06b1c7345515d0c872c71c8d34a77be764a0"}, {"version": "3984e55a01cd37282bfc4caa7a8f2a78eb7e8ec7b47dd8cec3569d907d270cc3", "signature": "50d61d1b21fd4452d1b8a3e57eb55e982df4c5eaf9f8a597861e136d16094245"}, "0ceec7923fb2a6e540f73682c7acffe9124c9c3517a02eac27632844b8a61251", {"version": "c115e90807f74b9b380e94ad4a11ac4315114c920092aefcef1bd51e450cabed", "signature": "38fc4f9c48711d3d441e7caa53a42d85485e985f20dc1213170379ee178b8235"}, {"version": "e171f0cd13b54a9c2c2f399d1de747c9a2deeae9343edd4c3b3713eccd64b137", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, "8d48b8f8a377ade8dd1f000625bc276eea067f2529cc9cafdf082d17142107d6", "8e9c23ba78aabc2e0a27033f18737a6df754067731e69dc5f52823957d60a4b6", "29c5862cadd1c5e069453c60e8b240870431396921a50afc57bfdf5bdc614e47", "76232dbb982272b182a76ad8745a9b02724dc9896e2328ce360e2c56c64c9778", {"version": "a71026942d45c0177d37a40d1239023880767cba7cb57f15a1857ca26dff48c1", "signature": "a5bc006cbb01fe402cf0cdf8bff78d8b7c09a175faff0d575972d87f0c1aefb8"}, "77b390170976625bb91b4ba2eff90a17d6474a3b9e37121894bee56dcce72e07"], "options": {"declaration": true, "esModuleInterop": true, "inlineSourceMap": true, "module": 1, "noFallthroughCasesInSwitch": true, "noImplicitReturns": true, "noUnusedLocals": false, "noUnusedParameters": false, "outDir": "./main", "rootDir": "../src", "strict": true, "strictFunctionTypes": false, "target": 5}, "fileIdsList": [[44, 72, 79], [44, 72], [85], [41, 44, 72, 73, 74], [41, 81], [74, 75, 78, 80], [76], [77], [29], [31], [32, 37], [33, 41, 42, 49, 58], [33, 34, 41, 49], [35, 65], [36, 37, 42, 50], [37, 58], [38, 39, 41, 49], [39], [40, 41], [41], [41, 42, 43, 58, 64], [42, 43], [44, 49, 58, 64], [41, 42, 44, 45, 49, 58, 61, 64], [44, 46, 58, 61, 64], [29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71], [41, 47], [48, 64], [39, 41, 49, 58], [50], [51], [31, 52], [53, 63], [54], [55], [41, 56], [56, 57, 65, 67], [41, 58], [59], [60], [49, 58, 61], [62], [49, 63], [55, 64], [65], [58, 66], [67], [68], [41, 43, 58, 64, 67, 69], [58, 70], [44, 72, 77], [32, 42, 44, 58, 72, 92], [94], [39, 41, 49, 58, 61, 72, 83], [87], [82, 84, 87, 89, 90], [35, 65, 82, 84, 86, 97], [81, 82, 87, 88, 89, 95], [65, 82, 87], [82, 84], [82], [82, 87]], "referencedMap": [[80, 1], [79, 2], [86, 3], [75, 4], [82, 5], [81, 6], [77, 7], [76, 8], [29, 9], [31, 10], [32, 11], [33, 12], [34, 13], [35, 14], [36, 15], [37, 16], [38, 17], [39, 18], [40, 19], [41, 20], [42, 21], [43, 22], [44, 23], [45, 24], [46, 25], [72, 26], [47, 27], [48, 28], [49, 29], [50, 30], [51, 31], [52, 32], [53, 33], [54, 34], [55, 35], [56, 36], [57, 37], [58, 38], [59, 39], [60, 40], [61, 41], [62, 42], [63, 43], [64, 44], [65, 45], [66, 46], [67, 47], [68, 48], [69, 49], [70, 50], [78, 51], [94, 52], [95, 53], [84, 54], [88, 55], [91, 56], [87, 57], [96, 58], [90, 59]], "exportedModulesMap": [[80, 1], [79, 2], [86, 3], [75, 4], [82, 5], [81, 6], [77, 7], [76, 8], [29, 9], [31, 10], [32, 11], [33, 12], [34, 13], [35, 14], [36, 15], [37, 16], [38, 17], [39, 18], [40, 19], [41, 20], [42, 21], [43, 22], [44, 23], [45, 24], [46, 25], [72, 26], [47, 27], [48, 28], [49, 29], [50, 30], [51, 31], [52, 32], [53, 33], [54, 34], [55, 35], [56, 36], [57, 37], [58, 38], [59, 39], [60, 40], [61, 41], [62, 42], [63, 43], [64, 44], [65, 45], [66, 46], [67, 47], [68, 48], [69, 49], [70, 50], [78, 51], [94, 52], [95, 53], [84, 54], [88, 55], [87, 60], [96, 61], [90, 62]], "semanticDiagnosticsPerFile": [80, 79, 92, 86, 75, 82, 81, 77, 76, 85, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 30, 71, 44, 45, 46, 72, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 74, 73, 78, 94, 95, 89, 83, 93, 84, 7, 6, 2, 8, 9, 10, 11, 12, 13, 14, 15, 3, 4, 19, 16, 17, 18, 20, 21, 22, 5, 23, 24, 25, 26, 27, 1, 28, 88, 91, 87, 96, 90, 97]}, "version": "4.9.5"}