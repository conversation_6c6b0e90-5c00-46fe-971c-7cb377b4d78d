@echo off 
title Bake It Out - Starting... 
echo. 
echo ­čžü Welcome to Bake It Out! 
echo ========================== 
echo. 
echo ­čÜÇ Starting server... 
cd /d "%~dp0server" 
start /min "Bake It Out Server" cmd /c "npm install && npm start" 
echo. 
echo ÔÖ│ Waiting for server to start... 
timeout /t 8 /nobreak > nul 
echo. 
echo ­čÄ« Opening game in browser... 
start "" "http://localhost:3000" 
echo. 
echo ­čžü Bake It Out is now running! 
echo ­čöÇ Keep this window open while playing 
echo ­čöÇ Close this window to stop the game 
echo. 
pause 
