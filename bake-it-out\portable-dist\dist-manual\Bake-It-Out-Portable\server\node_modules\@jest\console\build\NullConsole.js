'use strict';

Object.defineProperty(exports, '__esModule', {
  value: true
});
exports.default = void 0;
var _CustomConsole = _interopRequireDefault(require('./CustomConsole'));
function _interopRequireDefault(obj) {
  return obj && obj.__esModule ? obj : {default: obj};
}
/* eslint-disable @typescript-eslint/no-empty-function */
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

class NullConsole extends _CustomConsole.default {
  assert() {}
  debug() {}
  dir() {}
  error() {}
  info() {}
  log() {}
  time() {}
  timeEnd() {}
  timeLog() {}
  trace() {}
  warn() {}
  group() {}
  groupCollapsed() {}
  groupEnd() {}
}
exports.default = NullConsole;
