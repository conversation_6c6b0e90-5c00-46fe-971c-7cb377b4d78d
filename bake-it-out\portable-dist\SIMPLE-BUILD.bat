@echo off
echo 🚀 Simple Build - Bake It Out Standalone
echo =========================================

echo.
echo 📦 Step 1: Building Next.js application...
call npm run build
if %errorlevel% neq 0 (
    echo ❌ Failed to build Next.js application
    pause
    exit /b 1
)

echo.
echo ✅ Next.js build completed!

echo.
echo 📱 Step 2: Creating standalone executable...

echo.
echo 🔧 Method 1: Trying Electron Builder (simple)...
call npx electron-builder --win --config.directories.output=dist-simple --config.win.target=portable --publish=never
if %errorlevel% equ 0 (
    echo ✅ Electron Builder succeeded!
    goto :success
)

echo.
echo ⚠️ Electron Builder failed, trying alternative method...

echo.
echo 🔧 Method 2: Creating manual package...
if not exist "dist-manual" mkdir "dist-manual"
if not exist "dist-manual\Bake-It-Out-Portable" mkdir "dist-manual\Bake-It-Out-Portable"

echo Copying application files...
xcopy /E /Y "out\*" "dist-manual\Bake-It-Out-Portable\app\"
xcopy /E /Y "electron\*" "dist-manual\Bake-It-Out-Portable\electron\"
xcopy /E /Y "server\*" "dist-manual\Bake-It-Out-Portable\server\"
copy "package.json" "dist-manual\Bake-It-Out-Portable\"

echo.
echo 📝 Creating launcher script...
echo @echo off > "dist-manual\Bake-It-Out-Portable\start-game.bat"
echo echo Starting Bake It Out... >> "dist-manual\Bake-It-Out-Portable\start-game.bat"
echo echo. >> "dist-manual\Bake-It-Out-Portable\start-game.bat"
echo echo 🚀 Starting server... >> "dist-manual\Bake-It-Out-Portable\start-game.bat"
echo start /min cmd /c "cd server && npm install && npm start" >> "dist-manual\Bake-It-Out-Portable\start-game.bat"
echo echo. >> "dist-manual\Bake-It-Out-Portable\start-game.bat"
echo echo ⏳ Waiting for server to start... >> "dist-manual\Bake-It-Out-Portable\start-game.bat"
echo timeout /t 5 /nobreak ^> nul >> "dist-manual\Bake-It-Out-Portable\start-game.bat"
echo echo. >> "dist-manual\Bake-It-Out-Portable\start-game.bat"
echo echo 🎮 Starting game... >> "dist-manual\Bake-It-Out-Portable\start-game.bat"
echo start "" "http://localhost:3000" >> "dist-manual\Bake-It-Out-Portable\start-game.bat"
echo echo. >> "dist-manual\Bake-It-Out-Portable\start-game.bat"
echo echo 🧁 Bake It Out is now running! >> "dist-manual\Bake-It-Out-Portable\start-game.bat"
echo echo Close this window to stop the game. >> "dist-manual\Bake-It-Out-Portable\start-game.bat"
echo pause >> "dist-manual\Bake-It-Out-Portable\start-game.bat"

echo.
echo 📝 Creating README...
echo # Bake It Out - Portable Version > "dist-manual\Bake-It-Out-Portable\README.txt"
echo. >> "dist-manual\Bake-It-Out-Portable\README.txt"
echo ## How to Run: >> "dist-manual\Bake-It-Out-Portable\README.txt"
echo 1. Double-click "start-game.bat" >> "dist-manual\Bake-It-Out-Portable\README.txt"
echo 2. Wait for the server to start >> "dist-manual\Bake-It-Out-Portable\README.txt"
echo 3. The game will open in your browser >> "dist-manual\Bake-It-Out-Portable\README.txt"
echo. >> "dist-manual\Bake-It-Out-Portable\README.txt"
echo ## Requirements: >> "dist-manual\Bake-It-Out-Portable\README.txt"
echo - Node.js installed on your system >> "dist-manual\Bake-It-Out-Portable\README.txt"
echo - Internet connection for first run >> "dist-manual\Bake-It-Out-Portable\README.txt"
echo. >> "dist-manual\Bake-It-Out-Portable\README.txt"
echo Enjoy baking! >> "dist-manual\Bake-It-Out-Portable\README.txt"

echo.
echo ✅ Manual package created successfully!

:success
echo.
echo 🎉 Build process completed!
echo.
echo 📁 Output locations:
if exist "dist" echo    - Electron Build: dist\
if exist "dist-simple" echo    - Simple Build: dist-simple\
if exist "dist-manual" echo    - Manual Package: dist-manual\Bake-It-Out-Portable\
echo.
echo 🧁 Your Bake It Out game is ready!

pause
