{"name": "write-file-atomic", "version": "4.0.2", "description": "Write files in an atomic fashion w/configurable ownership", "main": "./lib/index.js", "scripts": {"test": "tap", "posttest": "npm run lint", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "lintfix": "npm run lint -- --fix", "snap": "tap", "template-oss-apply": "template-oss-apply --force"}, "repository": {"type": "git", "url": "https://github.com/npm/write-file-atomic.git"}, "keywords": ["writeFile", "atomic"], "author": "GitHub Inc.", "license": "ISC", "bugs": {"url": "https://github.com/npm/write-file-atomic/issues"}, "homepage": "https://github.com/npm/write-file-atomic", "dependencies": {"imurmurhash": "^0.1.4", "signal-exit": "^3.0.7"}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "3.5.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "tap": "^16.0.1"}, "files": ["bin/", "lib/"], "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "windowsCI": false, "version": "3.5.0"}}