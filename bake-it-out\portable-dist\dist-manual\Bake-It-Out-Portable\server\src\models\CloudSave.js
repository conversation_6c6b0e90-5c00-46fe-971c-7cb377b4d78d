const mongoose = require('mongoose');

const cloudSaveSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  saveName: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  description: {
    type: String,
    maxlength: 500,
    default: ''
  },
  gameData: {
    type: mongoose.Schema.Types.Mixed,
    required: true,
    validate: {
      validator: function(data) {
        // Validate that gameData has required fields
        return data && 
               typeof data.player === 'object' &&
               Array.isArray(data.equipment) &&
               Array.isArray(data.inventory) &&
               Array.isArray(data.orders);
      },
      message: 'Invalid game data structure'
    }
  },
  metadata: {
    version: {
      type: Number,
      default: 1
    },
    gameVersion: {
      type: String,
      default: '1.0.0'
    },
    platform: {
      type: String,
      enum: ['web', 'desktop', 'mobile'],
      default: 'web'
    },
    deviceInfo: {
      type: String,
      maxlength: 200
    },
    playTime: {
      type: Number,
      default: 0
    },
    level: {
      type: Number,
      default: 1
    },
    money: {
      type: Number,
      default: 0
    }
  },
  saveType: {
    type: String,
    enum: ['manual', 'auto', 'checkpoint'],
    default: 'manual'
  },
  isActive: {
    type: Boolean,
    default: true
  },
  size: {
    type: Number,
    default: 0
  },
  checksum: {
    type: String,
    required: true
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      delete ret.__v;
      return ret;
    }
  }
});

// Compound indexes for efficient queries
cloudSaveSchema.index({ userId: 1, createdAt: -1 });
cloudSaveSchema.index({ userId: 1, saveType: 1, createdAt: -1 });
cloudSaveSchema.index({ userId: 1, isActive: 1, createdAt: -1 });

// Pre-save middleware to calculate size and checksum
cloudSaveSchema.pre('save', function(next) {
  try {
    const gameDataString = JSON.stringify(this.gameData);
    this.size = Buffer.byteLength(gameDataString, 'utf8');
    
    // Simple checksum calculation
    const crypto = require('crypto');
    this.checksum = crypto.createHash('md5').update(gameDataString).digest('hex');
    
    next();
  } catch (error) {
    next(error);
  }
});

// Static method to get user's saves
cloudSaveSchema.statics.getUserSaves = function(userId, options = {}) {
  const {
    limit = 20,
    skip = 0,
    saveType = null,
    activeOnly = true
  } = options;

  const query = { userId };
  if (saveType) query.saveType = saveType;
  if (activeOnly) query.isActive = true;

  return this.find(query)
    .sort({ createdAt: -1 })
    .limit(limit)
    .skip(skip)
    .select('-gameData'); // Exclude large gameData for listing
};

// Static method to get save with game data
cloudSaveSchema.statics.getSaveWithData = function(saveId, userId) {
  return this.findOne({ _id: saveId, userId, isActive: true });
};

// Instance method to create backup before update
cloudSaveSchema.methods.createBackup = function() {
  const BackupSave = this.constructor;
  const backup = new BackupSave({
    ...this.toObject(),
    _id: undefined,
    saveName: `${this.saveName} (Backup)`,
    saveType: 'auto',
    createdAt: new Date()
  });
  return backup.save();
};

// Instance method to validate game data integrity
cloudSaveSchema.methods.validateIntegrity = function() {
  try {
    const gameDataString = JSON.stringify(this.gameData);
    const crypto = require('crypto');
    const calculatedChecksum = crypto.createHash('md5').update(gameDataString).digest('hex');
    return calculatedChecksum === this.checksum;
  } catch (error) {
    return false;
  }
};

module.exports = mongoose.model('CloudSave', cloudSaveSchema);
