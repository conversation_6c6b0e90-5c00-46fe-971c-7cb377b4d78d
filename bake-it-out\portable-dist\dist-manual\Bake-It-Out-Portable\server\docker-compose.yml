version: '3.8'

services:
  # Bake It Out Server
  bake-it-out-server:
    build: .
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - MONGODB_URI=mongodb://mongo:27017/bake-it-out
      - JWT_SECRET=${JWT_SECRET:-your-super-secret-jwt-key-change-in-production}
      - ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3002
    depends_on:
      - mongo
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs
    networks:
      - bake-it-out-network

  # MongoDB Database
  mongo:
    image: mongo:6.0
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_DATABASE=bake-it-out
    volumes:
      - mongo_data:/data/db
      - ./mongo-init:/docker-entrypoint-initdb.d
    restart: unless-stopped
    networks:
      - bake-it-out-network

  # MongoDB Admin Interface (optional)
  mongo-express:
    image: mongo-express:latest
    ports:
      - "8081:8081"
    environment:
      - ME_CONFIG_MONGODB_SERVER=mongo
      - ME_CONFIG_MONGODB_PORT=27017
      - ME_CONFIG_BASICAUTH_USERNAME=admin
      - ME_CONFIG_BASICAUTH_PASSWORD=admin123
    depends_on:
      - mongo
    restart: unless-stopped
    networks:
      - bake-it-out-network
    profiles:
      - admin

  # Redis (optional, for caching and sessions)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - bake-it-out-network
    profiles:
      - cache

  # Nginx Reverse Proxy (optional)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - bake-it-out-server
    restart: unless-stopped
    networks:
      - bake-it-out-network
    profiles:
      - proxy

volumes:
  mongo_data:
  redis_data:

networks:
  bake-it-out-network:
    driver: bridge
