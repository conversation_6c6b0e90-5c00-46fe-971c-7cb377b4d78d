// Simple Socket.IO server for multiplayer functionality
const { createServer } = require('http')
const { Server } = require('socket.io')

const httpServer = createServer()
const io = new Server(httpServer, {
  cors: {
    origin: ["http://localhost:3000", "http://127.0.0.1:3000"],
    methods: ["GET", "POST"],
    credentials: true
  },
  transports: ['websocket', 'polling']
})

// In-memory storage for development (use Redis in production)
const rooms = new Map()
const players = new Map()

// Utility functions
function generateRoomId() {
  return Math.random().toString(36).substring(2, 8).toUpperCase()
}

function generatePlayerId() {
  return 'player_' + Math.random().toString(36).substring(2, 15)
}

function createRoom(roomData, hostPlayer) {
  const roomId = generateRoomId()
  const room = {
    id: roomId,
    name: roomData.name || `Room ${roomId}`,
    mode: roomData.mode || 'cooperative',
    maxPlayers: roomData.maxPlayers || 4,
    currentPlayers: 1,
    players: [hostPlayer],
    gameState: 'waiting',
    settings: {
      gameMode: roomData.gameMode || 'cooperative',
      timeLimit: roomData.timeLimit,
      targetScore: roomData.targetScore,
      difficulty: roomData.difficulty || 'medium',
      allowSpectators: roomData.allowSpectators || true
    },
    createdAt: Date.now(),
    gameData: {
      sharedResources: {
        inventory: {},
        orders: [],
        equipment: [],
        automationJobs: []
      },
      gameStats: {
        totalOrders: 0,
        totalRevenue: 0,
        totalExperience: 0,
        gameStartTime: Date.now()
      },
      events: []
    }
  }
  
  rooms.set(roomId, room)
  return room
}

function addPlayerToRoom(roomId, player) {
  const room = rooms.get(roomId)
  if (!room) return null
  
  if (room.currentPlayers >= room.maxPlayers) {
    return null // Room is full
  }
  
  room.players.push(player)
  room.currentPlayers++
  rooms.set(roomId, room)
  return room
}

function removePlayerFromRoom(roomId, playerId) {
  const room = rooms.get(roomId)
  if (!room) return null
  
  room.players = room.players.filter(p => p.id !== playerId)
  room.currentPlayers--
  
  if (room.currentPlayers === 0) {
    rooms.delete(roomId)
    return null
  }
  
  // If host left, assign new host
  if (!room.players.find(p => p.isHost)) {
    room.players[0].isHost = true
  }
  
  rooms.set(roomId, room)
  return room
}

function broadcastToRoom(roomId, event, data, excludeSocketId = null) {
  const room = rooms.get(roomId)
  if (!room) return
  
  room.players.forEach(player => {
    const playerSocket = players.get(player.id)
    if (playerSocket && playerSocket.id !== excludeSocketId) {
      playerSocket.emit(event, data)
    }
  })
}

// Socket.IO event handlers
io.on('connection', (socket) => {
  console.log(`Player connected: ${socket.id}`)
  
  // Room management
  socket.on('create_room', (roomData) => {
    try {
      const playerId = generatePlayerId()
      const hostPlayer = {
        id: playerId,
        name: roomData.hostName || 'Host',
        avatar: roomData.hostAvatar || '👨‍🍳',
        level: roomData.hostLevel || 1,
        isHost: true,
        isReady: false,
        socketId: socket.id
      }
      
      const room = createRoom(roomData, hostPlayer)
      players.set(playerId, socket)
      socket.playerId = playerId
      socket.roomId = room.id
      socket.join(room.id)
      
      socket.emit('room_created', room)
      console.log(`Room created: ${room.id} by ${hostPlayer.name}`)
    } catch (error) {
      socket.emit('error', { code: 'CREATE_ROOM_ERROR', message: error.message })
    }
  })
  
  socket.on('join_room', (roomId, playerData) => {
    try {
      const room = rooms.get(roomId)
      if (!room) {
        socket.emit('error', { code: 'ROOM_NOT_FOUND', message: 'Room not found' })
        return
      }
      
      if (room.currentPlayers >= room.maxPlayers) {
        socket.emit('error', { code: 'ROOM_FULL', message: 'Room is full' })
        return
      }
      
      const playerId = generatePlayerId()
      const player = {
        id: playerId,
        name: playerData.name || 'Player',
        avatar: playerData.avatar || '👨‍🍳',
        level: playerData.level || 1,
        isHost: false,
        isReady: false,
        socketId: socket.id
      }
      
      const updatedRoom = addPlayerToRoom(roomId, player)
      if (!updatedRoom) {
        socket.emit('error', { code: 'JOIN_ROOM_ERROR', message: 'Failed to join room' })
        return
      }
      
      players.set(playerId, socket)
      socket.playerId = playerId
      socket.roomId = roomId
      socket.join(roomId)
      
      socket.emit('room_joined', updatedRoom, player)
      broadcastToRoom(roomId, 'player_joined', player, socket.id)
      broadcastToRoom(roomId, 'room_updated', updatedRoom, socket.id)
      
      console.log(`Player ${player.name} joined room ${roomId}`)
    } catch (error) {
      socket.emit('error', { code: 'JOIN_ROOM_ERROR', message: error.message })
    }
  })
  
  socket.on('leave_room', (roomId) => {
    try {
      if (socket.playerId && socket.roomId) {
        const updatedRoom = removePlayerFromRoom(socket.roomId, socket.playerId)
        
        socket.leave(socket.roomId)
        players.delete(socket.playerId)
        
        if (updatedRoom) {
          broadcastToRoom(socket.roomId, 'player_left', socket.playerId)
          broadcastToRoom(socket.roomId, 'room_updated', updatedRoom)
        }
        
        socket.emit('room_left', socket.roomId)
        console.log(`Player ${socket.playerId} left room ${socket.roomId}`)
        
        socket.playerId = null
        socket.roomId = null
      }
    } catch (error) {
      socket.emit('error', { code: 'LEAVE_ROOM_ERROR', message: error.message })
    }
  })
  
  // Game state synchronization
  socket.on('player_action', (action) => {
    try {
      if (!socket.roomId || !socket.playerId) return
      
      const room = rooms.get(socket.roomId)
      if (!room) return
      
      // Add event to room history
      const event = {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        type: action.type,
        playerId: socket.playerId,
        data: action.data,
        timestamp: action.timestamp || Date.now()
      }
      
      room.gameData.events.push(event)
      
      // Keep only last 100 events
      if (room.gameData.events.length > 100) {
        room.gameData.events = room.gameData.events.slice(-100)
      }
      
      rooms.set(socket.roomId, room)
      
      // Broadcast action to all players in room
      broadcastToRoom(socket.roomId, 'player_action', action, socket.id)
      
      console.log(`Player action in room ${socket.roomId}:`, action.type)
    } catch (error) {
      socket.emit('error', { code: 'PLAYER_ACTION_ERROR', message: error.message })
    }
  })
  
  socket.on('game_state_update', (gameStateUpdate) => {
    try {
      if (!socket.roomId) return
      
      const room = rooms.get(socket.roomId)
      if (!room) return
      
      // Update room game data
      Object.assign(room.gameData, gameStateUpdate)
      rooms.set(socket.roomId, room)
      
      // Broadcast update to all players
      broadcastToRoom(socket.roomId, 'game_state_update', gameStateUpdate, socket.id)
    } catch (error) {
      socket.emit('error', { code: 'GAME_STATE_ERROR', message: error.message })
    }
  })
  
  // Chat system
  socket.on('send_message', (message) => {
    try {
      if (!socket.roomId) return
      
      broadcastToRoom(socket.roomId, 'message_received', message)
      console.log(`Message in room ${socket.roomId}: ${message.content}`)
    } catch (error) {
      socket.emit('error', { code: 'MESSAGE_ERROR', message: error.message })
    }
  })
  
  // Game control
  socket.on('game_start', (roomId) => {
    try {
      const room = rooms.get(roomId)
      if (!room) return
      
      // Only host can start game
      const player = room.players.find(p => p.socketId === socket.id)
      if (!player || !player.isHost) {
        socket.emit('error', { code: 'NOT_HOST', message: 'Only host can start the game' })
        return
      }
      
      room.gameState = 'playing'
      room.startedAt = Date.now()
      room.gameData.gameStats.gameStartTime = Date.now()
      rooms.set(roomId, room)
      
      broadcastToRoom(roomId, 'game_started', room.gameData)
      console.log(`Game started in room ${roomId}`)
    } catch (error) {
      socket.emit('error', { code: 'GAME_START_ERROR', message: error.message })
    }
  })
  
  // Handle disconnection
  socket.on('disconnect', (reason) => {
    console.log(`Player disconnected: ${socket.id}, reason: ${reason}`)
    
    if (socket.playerId && socket.roomId) {
      const updatedRoom = removePlayerFromRoom(socket.roomId, socket.playerId)
      players.delete(socket.playerId)
      
      if (updatedRoom) {
        broadcastToRoom(socket.roomId, 'player_left', socket.playerId)
        broadcastToRoom(socket.roomId, 'room_updated', updatedRoom)
      }
    }
  })
})

const PORT = process.env.PORT || 3001

httpServer.listen(PORT, () => {
  console.log(`🚀 Multiplayer server running on port ${PORT}`)
  console.log(`📡 Socket.IO server ready for connections`)
})

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully')
  httpServer.close(() => {
    console.log('Server closed')
    process.exit(0)
  })
})

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully')
  httpServer.close(() => {
    console.log('Server closed')
    process.exit(0)
  })
})
