(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[474],{927:(t,e,r)=>{"use strict";r.d(e,{Ao:()=>l,Bx:()=>s,Jh:()=>h,O4:()=>o,ZS:()=>i,fF:()=>c,iO:()=>n,ro:()=>a});var i,n,s,o,a,h,l,c,u="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},f={};(function(){var t,e,r,p="function"==typeof Object.defineProperties?Object.defineProperty:function(t,e,r){return t==Array.prototype||t==Object.prototype||(t[e]=r.value),t},d=function(t){t=["object"==typeof globalThis&&globalThis,t,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof u&&u];for(var e=0;e<t.length;++e){var r=t[e];if(r&&r.Math==Math)return r}throw Error("Cannot find global object")}(this);!function(t,e){if(e)t:{var r=d;t=t.split(".");for(var i=0;i<t.length-1;i++){var n=t[i];if(!(n in r))break t;r=r[n]}(e=e(i=r[t=t[t.length-1]]))!=i&&null!=e&&p(r,t,{configurable:!0,writable:!0,value:e})}}("Array.prototype.values",function(t){return t||function(){var t,e,r,i,n;return t=this,e=function(t,e){return e},t instanceof String&&(t+=""),r=0,i=!1,(n={next:function(){if(!i&&r<t.length){var n=r++;return{value:e(n,t[n]),done:!1}}return i=!0,{done:!0,value:void 0}}})[Symbol.iterator]=function(){return n},n}});var g=g||{},y=this||self;function m(t){var e=typeof t;return"array"==(e="object"!=e?e:t?Array.isArray(t)?"array":e:"null")||"object"==e&&"number"==typeof t.length}function b(t){var e=typeof t;return"object"==e&&null!=t||"function"==e}function v(t,e,r){return t.call.apply(t.bind,arguments)}function _(t,e,r){if(!t)throw Error();if(2<arguments.length){var i=Array.prototype.slice.call(arguments,2);return function(){var r=Array.prototype.slice.call(arguments);return Array.prototype.unshift.apply(r,i),t.apply(e,r)}}return function(){return t.apply(e,arguments)}}function w(t,e,r){return(w=Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?v:_).apply(null,arguments)}function E(t,e){var r=Array.prototype.slice.call(arguments,1);return function(){var e=r.slice();return e.push.apply(e,arguments),t.apply(this,e)}}function A(t,e){function r(){}r.prototype=e.prototype,t.aa=e.prototype,t.prototype=new r,t.prototype.constructor=t,t.Qb=function(t,r,i){for(var n=Array(arguments.length-2),s=2;s<arguments.length;s++)n[s-2]=arguments[s];return e.prototype[r].apply(t,n)}}function C(t){let e=t.length;if(0<e){let r=Array(e);for(let i=0;i<e;i++)r[i]=t[i];return r}return[]}function T(t,e){for(let e=1;e<arguments.length;e++){let r=arguments[e];if(m(r)){let e=t.length||0,i=r.length||0;t.length=e+i;for(let n=0;n<i;n++)t[e+n]=r[n]}else t.push(r)}}class k{constructor(t,e){this.i=t,this.j=e,this.h=0,this.g=null}get(){let t;return 0<this.h?(this.h--,t=this.g,this.g=t.next,t.next=null):t=this.i(),t}}function O(t){return/^[\s\xa0]*$/.test(t)}function S(){var t=y.navigator;return t&&(t=t.userAgent)?t:""}function x(t){return x[" "](t),t}x[" "]=function(){};var I=-1!=S().indexOf("Gecko")&&(-1==S().toLowerCase().indexOf("webkit")||-1!=S().indexOf("Edge"))&&-1==S().indexOf("Trident")&&-1==S().indexOf("MSIE")&&-1==S().indexOf("Edge");function R(t,e,r){for(let i in t)e.call(r,t[i],i,t)}function B(t){let e={};for(let r in t)e[r]=t[r];return e}let L="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");function D(t,e){let r,i;for(let e=1;e<arguments.length;e++){for(r in i=arguments[e])t[r]=i[r];for(let e=0;e<L.length;e++)r=L[e],Object.prototype.hasOwnProperty.call(i,r)&&(t[r]=i[r])}}class N{constructor(){this.h=this.g=null}add(t,e){let r=P.get();r.set(t,e),this.h?this.h.next=r:this.g=r,this.h=r}}var P=new k(()=>new U,t=>t.reset());class U{constructor(){this.next=this.g=this.h=null}set(t,e){this.h=t,this.g=e,this.next=null}reset(){this.next=this.g=this.h=null}}let j,M=!1,F=new N,H=()=>{let t=y.Promise.resolve(void 0);j=()=>{t.then(V)}};var V=()=>{let t;for(var e;t=null,F.g&&(t=F.g,F.g=F.g.next,F.g||(F.h=null),t.next=null),e=t;){try{e.h.call(e.g)}catch(t){!function(t){y.setTimeout(()=>{throw t},0)}(t)}P.j(e),100>P.h&&(P.h++,e.next=P.g,P.g=e)}M=!1};function q(){this.s=this.s,this.C=this.C}function z(t,e){this.type=t,this.g=this.target=e,this.defaultPrevented=!1}q.prototype.s=!1,q.prototype.ma=function(){this.s||(this.s=!0,this.N())},q.prototype.N=function(){if(this.C)for(;this.C.length;)this.C.shift()()},z.prototype.h=function(){this.defaultPrevented=!0};var $=function(){if(!y.addEventListener||!Object.defineProperty)return!1;var t=!1,e=Object.defineProperty({},"passive",{get:function(){t=!0}});try{let t=()=>{};y.addEventListener("test",t,e),y.removeEventListener("test",t,e)}catch(t){}return t}();function W(t,e){if(z.call(this,t?t.type:""),this.relatedTarget=this.g=this.target=null,this.button=this.screenY=this.screenX=this.clientY=this.clientX=0,this.key="",this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1,this.state=null,this.pointerId=0,this.pointerType="",this.i=null,t){var r=this.type=t.type,i=t.changedTouches&&t.changedTouches.length?t.changedTouches[0]:null;if(this.target=t.target||t.srcElement,this.g=e,e=t.relatedTarget){if(I){t:{try{x(e.nodeName);var n=!0;break t}catch(t){}n=!1}n||(e=null)}}else"mouseover"==r?e=t.fromElement:"mouseout"==r&&(e=t.toElement);this.relatedTarget=e,i?(this.clientX=void 0!==i.clientX?i.clientX:i.pageX,this.clientY=void 0!==i.clientY?i.clientY:i.pageY,this.screenX=i.screenX||0,this.screenY=i.screenY||0):(this.clientX=void 0!==t.clientX?t.clientX:t.pageX,this.clientY=void 0!==t.clientY?t.clientY:t.pageY,this.screenX=t.screenX||0,this.screenY=t.screenY||0),this.button=t.button,this.key=t.key||"",this.ctrlKey=t.ctrlKey,this.altKey=t.altKey,this.shiftKey=t.shiftKey,this.metaKey=t.metaKey,this.pointerId=t.pointerId||0,this.pointerType="string"==typeof t.pointerType?t.pointerType:K[t.pointerType]||"",this.state=t.state,this.i=t,t.defaultPrevented&&W.aa.h.call(this)}}A(W,z);var K={2:"touch",3:"pen",4:"mouse"};W.prototype.h=function(){W.aa.h.call(this);var t=this.i;t.preventDefault?t.preventDefault():t.returnValue=!1};var X="closure_listenable_"+(1e6*Math.random()|0),J=0;function Y(t,e,r,i,n){this.listener=t,this.proxy=null,this.src=e,this.type=r,this.capture=!!i,this.ha=n,this.key=++J,this.da=this.fa=!1}function G(t){t.da=!0,t.listener=null,t.proxy=null,t.src=null,t.ha=null}function Z(t){this.src=t,this.g={},this.h=0}function Q(t,e){var r=e.type;if(r in t.g){var i,n=t.g[r],s=Array.prototype.indexOf.call(n,e,void 0);(i=0<=s)&&Array.prototype.splice.call(n,s,1),i&&(G(e),0==t.g[r].length&&(delete t.g[r],t.h--))}}function tt(t,e,r,i){for(var n=0;n<t.length;++n){var s=t[n];if(!s.da&&s.listener==e&&!!r==s.capture&&s.ha==i)return n}return -1}Z.prototype.add=function(t,e,r,i,n){var s=t.toString();(t=this.g[s])||(t=this.g[s]=[],this.h++);var o=tt(t,e,i,n);return -1<o?(e=t[o],r||(e.fa=!1)):((e=new Y(e,this.src,s,!!i,n)).fa=r,t.push(e)),e};var te="closure_lm_"+(1e6*Math.random()|0),tr={};function ti(t,e,r,i,n,s){if(!e)throw Error("Invalid event type");var o=b(n)?!!n.capture:!!n,a=ta(t);if(a||(t[te]=a=new Z(t)),(r=a.add(e,r,i,o,s)).proxy)return r;if(i=function t(e){return to.call(t.src,t.listener,e)},r.proxy=i,i.src=t,i.listener=r,t.addEventListener)$||(n=o),void 0===n&&(n=!1),t.addEventListener(e.toString(),i,n);else if(t.attachEvent)t.attachEvent(ts(e.toString()),i);else if(t.addListener&&t.removeListener)t.addListener(i);else throw Error("addEventListener and attachEvent are unavailable.");return r}function tn(t){if("number"!=typeof t&&t&&!t.da){var e=t.src;if(e&&e[X])Q(e.i,t);else{var r=t.type,i=t.proxy;e.removeEventListener?e.removeEventListener(r,i,t.capture):e.detachEvent?e.detachEvent(ts(r),i):e.addListener&&e.removeListener&&e.removeListener(i),(r=ta(e))?(Q(r,t),0==r.h&&(r.src=null,e[te]=null)):G(t)}}}function ts(t){return t in tr?tr[t]:tr[t]="on"+t}function to(t,e){if(t.da)t=!0;else{e=new W(e,this);var r=t.listener,i=t.ha||t.src;t.fa&&tn(t),t=r.call(i,e)}return t}function ta(t){return(t=t[te])instanceof Z?t:null}var th="__closure_events_fn_"+(1e9*Math.random()>>>0);function tl(t){return"function"==typeof t?t:(t[th]||(t[th]=function(e){return t.handleEvent(e)}),t[th])}function tc(){q.call(this),this.i=new Z(this),this.M=this,this.F=null}function tu(t,e){var r,i=t.F;if(i)for(r=[];i;i=i.F)r.push(i);if(t=t.M,i=e.type||e,"string"==typeof e)e=new z(e,t);else if(e instanceof z)e.target=e.target||t;else{var n=e;D(e=new z(i,t),n)}if(n=!0,r)for(var s=r.length-1;0<=s;s--){var o=e.g=r[s];n=tf(o,i,!0,e)&&n}if(n=tf(o=e.g=t,i,!0,e)&&n,n=tf(o,i,!1,e)&&n,r)for(s=0;s<r.length;s++)n=tf(o=e.g=r[s],i,!1,e)&&n}function tf(t,e,r,i){if(!(e=t.i.g[String(e)]))return!0;e=e.concat();for(var n=!0,s=0;s<e.length;++s){var o=e[s];if(o&&!o.da&&o.capture==r){var a=o.listener,h=o.ha||o.src;o.fa&&Q(t.i,o),n=!1!==a.call(h,i)&&n}}return n&&!i.defaultPrevented}function tp(t,e,r){if("function"==typeof t)r&&(t=w(t,r));else if(t&&"function"==typeof t.handleEvent)t=w(t.handleEvent,t);else throw Error("Invalid listener argument");return 0x7fffffff<Number(e)?-1:y.setTimeout(t,e||0)}A(tc,q),tc.prototype[X]=!0,tc.prototype.removeEventListener=function(t,e,r,i){!function t(e,r,i,n,s){if(Array.isArray(r))for(var o=0;o<r.length;o++)t(e,r[o],i,n,s);else(n=b(n)?!!n.capture:!!n,i=tl(i),e&&e[X])?(e=e.i,(r=String(r).toString())in e.g&&-1<(i=tt(o=e.g[r],i,n,s))&&(G(o[i]),Array.prototype.splice.call(o,i,1),0==o.length&&(delete e.g[r],e.h--))):e&&(e=ta(e))&&(r=e.g[r.toString()],e=-1,r&&(e=tt(r,i,n,s)),(i=-1<e?r[e]:null)&&tn(i))}(this,t,e,r,i)},tc.prototype.N=function(){if(tc.aa.N.call(this),this.i){var t,e=this.i;for(t in e.g){for(var r=e.g[t],i=0;i<r.length;i++)G(r[i]);delete e.g[t],e.h--}}this.F=null},tc.prototype.K=function(t,e,r,i){return this.i.add(String(t),e,!1,r,i)},tc.prototype.L=function(t,e,r,i){return this.i.add(String(t),e,!0,r,i)};class td extends q{constructor(t,e){super(),this.m=t,this.l=e,this.h=null,this.i=!1,this.g=null}j(t){this.h=arguments,this.g?this.i=!0:function t(e){e.g=tp(()=>{e.g=null,e.i&&(e.i=!1,t(e))},e.l);let r=e.h;e.h=null,e.m.apply(null,r)}(this)}N(){super.N(),this.g&&(y.clearTimeout(this.g),this.g=null,this.i=!1,this.h=null)}}function tg(t){q.call(this),this.h=t,this.g={}}A(tg,q);var ty=[];function tm(t){R(t.g,function(t,e){this.g.hasOwnProperty(e)&&tn(t)},t),t.g={}}tg.prototype.N=function(){tg.aa.N.call(this),tm(this)},tg.prototype.handleEvent=function(){throw Error("EventHandler.handleEvent not implemented")};var tb=y.JSON.stringify,tv=y.JSON.parse,t_=class{stringify(t){return y.JSON.stringify(t,void 0)}parse(t){return y.JSON.parse(t,void 0)}};function tw(){}function tE(t){return t.h||(t.h=t.i())}function tA(){}tw.prototype.h=null;var tC={OPEN:"a",kb:"b",Ja:"c",wb:"d"};function tT(){z.call(this,"d")}function tk(){z.call(this,"c")}A(tT,z),A(tk,z);var tO={},tS=null;function tx(){return tS=tS||new tc}function tI(t){z.call(this,tO.La,t)}function tR(t){let e=tx();tu(e,new tI(e))}function tB(t,e){z.call(this,tO.STAT_EVENT,t),this.stat=e}function tL(t){let e=tx();tu(e,new tB(e,t))}function tD(t,e){z.call(this,tO.Ma,t),this.size=e}function tN(t,e){if("function"!=typeof t)throw Error("Fn must not be null and must be a function");return y.setTimeout(function(){t()},e)}function tP(){this.g=!0}function tU(t,e,r,i){t.info(function(){return"XMLHTTP TEXT ("+e+"): "+function(t,e){if(!t.g)return e;if(!e)return null;try{var r=JSON.parse(e);if(r){for(t=0;t<r.length;t++)if(Array.isArray(r[t])){var i=r[t];if(!(2>i.length)){var n=i[1];if(Array.isArray(n)&&!(1>n.length)){var s=n[0];if("noop"!=s&&"stop"!=s&&"close"!=s)for(var o=1;o<n.length;o++)n[o]=""}}}}return tb(r)}catch(t){return e}}(t,r)+(i?" "+i:"")})}tO.La="serverreachability",A(tI,z),tO.STAT_EVENT="statevent",A(tB,z),tO.Ma="timingevent",A(tD,z),tP.prototype.xa=function(){this.g=!1},tP.prototype.info=function(){};var tj={NO_ERROR:0,gb:1,tb:2,sb:3,nb:4,rb:5,ub:6,Ia:7,TIMEOUT:8,xb:9},tM={lb:"complete",Hb:"success",Ja:"error",Ia:"abort",zb:"ready",Ab:"readystatechange",TIMEOUT:"timeout",vb:"incrementaldata",yb:"progress",ob:"downloadprogress",Pb:"uploadprogress"};function tF(){}function tH(t,e,r,i){this.j=t,this.i=e,this.l=r,this.R=i||1,this.U=new tg(this),this.I=45e3,this.H=null,this.o=!1,this.m=this.A=this.v=this.L=this.F=this.S=this.B=null,this.D=[],this.g=null,this.C=0,this.s=this.u=null,this.X=-1,this.J=!1,this.O=0,this.M=null,this.W=this.K=this.T=this.P=!1,this.h=new tV}function tV(){this.i=null,this.g="",this.h=!1}A(tF,tw),tF.prototype.g=function(){return new XMLHttpRequest},tF.prototype.i=function(){return{}},e=new tF;var tq={},tz={};function t$(t,e,r){t.L=1,t.v=eo(ee(e)),t.m=r,t.P=!0,tW(t,null)}function tW(t,e){t.F=Date.now(),tX(t),t.A=ee(t.v);var r,i,n,s,o,a,h=t.A,l=t.R;Array.isArray(l)||(l=[String(l)]),ev(h.i,"t",l),t.C=0,h=t.j.J,t.h=new tV,t.g=e5(t.j,h?e:null,!t.m),0<t.O&&(t.M=new td(w(t.Y,t,t.g),t.O)),e=t.U,h=t.g,l=t.ca;var c="readystatechange";Array.isArray(c)||(c&&(ty[0]=c.toString()),c=ty);for(var u=0;u<c.length;u++){var f=function t(e,r,i,n,s){if(n&&n.once)return function t(e,r,i,n,s){if(Array.isArray(r)){for(var o=0;o<r.length;o++)t(e,r[o],i,n,s);return null}return i=tl(i),e&&e[X]?e.L(r,i,b(n)?!!n.capture:!!n,s):ti(e,r,i,!0,n,s)}(e,r,i,n,s);if(Array.isArray(r)){for(var o=0;o<r.length;o++)t(e,r[o],i,n,s);return null}return i=tl(i),e&&e[X]?e.K(r,i,b(n)?!!n.capture:!!n,s):ti(e,r,i,!1,n,s)}(h,c[u],l||e.handleEvent,!1,e.h||e);if(!f)break;e.g[f.key]=f}e=t.H?B(t.H):{},t.m?(t.u||(t.u="POST"),e["Content-Type"]="application/x-www-form-urlencoded",t.g.ea(t.A,t.u,t.m,e)):(t.u="GET",t.g.ea(t.A,t.u,null,e)),tR(),r=t.i,i=t.u,n=t.A,s=t.l,o=t.R,a=t.m,r.info(function(){if(r.g)if(a)for(var t="",e=a.split("&"),h=0;h<e.length;h++){var l=e[h].split("=");if(1<l.length){var c=l[0];l=l[1];var u=c.split("_");t=2<=u.length&&"type"==u[1]?t+(c+"=")+l+"&":t+(c+"=redacted&")}}else t=null;else t=a;return"XMLHTTP REQ ("+s+") [attempt "+o+"]: "+i+"\n"+n+"\n"+t})}function tK(t){return!!t.g&&"GET"==t.u&&2!=t.L&&t.j.Ca}function tX(t){t.S=Date.now()+t.I,tJ(t,t.I)}function tJ(t,e){if(null!=t.B)throw Error("WatchDog timer not null");t.B=tN(w(t.ba,t),e)}function tY(t){t.B&&(y.clearTimeout(t.B),t.B=null)}function tG(t){0==t.j.G||t.J||e0(t.j,t)}function tZ(t){tY(t);var e=t.M;e&&"function"==typeof e.ma&&e.ma(),t.M=null,tm(t.U),t.g&&(e=t.g,t.g=null,e.abort(),e.ma())}function tQ(t,e){try{var r=t.j;if(0!=r.G&&(r.g==t||t3(r.h,t))){if(!t.K&&t3(r.h,t)&&3==r.G){try{var i=r.Da.g.parse(e)}catch(t){i=null}if(Array.isArray(i)&&3==i.length){var n=i;if(0==n[0]){t:if(!r.u){if(r.g)if(r.g.F+3e3<t.F)eQ(r),eq(r);else break t;eY(r),tL(18)}}else r.za=n[1],0<r.za-r.T&&37500>n[2]&&r.F&&0==r.v&&!r.C&&(r.C=tN(w(r.Za,r),6e3));if(1>=t6(r.h)&&r.ca){try{r.ca()}catch(t){}r.ca=void 0}}else e2(r,11)}else if((t.K||r.g==t)&&eQ(r),!O(e))for(n=r.Da.g.parse(e),e=0;e<n.length;e++){let a=n[e];if(r.T=a[0],a=a[1],2==r.G)if("c"==a[0]){r.K=a[1],r.ia=a[2];let e=a[3];null!=e&&(r.la=e,r.j.info("VER="+r.la));let n=a[4];null!=n&&(r.Aa=n,r.j.info("SVER="+r.Aa));let h=a[5];null!=h&&"number"==typeof h&&0<h&&(r.L=i=1.5*h,r.j.info("backChannelRequestTimeoutMs_="+i)),i=r;let l=t.g;if(l){let t=l.g?l.g.getResponseHeader("X-Client-Wire-Protocol"):null;if(t){var s=i.h;s.g||-1==t.indexOf("spdy")&&-1==t.indexOf("quic")&&-1==t.indexOf("h2")||(s.j=s.l,s.g=new Set,s.h&&(t5(s,s.h),s.h=null))}if(i.D){let t=l.g?l.g.getResponseHeader("X-HTTP-Session-Id"):null;t&&(i.ya=t,es(i.I,i.D,t))}}if(r.G=3,r.l&&r.l.ua(),r.ba&&(r.R=Date.now()-t.F,r.j.info("Handshake RTT: "+r.R+"ms")),(i=r).qa=e3(i,i.J?i.ia:null,i.W),t.K){t4(i.h,t);var o=i.L;o&&(t.I=o),t.B&&(tY(t),tX(t)),i.g=t}else eJ(i);0<r.i.length&&e$(r)}else"stop"!=a[0]&&"close"!=a[0]||e2(r,7);else 3==r.G&&("stop"==a[0]||"close"==a[0]?"stop"==a[0]?e2(r,7):eV(r):"noop"!=a[0]&&r.l&&r.l.ta(a),r.v=0)}}tR(4)}catch(t){}}tH.prototype.ca=function(t){t=t.target;let e=this.M;e&&3==ej(t)?e.j():this.Y(t)},tH.prototype.Y=function(t){try{if(t==this.g)t:{let b=ej(this.g);var e=this.g.Ba();let v=this.g.Z();if(!(3>b)&&(3!=b||this.g&&(this.h.h||this.g.oa()||eM(this.g)))){this.J||4!=b||7==e||(8==e||0>=v?tR(3):tR(2)),tY(this);var r=this.g.Z();this.X=r;e:if(tK(this)){var i=eM(this.g);t="";var n=i.length,s=4==ej(this.g);if(!this.h.i){if("undefined"==typeof TextDecoder){tZ(this),tG(this);var o="";break e}this.h.i=new y.TextDecoder}for(e=0;e<n;e++)this.h.h=!0,t+=this.h.i.decode(i[e],{stream:!(s&&e==n-1)});i.length=0,this.h.g+=t,this.C=0,o=this.h.g}else o=this.g.oa();if(this.o=200==r,a=this.i,h=this.u,l=this.A,c=this.l,u=this.R,f=r,a.info(function(){return"XMLHTTP RESP ("+c+") [ attempt "+u+"]: "+h+"\n"+l+"\n"+b+" "+f}),this.o){if(this.T&&!this.K){e:{if(this.g){var a,h,l,c,u,f,p,d=this.g;if((p=d.g?d.g.getResponseHeader("X-HTTP-Initial-Response"):null)&&!O(p)){var g=p;break e}}g=null}if(r=g)tU(this.i,this.l,r,"Initial handshake response via X-HTTP-Initial-Response"),this.K=!0,tQ(this,r);else{this.o=!1,this.s=3,tL(12),tZ(this),tG(this);break t}}if(this.P){let t;for(r=!0;!this.J&&this.C<o.length;)if((t=function(t,e){var r=t.C,i=e.indexOf("\n",r);return -1==i?tz:isNaN(r=Number(e.substring(r,i)))?tq:(i+=1)+r>e.length?tz:(e=e.slice(i,i+r),t.C=i+r,e)}(this,o))==tz){4==b&&(this.s=4,tL(14),r=!1),tU(this.i,this.l,null,"[Incomplete Response]");break}else if(t==tq){this.s=4,tL(15),tU(this.i,this.l,o,"[Invalid Chunk]"),r=!1;break}else tU(this.i,this.l,t,null),tQ(this,t);if(tK(this)&&0!=this.C&&(this.h.g=this.h.g.slice(this.C),this.C=0),4!=b||0!=o.length||this.h.h||(this.s=1,tL(16),r=!1),this.o=this.o&&r,r){if(0<o.length&&!this.W){this.W=!0;var m=this.j;m.g==this&&m.ba&&!m.M&&(m.j.info("Great, no buffering proxy detected. Bytes received: "+o.length),eG(m),m.M=!0,tL(11))}}else tU(this.i,this.l,o,"[Invalid Chunked Response]"),tZ(this),tG(this)}else tU(this.i,this.l,o,null),tQ(this,o);4==b&&tZ(this),this.o&&!this.J&&(4==b?e0(this.j,this):(this.o=!1,tX(this)))}else(function(t){let e={};t=(t.g&&2<=ej(t)&&t.g.getAllResponseHeaders()||"").split("\r\n");for(let i=0;i<t.length;i++){if(O(t[i]))continue;var r=function(t){var e=1;t=t.split(":");let r=[];for(;0<e&&t.length;)r.push(t.shift()),e--;return t.length&&r.push(t.join(":")),r}(t[i]);let n=r[0];if("string"!=typeof(r=r[1]))continue;r=r.trim();let s=e[n]||[];e[n]=s,s.push(r)}var i=function(t){return t.join(", ")};for(let t in e)i.call(void 0,e[t],t,e)})(this.g),400==r&&0<o.indexOf("Unknown SID")?(this.s=3,tL(12)):(this.s=0,tL(13)),tZ(this),tG(this)}}}catch(t){}finally{}},tH.prototype.cancel=function(){this.J=!0,tZ(this)},tH.prototype.ba=function(){var t,e;this.B=null;let r=Date.now();0<=r-this.S?(t=this.i,e=this.A,t.info(function(){return"TIMEOUT: "+e}),2!=this.L&&(tR(),tL(17)),tZ(this),this.s=2,tG(this)):tJ(this,this.S-r)};var t0=class{constructor(t,e){this.g=t,this.map=e}};function t1(t){this.l=t||10,t=y.PerformanceNavigationTiming?0<(t=y.performance.getEntriesByType("navigation")).length&&("hq"==t[0].nextHopProtocol||"h2"==t[0].nextHopProtocol):!!(y.chrome&&y.chrome.loadTimes&&y.chrome.loadTimes()&&y.chrome.loadTimes().wasFetchedViaSpdy),this.j=t?this.l:1,this.g=null,1<this.j&&(this.g=new Set),this.h=null,this.i=[]}function t2(t){return!!t.h||!!t.g&&t.g.size>=t.j}function t6(t){return t.h?1:t.g?t.g.size:0}function t3(t,e){return t.h?t.h==e:!!t.g&&t.g.has(e)}function t5(t,e){t.g?t.g.add(e):t.h=e}function t4(t,e){t.h&&t.h==e?t.h=null:t.g&&t.g.has(e)&&t.g.delete(e)}function t8(t){if(null!=t.h)return t.i.concat(t.h.D);if(null!=t.g&&0!==t.g.size){let e=t.i;for(let r of t.g.values())e=e.concat(r.D);return e}return C(t.i)}function t7(t,e){if(t.forEach&&"function"==typeof t.forEach)t.forEach(e,void 0);else if(m(t)||"string"==typeof t)Array.prototype.forEach.call(t,e,void 0);else for(var r=function(t){if(t.na&&"function"==typeof t.na)return t.na();if(!t.V||"function"!=typeof t.V){if("undefined"!=typeof Map&&t instanceof Map)return Array.from(t.keys());if(!("undefined"!=typeof Set&&t instanceof Set)){if(m(t)||"string"==typeof t){var e=[];t=t.length;for(var r=0;r<t;r++)e.push(r);return e}for(let i in e=[],r=0,t)e[r++]=i;return e}}}(t),i=function(t){if(t.V&&"function"==typeof t.V)return t.V();if("undefined"!=typeof Map&&t instanceof Map||"undefined"!=typeof Set&&t instanceof Set)return Array.from(t.values());if("string"==typeof t)return t.split("");if(m(t)){for(var e=[],r=t.length,i=0;i<r;i++)e.push(t[i]);return e}for(i in e=[],r=0,t)e[r++]=t[i];return e}(t),n=i.length,s=0;s<n;s++)e.call(void 0,i[s],r&&r[s],t)}t1.prototype.cancel=function(){if(this.i=t8(this),this.h)this.h.cancel(),this.h=null;else if(this.g&&0!==this.g.size){for(let t of this.g.values())t.cancel();this.g.clear()}};var t9=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");function et(t){if(this.g=this.o=this.j="",this.s=null,this.m=this.l="",this.h=!1,t instanceof et){this.h=t.h,er(this,t.j),this.o=t.o,this.g=t.g,ei(this,t.s),this.l=t.l;var e=t.i,r=new eg;r.i=e.i,e.g&&(r.g=new Map(e.g),r.h=e.h),en(this,r),this.m=t.m}else t&&(e=String(t).match(t9))?(this.h=!1,er(this,e[1]||"",!0),this.o=ea(e[2]||""),this.g=ea(e[3]||"",!0),ei(this,e[4]),this.l=ea(e[5]||"",!0),en(this,e[6]||"",!0),this.m=ea(e[7]||"")):(this.h=!1,this.i=new eg(null,this.h))}function ee(t){return new et(t)}function er(t,e,r){t.j=r?ea(e,!0):e,t.j&&(t.j=t.j.replace(/:$/,""))}function ei(t,e){if(e){if(isNaN(e=Number(e))||0>e)throw Error("Bad port number "+e);t.s=e}else t.s=null}function en(t,e,r){var i,n;e instanceof eg?(t.i=e,i=t.i,(n=t.h)&&!i.j&&(ey(i),i.i=null,i.g.forEach(function(t,e){var r=e.toLowerCase();e!=r&&(em(this,e),ev(this,r,t))},i)),i.j=n):(r||(e=eh(e,ep)),t.i=new eg(e,t.h))}function es(t,e,r){t.i.set(e,r)}function eo(t){return es(t,"zx",Math.floor(0x80000000*Math.random()).toString(36)+Math.abs(Math.floor(0x80000000*Math.random())^Date.now()).toString(36)),t}function ea(t,e){return t?e?decodeURI(t.replace(/%25/g,"%2525")):decodeURIComponent(t):""}function eh(t,e,r){return"string"==typeof t?(t=encodeURI(t).replace(e,el),r&&(t=t.replace(/%25([0-9a-fA-F]{2})/g,"%$1")),t):null}function el(t){return"%"+((t=t.charCodeAt(0))>>4&15).toString(16)+(15&t).toString(16)}et.prototype.toString=function(){var t=[],e=this.j;e&&t.push(eh(e,ec,!0),":");var r=this.g;return(r||"file"==e)&&(t.push("//"),(e=this.o)&&t.push(eh(e,ec,!0),"@"),t.push(encodeURIComponent(String(r)).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),null!=(r=this.s)&&t.push(":",String(r))),(r=this.l)&&(this.g&&"/"!=r.charAt(0)&&t.push("/"),t.push(eh(r,"/"==r.charAt(0)?ef:eu,!0))),(r=this.i.toString())&&t.push("?",r),(r=this.m)&&t.push("#",eh(r,ed)),t.join("")};var ec=/[#\/\?@]/g,eu=/[#\?:]/g,ef=/[#\?]/g,ep=/[#\?@]/g,ed=/#/g;function eg(t,e){this.h=this.g=null,this.i=t||null,this.j=!!e}function ey(t){t.g||(t.g=new Map,t.h=0,t.i&&function(t,e){if(t){t=t.split("&");for(var r=0;r<t.length;r++){var i=t[r].indexOf("="),n=null;if(0<=i){var s=t[r].substring(0,i);n=t[r].substring(i+1)}else s=t[r];e(s,n?decodeURIComponent(n.replace(/\+/g," ")):"")}}}(t.i,function(e,r){t.add(decodeURIComponent(e.replace(/\+/g," ")),r)}))}function em(t,e){ey(t),e=e_(t,e),t.g.has(e)&&(t.i=null,t.h-=t.g.get(e).length,t.g.delete(e))}function eb(t,e){return ey(t),e=e_(t,e),t.g.has(e)}function ev(t,e,r){em(t,e),0<r.length&&(t.i=null,t.g.set(e_(t,e),C(r)),t.h+=r.length)}function e_(t,e){return e=String(e),t.j&&(e=e.toLowerCase()),e}function ew(t,e,r,i,n){try{n&&(n.onload=null,n.onerror=null,n.onabort=null,n.ontimeout=null),i(r)}catch(t){}}function eE(){this.g=new t_}function eA(t){this.l=t.Ub||null,this.j=t.eb||!1}function eC(t,e){tc.call(this),this.D=t,this.o=e,this.m=void 0,this.status=this.readyState=0,this.responseType=this.responseText=this.response=this.statusText="",this.onreadystatechange=null,this.u=new Headers,this.h=null,this.B="GET",this.A="",this.g=!1,this.v=this.j=this.l=null}function eT(t){t.j.read().then(t.Pa.bind(t)).catch(t.ga.bind(t))}function ek(t){t.readyState=4,t.l=null,t.j=null,t.v=null,eO(t)}function eO(t){t.onreadystatechange&&t.onreadystatechange.call(t)}function eS(t){let e="";return R(t,function(t,r){e+=r,e+=":",e+=t,e+="\r\n"}),e}function ex(t,e,r){t:{for(i in r){var i=!1;break t}i=!0}i||(r=eS(r),"string"==typeof t?null!=r&&encodeURIComponent(String(r)):es(t,e,r))}function eI(t){tc.call(this),this.headers=new Map,this.o=t||null,this.h=!1,this.v=this.g=null,this.D="",this.m=0,this.l="",this.j=this.B=this.u=this.A=!1,this.I=null,this.H="",this.J=!1}(r=eg.prototype).add=function(t,e){ey(this),this.i=null,t=e_(this,t);var r=this.g.get(t);return r||this.g.set(t,r=[]),r.push(e),this.h+=1,this},r.forEach=function(t,e){ey(this),this.g.forEach(function(r,i){r.forEach(function(r){t.call(e,r,i,this)},this)},this)},r.na=function(){ey(this);let t=Array.from(this.g.values()),e=Array.from(this.g.keys()),r=[];for(let i=0;i<e.length;i++){let n=t[i];for(let t=0;t<n.length;t++)r.push(e[i])}return r},r.V=function(t){ey(this);let e=[];if("string"==typeof t)eb(this,t)&&(e=e.concat(this.g.get(e_(this,t))));else{t=Array.from(this.g.values());for(let r=0;r<t.length;r++)e=e.concat(t[r])}return e},r.set=function(t,e){return ey(this),this.i=null,eb(this,t=e_(this,t))&&(this.h-=this.g.get(t).length),this.g.set(t,[e]),this.h+=1,this},r.get=function(t,e){return t&&0<(t=this.V(t)).length?String(t[0]):e},r.toString=function(){if(this.i)return this.i;if(!this.g)return"";let t=[],e=Array.from(this.g.keys());for(var r=0;r<e.length;r++){var i=e[r];let s=encodeURIComponent(String(i)),o=this.V(i);for(i=0;i<o.length;i++){var n=s;""!==o[i]&&(n+="="+encodeURIComponent(String(o[i]))),t.push(n)}}return this.i=t.join("&")},A(eA,tw),eA.prototype.g=function(){return new eC(this.l,this.j)},eA.prototype.i=(t={},function(){return t}),A(eC,tc),(r=eC.prototype).open=function(t,e){if(0!=this.readyState)throw this.abort(),Error("Error reopening a connection");this.B=t,this.A=e,this.readyState=1,eO(this)},r.send=function(t){if(1!=this.readyState)throw this.abort(),Error("need to call open() first. ");this.g=!0;let e={headers:this.u,method:this.B,credentials:this.m,cache:void 0};t&&(e.body=t),(this.D||y).fetch(new Request(this.A,e)).then(this.Sa.bind(this),this.ga.bind(this))},r.abort=function(){this.response=this.responseText="",this.u=new Headers,this.status=0,this.j&&this.j.cancel("Request was aborted.").catch(()=>{}),1<=this.readyState&&this.g&&4!=this.readyState&&(this.g=!1,ek(this)),this.readyState=0},r.Sa=function(t){if(this.g&&(this.l=t,this.h||(this.status=this.l.status,this.statusText=this.l.statusText,this.h=t.headers,this.readyState=2,eO(this)),this.g&&(this.readyState=3,eO(this),this.g)))if("arraybuffer"===this.responseType)t.arrayBuffer().then(this.Qa.bind(this),this.ga.bind(this));else if(void 0!==y.ReadableStream&&"body"in t){if(this.j=t.body.getReader(),this.o){if(this.responseType)throw Error('responseType must be empty for "streamBinaryChunks" mode responses.');this.response=[]}else this.response=this.responseText="",this.v=new TextDecoder;eT(this)}else t.text().then(this.Ra.bind(this),this.ga.bind(this))},r.Pa=function(t){if(this.g){if(this.o&&t.value)this.response.push(t.value);else if(!this.o){var e=t.value?t.value:new Uint8Array(0);(e=this.v.decode(e,{stream:!t.done}))&&(this.response=this.responseText+=e)}t.done?ek(this):eO(this),3==this.readyState&&eT(this)}},r.Ra=function(t){this.g&&(this.response=this.responseText=t,ek(this))},r.Qa=function(t){this.g&&(this.response=t,ek(this))},r.ga=function(){this.g&&ek(this)},r.setRequestHeader=function(t,e){this.u.append(t,e)},r.getResponseHeader=function(t){return this.h&&this.h.get(t.toLowerCase())||""},r.getAllResponseHeaders=function(){if(!this.h)return"";let t=[],e=this.h.entries();for(var r=e.next();!r.done;)t.push((r=r.value)[0]+": "+r[1]),r=e.next();return t.join("\r\n")},Object.defineProperty(eC.prototype,"withCredentials",{get:function(){return"include"===this.m},set:function(t){this.m=t?"include":"same-origin"}}),A(eI,tc);var eR=/^https?$/i,eB=["POST","PUT"];function eL(t,e){t.h=!1,t.g&&(t.j=!0,t.g.abort(),t.j=!1),t.l=e,t.m=5,eD(t),eP(t)}function eD(t){t.A||(t.A=!0,tu(t,"complete"),tu(t,"error"))}function eN(t){if(t.h&&void 0!==g&&(!t.v[1]||4!=ej(t)||2!=t.Z())){if(t.u&&4==ej(t))tp(t.Ea,0,t);else if(tu(t,"readystatechange"),4==ej(t)){t.h=!1;try{let o=t.Z();switch(o){case 200:case 201:case 202:case 204:case 206:case 304:case 1223:var e,r,i=!0;break;default:i=!1}if(!(e=i)){if(r=0===o){var n=String(t.D).match(t9)[1]||null;!n&&y.self&&y.self.location&&(n=y.self.location.protocol.slice(0,-1)),r=!eR.test(n?n.toLowerCase():"")}e=r}if(e)tu(t,"complete"),tu(t,"success");else{t.m=6;try{var s=2<ej(t)?t.g.statusText:""}catch(t){s=""}t.l=s+" ["+t.Z()+"]",eD(t)}}finally{eP(t)}}}}function eP(t,e){if(t.g){eU(t);let r=t.g,i=t.v[0]?()=>{}:null;t.g=null,t.v=null,e||tu(t,"ready");try{r.onreadystatechange=i}catch(t){}}}function eU(t){t.I&&(y.clearTimeout(t.I),t.I=null)}function ej(t){return t.g?t.g.readyState:0}function eM(t){try{if(!t.g)return null;if("response"in t.g)return t.g.response;switch(t.H){case"":case"text":return t.g.responseText;case"arraybuffer":if("mozResponseArrayBuffer"in t.g)return t.g.mozResponseArrayBuffer}return null}catch(t){return null}}function eF(t,e,r){return r&&r.internalChannelParams&&r.internalChannelParams[t]||e}function eH(t){this.Aa=0,this.i=[],this.j=new tP,this.ia=this.qa=this.I=this.W=this.g=this.ya=this.D=this.H=this.m=this.S=this.o=null,this.Ya=this.U=0,this.Va=eF("failFast",!1,t),this.F=this.C=this.u=this.s=this.l=null,this.X=!0,this.za=this.T=-1,this.Y=this.v=this.B=0,this.Ta=eF("baseRetryDelayMs",5e3,t),this.cb=eF("retryDelaySeedMs",1e4,t),this.Wa=eF("forwardChannelMaxRetries",2,t),this.wa=eF("forwardChannelRequestTimeoutMs",2e4,t),this.pa=t&&t.xmlHttpFactory||void 0,this.Xa=t&&t.Tb||void 0,this.Ca=t&&t.useFetchStreams||!1,this.L=void 0,this.J=t&&t.supportsCrossDomainXhr||!1,this.K="",this.h=new t1(t&&t.concurrentRequestLimit),this.Da=new eE,this.P=t&&t.fastHandshake||!1,this.O=t&&t.encodeInitMessageHeaders||!1,this.P&&this.O&&(this.O=!1),this.Ua=t&&t.Rb||!1,t&&t.xa&&this.j.xa(),t&&t.forceLongPolling&&(this.X=!1),this.ba=!this.P&&this.X&&t&&t.detectBufferingProxy||!1,this.ja=void 0,t&&t.longPollingTimeout&&0<t.longPollingTimeout&&(this.ja=t.longPollingTimeout),this.ca=void 0,this.R=0,this.M=!1,this.ka=this.A=null}function eV(t){if(ez(t),3==t.G){var e=t.U++,r=ee(t.I);if(es(r,"SID",t.K),es(r,"RID",e),es(r,"TYPE","terminate"),eK(t,r),(e=new tH(t,t.j,e)).L=2,e.v=eo(ee(r)),r=!1,y.navigator&&y.navigator.sendBeacon)try{r=y.navigator.sendBeacon(e.v.toString(),"")}catch(t){}!r&&y.Image&&((new Image).src=e.v,r=!0),r||(e.g=e5(e.j,null),e.g.ea(e.v)),e.F=Date.now(),tX(e)}e6(t)}function eq(t){t.g&&(eG(t),t.g.cancel(),t.g=null)}function ez(t){eq(t),t.u&&(y.clearTimeout(t.u),t.u=null),eQ(t),t.h.cancel(),t.s&&("number"==typeof t.s&&y.clearTimeout(t.s),t.s=null)}function e$(t){if(!t2(t.h)&&!t.s){t.s=!0;var e=t.Ga;j||H(),M||(j(),M=!0),F.add(e,t),t.B=0}}function eW(t,e){var r;r=e?e.l:t.U++;let i=ee(t.I);es(i,"SID",t.K),es(i,"RID",r),es(i,"AID",t.T),eK(t,i),t.m&&t.o&&ex(i,t.m,t.o),r=new tH(t,t.j,r,t.B+1),null===t.m&&(r.H=t.o),e&&(t.i=e.D.concat(t.i)),e=eX(t,r,1e3),r.I=Math.round(.5*t.wa)+Math.round(.5*t.wa*Math.random()),t5(t.h,r),t$(r,i,e)}function eK(t,e){t.H&&R(t.H,function(t,r){es(e,r,t)}),t.l&&t7({},function(t,r){es(e,r,t)})}function eX(t,e,r){r=Math.min(t.i.length,r);var i=t.l?w(t.l.Na,t.l,t):null;t:{var n=t.i;let e=-1;for(;;){let t=["count="+r];-1==e?0<r?(e=n[0].g,t.push("ofs="+e)):e=0:t.push("ofs="+e);let s=!0;for(let o=0;o<r;o++){let r=n[o].g,a=n[o].map;if(0>(r-=e))e=Math.max(0,n[o].g-100),s=!1;else try{!function(t,e,r){let i=r||"";try{t7(t,function(t,r){let n=t;b(t)&&(n=tb(t)),e.push(i+r+"="+encodeURIComponent(n))})}catch(t){throw e.push(i+"type="+encodeURIComponent("_badmap")),t}}(a,t,"req"+r+"_")}catch(t){i&&i(a)}}if(s){i=t.join("&");break t}}}return e.D=t=t.i.splice(0,r),i}function eJ(t){if(!t.g&&!t.u){t.Y=1;var e=t.Fa;j||H(),M||(j(),M=!0),F.add(e,t),t.v=0}}function eY(t){return!t.g&&!t.u&&!(3<=t.v)&&(t.Y++,t.u=tN(w(t.Fa,t),e1(t,t.v)),t.v++,!0)}function eG(t){null!=t.A&&(y.clearTimeout(t.A),t.A=null)}function eZ(t){t.g=new tH(t,t.j,"rpc",t.Y),null===t.m&&(t.g.H=t.o),t.g.O=0;var e=ee(t.qa);es(e,"RID","rpc"),es(e,"SID",t.K),es(e,"AID",t.T),es(e,"CI",t.F?"0":"1"),!t.F&&t.ja&&es(e,"TO",t.ja),es(e,"TYPE","xmlhttp"),eK(t,e),t.m&&t.o&&ex(e,t.m,t.o),t.L&&(t.g.I=t.L);var r=t.g;t=t.ia,r.L=1,r.v=eo(ee(e)),r.m=null,r.P=!0,tW(r,t)}function eQ(t){null!=t.C&&(y.clearTimeout(t.C),t.C=null)}function e0(t,e){var r,i=null;if(t.g==e){eQ(t),eG(t),t.g=null;var n=2}else{if(!t3(t.h,e))return;i=e.D,t4(t.h,e),n=1}if(0!=t.G){if(e.o)if(1==n){i=e.m?e.m.length:0,e=Date.now()-e.F;var s=t.B;tu(n=tx(),new tD(n,i)),e$(t)}else eJ(t);else if(3==(s=e.s)||0==s&&0<e.X||!(1==n&&(r=e,!(t6(t.h)>=t.h.j-!!t.s)&&(t.s?(t.i=r.D.concat(t.i),!0):1!=t.G&&2!=t.G&&!(t.B>=(t.Va?0:t.Wa))&&(t.s=tN(w(t.Ga,t,r),e1(t,t.B)),t.B++,!0)))||2==n&&eY(t)))switch(i&&0<i.length&&((e=t.h).i=e.i.concat(i)),s){case 1:e2(t,5);break;case 4:e2(t,10);break;case 3:e2(t,6);break;default:e2(t,2)}}}function e1(t,e){let r=t.Ta+Math.floor(Math.random()*t.cb);return t.isActive()||(r*=2),r*e}function e2(t,e){if(t.j.info("Error code "+e),2==e){var r=w(t.fb,t),i=t.Xa;let e=!i;i=new et(i||"//www.google.com/images/cleardot.gif"),y.location&&"http"==y.location.protocol||er(i,"https"),eo(i),e?function(t,e){let r=new tP;if(y.Image){let i=new Image;i.onload=E(ew,r,"TestLoadImage: loaded",!0,e,i),i.onerror=E(ew,r,"TestLoadImage: error",!1,e,i),i.onabort=E(ew,r,"TestLoadImage: abort",!1,e,i),i.ontimeout=E(ew,r,"TestLoadImage: timeout",!1,e,i),y.setTimeout(function(){i.ontimeout&&i.ontimeout()},1e4),i.src=t}else e(!1)}(i.toString(),r):function(t,e){let r=new tP,i=new AbortController,n=setTimeout(()=>{i.abort(),ew(r,"TestPingServer: timeout",!1,e)},1e4);fetch(t,{signal:i.signal}).then(t=>{clearTimeout(n),t.ok?ew(r,"TestPingServer: ok",!0,e):ew(r,"TestPingServer: server error",!1,e)}).catch(()=>{clearTimeout(n),ew(r,"TestPingServer: error",!1,e)})}(i.toString(),r)}else tL(2);t.G=0,t.l&&t.l.sa(e),e6(t),ez(t)}function e6(t){if(t.G=0,t.ka=[],t.l){let e=t8(t.h);(0!=e.length||0!=t.i.length)&&(T(t.ka,e),T(t.ka,t.i),t.h.i.length=0,C(t.i),t.i.length=0),t.l.ra()}}function e3(t,e,r){var i=r instanceof et?ee(r):new et(r);if(""!=i.g)e&&(i.g=e+"."+i.g),ei(i,i.s);else{var n=y.location;i=n.protocol,e=e?e+"."+n.hostname:n.hostname,n=+n.port;var s=new et(null);i&&er(s,i),e&&(s.g=e),n&&ei(s,n),r&&(s.l=r),i=s}return r=t.D,e=t.ya,r&&e&&es(i,r,e),es(i,"VER",t.la),eK(t,i),i}function e5(t,e,r){if(e&&!t.J)throw Error("Can't create secondary domain capable XhrIo object.");return(e=new eI(t.Ca&&!t.pa?new eA({eb:r}):t.pa)).Ha(t.J),e}function e4(){}function e8(){}function e7(t,e){tc.call(this),this.g=new eH(e),this.l=t,this.h=e&&e.messageUrlParams||null,t=e&&e.messageHeaders||null,e&&e.clientProtocolHeaderRequired&&(t?t["X-Client-Protocol"]="webchannel":t={"X-Client-Protocol":"webchannel"}),this.g.o=t,t=e&&e.initMessageHeaders||null,e&&e.messageContentType&&(t?t["X-WebChannel-Content-Type"]=e.messageContentType:t={"X-WebChannel-Content-Type":e.messageContentType}),e&&e.va&&(t?t["X-WebChannel-Client-Profile"]=e.va:t={"X-WebChannel-Client-Profile":e.va}),this.g.S=t,(t=e&&e.Sb)&&!O(t)&&(this.g.m=t),this.v=e&&e.supportsCrossDomainXhr||!1,this.u=e&&e.sendRawJson||!1,(e=e&&e.httpSessionIdParam)&&!O(e)&&(this.g.D=e,null!==(t=this.h)&&e in t&&e in(t=this.h)&&delete t[e]),this.j=new re(this)}function e9(t){tT.call(this),t.__headers__&&(this.headers=t.__headers__,this.statusCode=t.__status__,delete t.__headers__,delete t.__status__);var e=t.__sm__;if(e){t:{for(let r in e){t=r;break t}t=void 0}(this.i=t)&&(t=this.i,e=null!==e&&t in e?e[t]:void 0),this.data=e}else this.data=t}function rt(){tk.call(this),this.status=1}function re(t){this.g=t}(r=eI.prototype).Ha=function(t){this.J=t},r.ea=function(t,r,i,n){if(this.g)throw Error("[goog.net.XhrIo] Object is active with another request="+this.D+"; newUri="+t);r=r?r.toUpperCase():"GET",this.D=t,this.l="",this.m=0,this.A=!1,this.h=!0,this.g=this.o?this.o.g():e.g(),this.v=this.o?tE(this.o):tE(e),this.g.onreadystatechange=w(this.Ea,this);try{this.B=!0,this.g.open(r,String(t),!0),this.B=!1}catch(t){eL(this,t);return}if(t=i||"",i=new Map(this.headers),n)if(Object.getPrototypeOf(n)===Object.prototype)for(var s in n)i.set(s,n[s]);else if("function"==typeof n.keys&&"function"==typeof n.get)for(let t of n.keys())i.set(t,n.get(t));else throw Error("Unknown input type for opt_headers: "+String(n));for(let[e,o]of(n=Array.from(i.keys()).find(t=>"content-type"==t.toLowerCase()),s=y.FormData&&t instanceof y.FormData,!(0<=Array.prototype.indexOf.call(eB,r,void 0))||n||s||i.set("Content-Type","application/x-www-form-urlencoded;charset=utf-8"),i))this.g.setRequestHeader(e,o);this.H&&(this.g.responseType=this.H),"withCredentials"in this.g&&this.g.withCredentials!==this.J&&(this.g.withCredentials=this.J);try{eU(this),this.u=!0,this.g.send(t),this.u=!1}catch(t){eL(this,t)}},r.abort=function(t){this.g&&this.h&&(this.h=!1,this.j=!0,this.g.abort(),this.j=!1,this.m=t||7,tu(this,"complete"),tu(this,"abort"),eP(this))},r.N=function(){this.g&&(this.h&&(this.h=!1,this.j=!0,this.g.abort(),this.j=!1),eP(this,!0)),eI.aa.N.call(this)},r.Ea=function(){this.s||(this.B||this.u||this.j?eN(this):this.bb())},r.bb=function(){eN(this)},r.isActive=function(){return!!this.g},r.Z=function(){try{return 2<ej(this)?this.g.status:-1}catch(t){return -1}},r.oa=function(){try{return this.g?this.g.responseText:""}catch(t){return""}},r.Oa=function(t){if(this.g){var e=this.g.responseText;return t&&0==e.indexOf(t)&&(e=e.substring(t.length)),tv(e)}},r.Ba=function(){return this.m},r.Ka=function(){return"string"==typeof this.l?this.l:String(this.l)},(r=eH.prototype).la=8,r.G=1,r.connect=function(t,e,r,i){tL(0),this.W=t,this.H=e||{},r&&void 0!==i&&(this.H.OSID=r,this.H.OAID=i),this.F=this.X,this.I=e3(this,null,this.W),e$(this)},r.Ga=function(t){if(this.s)if(this.s=null,1==this.G){if(!t){this.U=Math.floor(1e5*Math.random()),t=this.U++;let n=new tH(this,this.j,t),s=this.o;if(this.S&&(s?D(s=B(s),this.S):s=this.S),null!==this.m||this.O||(n.H=s,s=null),this.P)t:{for(var e=0,r=0;r<this.i.length;r++){e:{var i=this.i[r];if("__data__"in i.map&&"string"==typeof(i=i.map.__data__)){i=i.length;break e}i=void 0}if(void 0===i)break;if(4096<(e+=i)){e=r;break t}if(4096===e||r===this.i.length-1){e=r+1;break t}}e=1e3}else e=1e3;e=eX(this,n,e),es(r=ee(this.I),"RID",t),es(r,"CVER",22),this.D&&es(r,"X-HTTP-Session-Id",this.D),eK(this,r),s&&(this.O?e="headers="+encodeURIComponent(String(eS(s)))+"&"+e:this.m&&ex(r,this.m,s)),t5(this.h,n),this.Ua&&es(r,"TYPE","init"),this.P?(es(r,"$req",e),es(r,"SID","null"),n.T=!0,t$(n,r,null)):t$(n,r,e),this.G=2}}else 3==this.G&&(t?eW(this,t):0==this.i.length||t2(this.h)||eW(this))},r.Fa=function(){if(this.u=null,eZ(this),this.ba&&!(this.M||null==this.g||0>=this.R)){var t=2*this.R;this.j.info("BP detection timer enabled: "+t),this.A=tN(w(this.ab,this),t)}},r.ab=function(){this.A&&(this.A=null,this.j.info("BP detection timeout reached."),this.j.info("Buffering proxy detected and switch to long-polling!"),this.F=!1,this.M=!0,tL(10),eq(this),eZ(this))},r.Za=function(){null!=this.C&&(this.C=null,eq(this),eY(this),tL(19))},r.fb=function(t){t?(this.j.info("Successfully pinged google.com"),tL(2)):(this.j.info("Failed to ping google.com"),tL(1))},r.isActive=function(){return!!this.l&&this.l.isActive(this)},(r=e4.prototype).ua=function(){},r.ta=function(){},r.sa=function(){},r.ra=function(){},r.isActive=function(){return!0},r.Na=function(){},e8.prototype.g=function(t,e){return new e7(t,e)},A(e7,tc),e7.prototype.m=function(){this.g.l=this.j,this.v&&(this.g.J=!0),this.g.connect(this.l,this.h||void 0)},e7.prototype.close=function(){eV(this.g)},e7.prototype.o=function(t){var e=this.g;if("string"==typeof t){var r={};r.__data__=t,t=r}else this.u&&((r={}).__data__=tb(t),t=r);e.i.push(new t0(e.Ya++,t)),3==e.G&&e$(e)},e7.prototype.N=function(){this.g.l=null,delete this.j,eV(this.g),delete this.g,e7.aa.N.call(this)},A(e9,tT),A(rt,tk),A(re,e4),re.prototype.ua=function(){tu(this.g,"a")},re.prototype.ta=function(t){tu(this.g,new e9(t))},re.prototype.sa=function(t){tu(this.g,new rt)},re.prototype.ra=function(){tu(this.g,"b")},e8.prototype.createWebChannel=e8.prototype.g,e7.prototype.send=e7.prototype.o,e7.prototype.open=e7.prototype.m,e7.prototype.close=e7.prototype.close,c=f.createWebChannelTransport=function(){return new e8},l=f.getStatEventTarget=function(){return tx()},h=f.Event=tO,a=f.Stat={mb:0,pb:1,qb:2,Jb:3,Ob:4,Lb:5,Mb:6,Kb:7,Ib:8,Nb:9,PROXY:10,NOPROXY:11,Gb:12,Cb:13,Db:14,Bb:15,Eb:16,Fb:17,ib:18,hb:19,jb:20},tj.NO_ERROR=0,tj.TIMEOUT=8,tj.HTTP_ERROR=6,o=f.ErrorCode=tj,tM.COMPLETE="complete",s=f.EventType=tM,tA.EventType=tC,tC.OPEN="a",tC.CLOSE="b",tC.ERROR="c",tC.MESSAGE="d",tc.prototype.listen=tc.prototype.K,n=f.WebChannel=tA,f.FetchXmlHttpFactory=eA,eI.prototype.listenOnce=eI.prototype.L,eI.prototype.getLastError=eI.prototype.Ka,eI.prototype.getLastErrorCode=eI.prototype.Ba,eI.prototype.getStatus=eI.prototype.Z,eI.prototype.getResponseJson=eI.prototype.Oa,eI.prototype.getResponseText=eI.prototype.oa,eI.prototype.send=eI.prototype.ea,eI.prototype.setWithCredentials=eI.prototype.Ha,i=f.XhrIo=eI}).apply(void 0!==u?u:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},1055:(t,e,r)=>{"use strict";let i,n;r.d(e,{MF:()=>N,j6:()=>R,xZ:()=>B,om:()=>I,Sx:()=>U,Wp:()=>P,KO:()=>j});var s=r(2881),o=r(6702),a=r(1280);let h=new WeakMap,l=new WeakMap,c=new WeakMap,u=new WeakMap,f=new WeakMap,p={get(t,e,r){if(t instanceof IDBTransaction){if("done"===e)return l.get(t);if("objectStoreNames"===e)return t.objectStoreNames||c.get(t);if("store"===e)return r.objectStoreNames[1]?void 0:r.objectStore(r.objectStoreNames[0])}return d(t[e])},set:(t,e,r)=>(t[e]=r,!0),has:(t,e)=>t instanceof IDBTransaction&&("done"===e||"store"===e)||e in t};function d(t){if(t instanceof IDBRequest){let e=new Promise((e,r)=>{let i=()=>{t.removeEventListener("success",n),t.removeEventListener("error",s)},n=()=>{e(d(t.result)),i()},s=()=>{r(t.error),i()};t.addEventListener("success",n),t.addEventListener("error",s)});return e.then(e=>{e instanceof IDBCursor&&h.set(e,t)}).catch(()=>{}),f.set(e,t),e}if(u.has(t))return u.get(t);let e=function(t){if("function"==typeof t)return t!==IDBDatabase.prototype.transaction||"objectStoreNames"in IDBTransaction.prototype?(n||(n=[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey])).includes(t)?function(...e){return t.apply(g(this),e),d(h.get(this))}:function(...e){return d(t.apply(g(this),e))}:function(e,...r){let i=t.call(g(this),e,...r);return c.set(i,e.sort?e.sort():[e]),d(i)};return t instanceof IDBTransaction&&function(t){if(l.has(t))return;let e=new Promise((e,r)=>{let i=()=>{t.removeEventListener("complete",n),t.removeEventListener("error",s),t.removeEventListener("abort",s)},n=()=>{e(),i()},s=()=>{r(t.error||new DOMException("AbortError","AbortError")),i()};t.addEventListener("complete",n),t.addEventListener("error",s),t.addEventListener("abort",s)});l.set(t,e)}(t),(i||(i=[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])).some(e=>t instanceof e)?new Proxy(t,p):t}(t);return e!==t&&(u.set(t,e),f.set(e,t)),e}let g=t=>f.get(t),y=["get","getKey","getAll","getAllKeys","count"],m=["put","add","delete","clear"],b=new Map;function v(t,e){if(!(t instanceof IDBDatabase&&!(e in t)&&"string"==typeof e))return;if(b.get(e))return b.get(e);let r=e.replace(/FromIndex$/,""),i=e!==r,n=m.includes(r);if(!(r in(i?IDBIndex:IDBObjectStore).prototype)||!(n||y.includes(r)))return;let s=async function(t,...e){let s=this.transaction(t,n?"readwrite":"readonly"),o=s.store;return i&&(o=o.index(e.shift())),(await Promise.all([o[r](...e),n&&s.done]))[0]};return b.set(e,s),s}p=(t=>({...t,get:(e,r,i)=>v(e,r)||t.get(e,r,i),has:(e,r)=>!!v(e,r)||t.has(e,r)}))(p);class _{constructor(t){this.container=t}getPlatformInfoString(){return this.container.getProviders().map(t=>{if(!function(t){let e=t.getComponent();return e?.type==="VERSION"}(t))return null;{let e=t.getImmediate();return`${e.library}/${e.version}`}}).filter(t=>t).join(" ")}}let w="@firebase/app",E="0.14.0",A=new o.Vy("@firebase/app"),C="[DEFAULT]",T={[w]:"fire-core","@firebase/app-compat":"fire-core-compat","@firebase/analytics":"fire-analytics","@firebase/analytics-compat":"fire-analytics-compat","@firebase/app-check":"fire-app-check","@firebase/app-check-compat":"fire-app-check-compat","@firebase/auth":"fire-auth","@firebase/auth-compat":"fire-auth-compat","@firebase/database":"fire-rtdb","@firebase/data-connect":"fire-data-connect","@firebase/database-compat":"fire-rtdb-compat","@firebase/functions":"fire-fn","@firebase/functions-compat":"fire-fn-compat","@firebase/installations":"fire-iid","@firebase/installations-compat":"fire-iid-compat","@firebase/messaging":"fire-fcm","@firebase/messaging-compat":"fire-fcm-compat","@firebase/performance":"fire-perf","@firebase/performance-compat":"fire-perf-compat","@firebase/remote-config":"fire-rc","@firebase/remote-config-compat":"fire-rc-compat","@firebase/storage":"fire-gcs","@firebase/storage-compat":"fire-gcs-compat","@firebase/firestore":"fire-fst","@firebase/firestore-compat":"fire-fst-compat","@firebase/ai":"fire-vertex","fire-js":"fire-js",firebase:"fire-js-all"},k=new Map,O=new Map,S=new Map;function x(t,e){try{t.container.addComponent(e)}catch(r){A.debug(`Component ${e.name} failed to register with FirebaseApp ${t.name}`,r)}}function I(t){let e=t.name;if(S.has(e))return A.debug(`There were multiple attempts to register component ${e}.`),!1;for(let r of(S.set(e,t),k.values()))x(r,t);for(let e of O.values())x(e,t);return!0}function R(t,e){let r=t.container.getProvider("heartbeat").getImmediate({optional:!0});return r&&r.triggerHeartbeat(),t.container.getProvider(e)}function B(t){return null!=t&&void 0!==t.settings}let L=new a.FA("app","Firebase",{"no-app":"No Firebase App '{$appName}' has been created - call initializeApp() first","bad-app-name":"Illegal App name: '{$appName}'","duplicate-app":"Firebase App named '{$appName}' already exists with different options or config","app-deleted":"Firebase App named '{$appName}' already deleted","server-app-deleted":"Firebase Server App has been deleted","no-options":"Need to provide options, when not being deployed to hosting via source.","invalid-app-argument":"firebase.{$appName}() takes either no argument or a Firebase App instance.","invalid-log-argument":"First argument to `onLog` must be null or a function.","idb-open":"Error thrown when opening IndexedDB. Original error: {$originalErrorMessage}.","idb-get":"Error thrown when reading from IndexedDB. Original error: {$originalErrorMessage}.","idb-set":"Error thrown when writing to IndexedDB. Original error: {$originalErrorMessage}.","idb-delete":"Error thrown when deleting from IndexedDB. Original error: {$originalErrorMessage}.","finalization-registry-not-supported":"FirebaseServerApp deleteOnDeref field defined but the JS runtime does not support FinalizationRegistry.","invalid-server-app-environment":"FirebaseServerApp is not for use in browser environments."});class D{constructor(t,e,r){this._isDeleted=!1,this._options={...t},this._config={...e},this._name=e.name,this._automaticDataCollectionEnabled=e.automaticDataCollectionEnabled,this._container=r,this.container.addComponent(new s.uA("app",()=>this,"PUBLIC"))}get automaticDataCollectionEnabled(){return this.checkDestroyed(),this._automaticDataCollectionEnabled}set automaticDataCollectionEnabled(t){this.checkDestroyed(),this._automaticDataCollectionEnabled=t}get name(){return this.checkDestroyed(),this._name}get options(){return this.checkDestroyed(),this._options}get config(){return this.checkDestroyed(),this._config}get container(){return this._container}get isDeleted(){return this._isDeleted}set isDeleted(t){this._isDeleted=t}checkDestroyed(){if(this.isDeleted)throw L.create("app-deleted",{appName:this._name})}}let N="12.0.0";function P(t,e={}){let r=t;"object"!=typeof e&&(e={name:e});let i={name:C,automaticDataCollectionEnabled:!0,...e},n=i.name;if("string"!=typeof n||!n)throw L.create("bad-app-name",{appName:String(n)});if(r||(r=(0,a.T9)()),!r)throw L.create("no-options");let o=k.get(n);if(o)if((0,a.bD)(r,o.options)&&(0,a.bD)(i,o.config))return o;else throw L.create("duplicate-app",{appName:n});let h=new s.h1(n);for(let t of S.values())h.addComponent(t);let l=new D(r,i,h);return k.set(n,l),l}function U(t=C){let e=k.get(t);if(!e&&t===C&&(0,a.T9)())return P();if(!e)throw L.create("no-app",{appName:t});return e}function j(t,e,r){let i=T[t]??t;r&&(i+=`-${r}`);let n=i.match(/\s|\//),o=e.match(/\s|\//);if(n||o){let t=[`Unable to register library "${i}" with version "${e}":`];n&&t.push(`library name "${i}" contains illegal characters (whitespace or "/")`),n&&o&&t.push("and"),o&&t.push(`version name "${e}" contains illegal characters (whitespace or "/")`),A.warn(t.join(" "));return}I(new s.uA(`${i}-version`,()=>({library:i,version:e}),"VERSION"))}let M="firebase-heartbeat-store",F=null;function H(){return F||(F=(function(t,e,{blocked:r,upgrade:i,blocking:n,terminated:s}={}){let o=indexedDB.open(t,1),a=d(o);return i&&o.addEventListener("upgradeneeded",t=>{i(d(o.result),t.oldVersion,t.newVersion,d(o.transaction),t)}),r&&o.addEventListener("blocked",t=>r(t.oldVersion,t.newVersion,t)),a.then(t=>{s&&t.addEventListener("close",()=>s()),n&&t.addEventListener("versionchange",t=>n(t.oldVersion,t.newVersion,t))}).catch(()=>{}),a})("firebase-heartbeat-database",0,{upgrade:(t,e)=>{if(0===e)try{t.createObjectStore(M)}catch(t){console.warn(t)}}}).catch(t=>{throw L.create("idb-open",{originalErrorMessage:t.message})})),F}async function V(t){try{let e=(await H()).transaction(M),r=await e.objectStore(M).get(z(t));return await e.done,r}catch(t){if(t instanceof a.g)A.warn(t.message);else{let e=L.create("idb-get",{originalErrorMessage:t?.message});A.warn(e.message)}}}async function q(t,e){try{let r=(await H()).transaction(M,"readwrite"),i=r.objectStore(M);await i.put(e,z(t)),await r.done}catch(t){if(t instanceof a.g)A.warn(t.message);else{let e=L.create("idb-set",{originalErrorMessage:t?.message});A.warn(e.message)}}}function z(t){return`${t.name}!${t.options.appId}`}class ${constructor(t){this.container=t,this._heartbeatsCache=null;let e=this.container.getProvider("app").getImmediate();this._storage=new K(e),this._heartbeatsCachePromise=this._storage.read().then(t=>(this._heartbeatsCache=t,t))}async triggerHeartbeat(){try{let t=this.container.getProvider("platform-logger").getImmediate().getPlatformInfoString(),e=W();if(this._heartbeatsCache?.heartbeats==null&&(this._heartbeatsCache=await this._heartbeatsCachePromise,this._heartbeatsCache?.heartbeats==null)||this._heartbeatsCache.lastSentHeartbeatDate===e||this._heartbeatsCache.heartbeats.some(t=>t.date===e))return;if(this._heartbeatsCache.heartbeats.push({date:e,agent:t}),this._heartbeatsCache.heartbeats.length>30){let t=function(t){if(0===t.length)return -1;let e=0,r=t[0].date;for(let i=1;i<t.length;i++)t[i].date<r&&(r=t[i].date,e=i);return e}(this._heartbeatsCache.heartbeats);this._heartbeatsCache.heartbeats.splice(t,1)}return this._storage.overwrite(this._heartbeatsCache)}catch(t){A.warn(t)}}async getHeartbeatsHeader(){try{if(null===this._heartbeatsCache&&await this._heartbeatsCachePromise,this._heartbeatsCache?.heartbeats==null||0===this._heartbeatsCache.heartbeats.length)return"";let t=W(),{heartbeatsToSend:e,unsentEntries:r}=function(t,e=1024){let r=[],i=t.slice();for(let n of t){let t=r.find(t=>t.agent===n.agent);if(t){if(t.dates.push(n.date),X(r)>e){t.dates.pop();break}}else if(r.push({agent:n.agent,dates:[n.date]}),X(r)>e){r.pop();break}i=i.slice(1)}return{heartbeatsToSend:r,unsentEntries:i}}(this._heartbeatsCache.heartbeats),i=(0,a.Uj)(JSON.stringify({version:2,heartbeats:e}));return this._heartbeatsCache.lastSentHeartbeatDate=t,r.length>0?(this._heartbeatsCache.heartbeats=r,await this._storage.overwrite(this._heartbeatsCache)):(this._heartbeatsCache.heartbeats=[],this._storage.overwrite(this._heartbeatsCache)),i}catch(t){return A.warn(t),""}}}function W(){return new Date().toISOString().substring(0,10)}class K{constructor(t){this.app=t,this._canUseIndexedDBPromise=this.runIndexedDBEnvironmentCheck()}async runIndexedDBEnvironmentCheck(){return!!(0,a.zW)()&&(0,a.eX)().then(()=>!0).catch(()=>!1)}async read(){if(!await this._canUseIndexedDBPromise)return{heartbeats:[]};{let t=await V(this.app);return t?.heartbeats?t:{heartbeats:[]}}}async overwrite(t){if(await this._canUseIndexedDBPromise){let e=await this.read();return q(this.app,{lastSentHeartbeatDate:t.lastSentHeartbeatDate??e.lastSentHeartbeatDate,heartbeats:t.heartbeats})}}async add(t){if(await this._canUseIndexedDBPromise){let e=await this.read();return q(this.app,{lastSentHeartbeatDate:t.lastSentHeartbeatDate??e.lastSentHeartbeatDate,heartbeats:[...e.heartbeats,...t.heartbeats]})}}}function X(t){return(0,a.Uj)(JSON.stringify({version:2,heartbeats:t})).length}I(new s.uA("platform-logger",t=>new _(t),"PRIVATE")),I(new s.uA("heartbeat",t=>new $(t),"PRIVATE")),j(w,E,""),j(w,E,"esm2020"),j("fire-js","")},1280:(t,e,r)=>{"use strict";r.d(e,{cY:()=>m,FA:()=>P,g:()=>N,u:()=>c,Uj:()=>l,Fy:()=>_,tD:()=>q,bD:()=>function t(e,r){if(e===r)return!0;let i=Object.keys(e),n=Object.keys(r);for(let s of i){if(!n.includes(s))return!1;let i=e[s],o=r[s];if(M(i)&&M(o)){if(!t(i,o))return!1}else if(i!==o)return!1}for(let t of n)if(!i.includes(t))return!1;return!0},hp:()=>V,T9:()=>g,Tj:()=>p,yU:()=>d,XA:()=>y,mS:()=>u,Ku:()=>W,ZQ:()=>C,sr:()=>S,zJ:()=>b,c1:()=>O,Im:()=>j,lT:()=>I,zW:()=>L,jZ:()=>T,lV:()=>x,nr:()=>R,Ov:()=>B,gE:()=>v,Am:()=>F,I9:()=>H,P1:()=>A,eX:()=>D});var i=r(9509);let n=function(t){let e=[],r=0;for(let i=0;i<t.length;i++){let n=t.charCodeAt(i);n<128?e[r++]=n:(n<2048?e[r++]=n>>6|192:((64512&n)==55296&&i+1<t.length&&(64512&t.charCodeAt(i+1))==56320?(n=65536+((1023&n)<<10)+(1023&t.charCodeAt(++i)),e[r++]=n>>18|240,e[r++]=n>>12&63|128):e[r++]=n>>12|224,e[r++]=n>>6&63|128),e[r++]=63&n|128)}return e},s=function(t){let e=[],r=0,i=0;for(;r<t.length;){let n=t[r++];if(n<128)e[i++]=String.fromCharCode(n);else if(n>191&&n<224){let s=t[r++];e[i++]=String.fromCharCode((31&n)<<6|63&s)}else if(n>239&&n<365){let s=t[r++],o=((7&n)<<18|(63&s)<<12|(63&t[r++])<<6|63&t[r++])-65536;e[i++]=String.fromCharCode(55296+(o>>10)),e[i++]=String.fromCharCode(56320+(1023&o))}else{let s=t[r++],o=t[r++];e[i++]=String.fromCharCode((15&n)<<12|(63&s)<<6|63&o)}}return e.join("")},o={byteToCharMap_:null,charToByteMap_:null,byteToCharMapWebSafe_:null,charToByteMapWebSafe_:null,ENCODED_VALS_BASE:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",get ENCODED_VALS(){return this.ENCODED_VALS_BASE+"+/="},get ENCODED_VALS_WEBSAFE(){return this.ENCODED_VALS_BASE+"-_."},HAS_NATIVE_SUPPORT:"function"==typeof atob,encodeByteArray(t,e){if(!Array.isArray(t))throw Error("encodeByteArray takes an array as a parameter");this.init_();let r=e?this.byteToCharMapWebSafe_:this.byteToCharMap_,i=[];for(let e=0;e<t.length;e+=3){let n=t[e],s=e+1<t.length,o=s?t[e+1]:0,a=e+2<t.length,h=a?t[e+2]:0,l=n>>2,c=(3&n)<<4|o>>4,u=(15&o)<<2|h>>6,f=63&h;!a&&(f=64,s||(u=64)),i.push(r[l],r[c],r[u],r[f])}return i.join("")},encodeString(t,e){return this.HAS_NATIVE_SUPPORT&&!e?btoa(t):this.encodeByteArray(n(t),e)},decodeString(t,e){return this.HAS_NATIVE_SUPPORT&&!e?atob(t):s(this.decodeStringToByteArray(t,e))},decodeStringToByteArray(t,e){this.init_();let r=e?this.charToByteMapWebSafe_:this.charToByteMap_,i=[];for(let e=0;e<t.length;){let n=r[t.charAt(e++)],s=e<t.length?r[t.charAt(e)]:0,o=++e<t.length?r[t.charAt(e)]:64,h=++e<t.length?r[t.charAt(e)]:64;if(++e,null==n||null==s||null==o||null==h)throw new a;let l=n<<2|s>>4;if(i.push(l),64!==o){let t=s<<4&240|o>>2;if(i.push(t),64!==h){let t=o<<6&192|h;i.push(t)}}}return i},init_(){if(!this.byteToCharMap_){this.byteToCharMap_={},this.charToByteMap_={},this.byteToCharMapWebSafe_={},this.charToByteMapWebSafe_={};for(let t=0;t<this.ENCODED_VALS.length;t++)this.byteToCharMap_[t]=this.ENCODED_VALS.charAt(t),this.charToByteMap_[this.byteToCharMap_[t]]=t,this.byteToCharMapWebSafe_[t]=this.ENCODED_VALS_WEBSAFE.charAt(t),this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[t]]=t,t>=this.ENCODED_VALS_BASE.length&&(this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(t)]=t,this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(t)]=t)}}};class a extends Error{constructor(){super(...arguments),this.name="DecodeBase64StringError"}}let h=function(t){let e=n(t);return o.encodeByteArray(e,!0)},l=function(t){return h(t).replace(/\./g,"")},c=function(t){try{return o.decodeString(t,!0)}catch(t){console.error("base64Decode failed: ",t)}return null};function u(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==r.g)return r.g;throw Error("Unable to locate global object.")}let f=()=>{try{return u().__FIREBASE_DEFAULTS__||(()=>{if(void 0===i||void 0===i.env)return;let t=i.env.__FIREBASE_DEFAULTS__;if(t)return JSON.parse(t)})()||(()=>{let t;if("undefined"==typeof document)return;try{t=document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/)}catch(t){return}let e=t&&c(t[1]);return e&&JSON.parse(e)})()}catch(t){console.info(`Unable to get __FIREBASE_DEFAULTS__ due to: ${t}`);return}},p=t=>f()?.emulatorHosts?.[t],d=t=>{let e=p(t);if(!e)return;let r=e.lastIndexOf(":");if(r<=0||r+1===e.length)throw Error(`Invalid host ${e} with no separate hostname and port!`);let i=parseInt(e.substring(r+1),10);return"["===e[0]?[e.substring(1,r-1),i]:[e.substring(0,r),i]},g=()=>f()?.config,y=t=>f()?.[`_${t}`];class m{constructor(){this.reject=()=>{},this.resolve=()=>{},this.promise=new Promise((t,e)=>{this.resolve=t,this.reject=e})}wrapCallback(t){return(e,r)=>{e?this.reject(e):this.resolve(r),"function"==typeof t&&(this.promise.catch(()=>{}),1===t.length?t(e):t(e,r))}}}function b(t){try{return(t.startsWith("http://")||t.startsWith("https://")?new URL(t).hostname:t).endsWith(".cloudworkstations.dev")}catch{return!1}}async function v(t){return(await fetch(t,{credentials:"include"})).ok}function _(t,e){if(t.uid)throw Error('The "uid" field is no longer supported by mockUserToken. Please use "sub" instead for Firebase Auth User ID.');let r=e||"demo-project",i=t.iat||0,n=t.sub||t.user_id;if(!n)throw Error("mockUserToken must contain 'sub' or 'user_id' field!");let s={iss:`https://securetoken.google.com/${r}`,aud:r,iat:i,exp:i+3600,auth_time:i,sub:n,user_id:n,firebase:{sign_in_provider:"custom",identities:{}},...t};return[l(JSON.stringify({alg:"none",type:"JWT"})),l(JSON.stringify(s)),""].join(".")}let w={},E=!1;function A(t,e){if("undefined"==typeof window||"undefined"==typeof document||!b(window.location.host)||w[t]===e||w[t]||E)return;function r(t){return`__firebase__banner__${t}`}w[t]=e;let i="__firebase__banner",n=function(){let t={prod:[],emulator:[]};for(let e of Object.keys(w))w[e]?t.emulator.push(e):t.prod.push(e);return t}().prod.length>0;function s(){let t,e,s=(t=document.getElementById(i),e=!1,t||((t=document.createElement("div")).setAttribute("id",i),e=!0),{created:e,element:t}),o=r("text"),a=document.getElementById(o)||document.createElement("span"),h=r("learnmore"),l=document.getElementById(h)||document.createElement("a"),c=r("preprendIcon"),u=document.getElementById(c)||document.createElementNS("http://www.w3.org/2000/svg","svg");if(s.created){let t=s.element;t.style.display="flex",t.style.background="#7faaf0",t.style.position="fixed",t.style.bottom="5px",t.style.left="5px",t.style.padding=".5em",t.style.borderRadius="5px",t.style.alignItems="center",l.setAttribute("id",h),l.innerText="Learn more",l.href="https://firebase.google.com/docs/studio/preview-apps#preview-backend",l.setAttribute("target","__blank"),l.style.paddingLeft="5px",l.style.textDecoration="underline";let e=function(){let t=document.createElement("span");return t.style.cursor="pointer",t.style.marginLeft="16px",t.style.fontSize="24px",t.innerHTML=" &times;",t.onclick=()=>{E=!0;let t=document.getElementById(i);t&&t.remove()},t}();u.setAttribute("width","24"),u.setAttribute("id",c),u.setAttribute("height","24"),u.setAttribute("viewBox","0 0 24 24"),u.setAttribute("fill","none"),u.style.marginLeft="-6px",t.append(u,a,l,e),document.body.appendChild(t)}n?(a.innerText="Preview backend disconnected.",u.innerHTML=`<g clip-path="url(#clip0_6013_33858)">
<path d="M4.8 17.6L12 5.6L19.2 17.6H4.8ZM6.91667 16.4H17.0833L12 7.93333L6.91667 16.4ZM12 15.6C12.1667 15.6 12.3056 15.5444 12.4167 15.4333C12.5389 15.3111 12.6 15.1667 12.6 15C12.6 14.8333 12.5389 14.6944 12.4167 14.5833C12.3056 14.4611 12.1667 14.4 12 14.4C11.8333 14.4 11.6889 14.4611 11.5667 14.5833C11.4556 14.6944 11.4 14.8333 11.4 15C11.4 15.1667 11.4556 15.3111 11.5667 15.4333C11.6889 15.5444 11.8333 15.6 12 15.6ZM11.4 13.6H12.6V10.4H11.4V13.6Z" fill="#212121"/>
</g>
<defs>
<clipPath id="clip0_6013_33858">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>`):(u.innerHTML=`<g clip-path="url(#clip0_6083_34804)">
<path d="M11.4 15.2H12.6V11.2H11.4V15.2ZM12 10C12.1667 10 12.3056 9.94444 12.4167 9.83333C12.5389 9.71111 12.6 9.56667 12.6 9.4C12.6 9.23333 12.5389 9.09444 12.4167 8.98333C12.3056 8.86111 12.1667 8.8 12 8.8C11.8333 8.8 11.6889 8.86111 11.5667 8.98333C11.4556 9.09444 11.4 9.23333 11.4 9.4C11.4 9.56667 11.4556 9.71111 11.5667 9.83333C11.6889 9.94444 11.8333 10 12 10ZM12 18.4C11.1222 18.4 10.2944 18.2333 9.51667 17.9C8.73889 17.5667 8.05556 17.1111 7.46667 16.5333C6.88889 15.9444 6.43333 15.2611 6.1 14.4833C5.76667 13.7056 5.6 12.8778 5.6 12C5.6 11.1111 5.76667 10.2833 6.1 9.51667C6.43333 8.73889 6.88889 8.06111 7.46667 7.48333C8.05556 6.89444 8.73889 6.43333 9.51667 6.1C10.2944 5.76667 11.1222 5.6 12 5.6C12.8889 5.6 13.7167 5.76667 14.4833 6.1C15.2611 6.43333 15.9389 6.89444 16.5167 7.48333C17.1056 8.06111 17.5667 8.73889 17.9 9.51667C18.2333 10.2833 18.4 11.1111 18.4 12C18.4 12.8778 18.2333 13.7056 17.9 14.4833C17.5667 15.2611 17.1056 15.9444 16.5167 16.5333C15.9389 17.1111 15.2611 17.5667 14.4833 17.9C13.7167 18.2333 12.8889 18.4 12 18.4ZM12 17.2C13.4444 17.2 14.6722 16.6944 15.6833 15.6833C16.6944 14.6722 17.2 13.4444 17.2 12C17.2 10.5556 16.6944 9.32778 15.6833 8.31667C14.6722 7.30555 13.4444 6.8 12 6.8C10.5556 6.8 9.32778 7.30555 8.31667 8.31667C7.30556 9.32778 6.8 10.5556 6.8 12C6.8 13.4444 7.30556 14.6722 8.31667 15.6833C9.32778 16.6944 10.5556 17.2 12 17.2Z" fill="#212121"/>
</g>
<defs>
<clipPath id="clip0_6083_34804">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>`,a.innerText="Preview backend running in this workspace."),a.setAttribute("id",o)}"loading"===document.readyState?window.addEventListener("DOMContentLoaded",s):s()}function C(){return"undefined"!=typeof navigator&&"string"==typeof navigator.userAgent?navigator.userAgent:""}function T(){return"undefined"!=typeof window&&!!(window.cordova||window.phonegap||window.PhoneGap)&&/ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(C())}function k(){let t=f()?.forceEnvironment;if("node"===t)return!0;if("browser"===t)return!1;try{return"[object process]"===Object.prototype.toString.call(r.g.process)}catch(t){return!1}}function O(){return"undefined"!=typeof navigator&&"Cloudflare-Workers"===navigator.userAgent}function S(){let t="object"==typeof chrome?chrome.runtime:"object"==typeof browser?browser.runtime:void 0;return"object"==typeof t&&void 0!==t.id}function x(){return"object"==typeof navigator&&"ReactNative"===navigator.product}function I(){let t=C();return t.indexOf("MSIE ")>=0||t.indexOf("Trident/")>=0}function R(){return!k()&&!!navigator.userAgent&&navigator.userAgent.includes("Safari")&&!navigator.userAgent.includes("Chrome")}function B(){return!k()&&!!navigator.userAgent&&(navigator.userAgent.includes("Safari")||navigator.userAgent.includes("WebKit"))&&!navigator.userAgent.includes("Chrome")}function L(){try{return"object"==typeof indexedDB}catch(t){return!1}}function D(){return new Promise((t,e)=>{try{let r=!0,i="validate-browser-context-for-indexeddb-analytics-module",n=self.indexedDB.open(i);n.onsuccess=()=>{n.result.close(),r||self.indexedDB.deleteDatabase(i),t(!0)},n.onupgradeneeded=()=>{r=!1},n.onerror=()=>{e(n.error?.message||"")}}catch(t){e(t)}})}class N extends Error{constructor(t,e,r){super(e),this.code=t,this.customData=r,this.name="FirebaseError",Object.setPrototypeOf(this,N.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,P.prototype.create)}}class P{constructor(t,e,r){this.service=t,this.serviceName=e,this.errors=r}create(t,...e){var r,i;let n=e[0]||{},s=`${this.service}/${t}`,o=this.errors[t],a=o?(r=o,i=n,r.replace(U,(t,e)=>{let r=i[e];return null!=r?String(r):`<${e}?>`})):"Error",h=`${this.serviceName}: ${a} (${s}).`;return new N(s,h,n)}}let U=/\{\$([^}]+)}/g;function j(t){for(let e in t)if(Object.prototype.hasOwnProperty.call(t,e))return!1;return!0}function M(t){return null!==t&&"object"==typeof t}function F(t){let e=[];for(let[r,i]of Object.entries(t))Array.isArray(i)?i.forEach(t=>{e.push(encodeURIComponent(r)+"="+encodeURIComponent(t))}):e.push(encodeURIComponent(r)+"="+encodeURIComponent(i));return e.length?"&"+e.join("&"):""}function H(t){let e={};return t.replace(/^\?/,"").split("&").forEach(t=>{if(t){let[r,i]=t.split("=");e[decodeURIComponent(r)]=decodeURIComponent(i)}}),e}function V(t){let e=t.indexOf("?");if(!e)return"";let r=t.indexOf("#",e);return t.substring(e,r>0?r:void 0)}function q(t,e){let r=new z(t,e);return r.subscribe.bind(r)}class z{constructor(t,e){this.observers=[],this.unsubscribes=[],this.observerCount=0,this.task=Promise.resolve(),this.finalized=!1,this.onNoObservers=e,this.task.then(()=>{t(this)}).catch(t=>{this.error(t)})}next(t){this.forEachObserver(e=>{e.next(t)})}error(t){this.forEachObserver(e=>{e.error(t)}),this.close(t)}complete(){this.forEachObserver(t=>{t.complete()}),this.close()}subscribe(t,e,r){let i;if(void 0===t&&void 0===e&&void 0===r)throw Error("Missing Observer.");void 0===(i=!function(t,e){if("object"!=typeof t||null===t)return!1;for(let r of e)if(r in t&&"function"==typeof t[r])return!0;return!1}(t,["next","error","complete"])?{next:t,error:e,complete:r}:t).next&&(i.next=$),void 0===i.error&&(i.error=$),void 0===i.complete&&(i.complete=$);let n=this.unsubscribeOne.bind(this,this.observers.length);return this.finalized&&this.task.then(()=>{try{this.finalError?i.error(this.finalError):i.complete()}catch(t){}}),this.observers.push(i),n}unsubscribeOne(t){void 0!==this.observers&&void 0!==this.observers[t]&&(delete this.observers[t],this.observerCount-=1,0===this.observerCount&&void 0!==this.onNoObservers&&this.onNoObservers(this))}forEachObserver(t){if(!this.finalized)for(let e=0;e<this.observers.length;e++)this.sendOne(e,t)}sendOne(t,e){this.task.then(()=>{if(void 0!==this.observers&&void 0!==this.observers[t])try{e(this.observers[t])}catch(t){"undefined"!=typeof console&&console.error&&console.error(t)}})}close(t){this.finalized||(this.finalized=!0,void 0!==t&&(this.finalError=t),this.task.then(()=>{this.observers=void 0,this.onNoObservers=void 0}))}}function $(){}function W(t){return t&&t._delegate?t._delegate:t}},2107:(t,e,r)=>{"use strict";r.d(e,{VV:()=>n,jz:()=>i});var i,n,s="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},o={};(function(){function t(){this.blockSize=-1,this.blockSize=64,this.g=[,,,,],this.B=Array(this.blockSize),this.o=this.h=0,this.s()}var e=function(){this.blockSize=-1};function r(){}function s(t,e,r){r||(r=0);var i=Array(16);if("string"==typeof e)for(var n=0;16>n;++n)i[n]=e.charCodeAt(r++)|e.charCodeAt(r++)<<8|e.charCodeAt(r++)<<16|e.charCodeAt(r++)<<24;else for(n=0;16>n;++n)i[n]=e[r++]|e[r++]<<8|e[r++]<<16|e[r++]<<24;e=t.g[0],r=t.g[1],n=t.g[2];var s=t.g[3],o=e+(s^r&(n^s))+i[0]+0xd76aa478|0;o=s+(n^(e=r+(o<<7|o>>>25))&(r^n))+i[1]+0xe8c7b756|0,o=n+(r^(s=e+(o<<12|o>>>20))&(e^r))+i[2]+0x242070db|0,o=r+(e^(n=s+(o<<17|o>>>15))&(s^e))+i[3]+0xc1bdceee|0,o=e+(s^(r=n+(o<<22|o>>>10))&(n^s))+i[4]+0xf57c0faf|0,o=s+(n^(e=r+(o<<7|o>>>25))&(r^n))+i[5]+0x4787c62a|0,o=n+(r^(s=e+(o<<12|o>>>20))&(e^r))+i[6]+0xa8304613|0,o=r+(e^(n=s+(o<<17|o>>>15))&(s^e))+i[7]+0xfd469501|0,o=e+(s^(r=n+(o<<22|o>>>10))&(n^s))+i[8]+0x698098d8|0,o=s+(n^(e=r+(o<<7|o>>>25))&(r^n))+i[9]+0x8b44f7af|0,o=n+(r^(s=e+(o<<12|o>>>20))&(e^r))+i[10]+0xffff5bb1|0,o=r+(e^(n=s+(o<<17|o>>>15))&(s^e))+i[11]+0x895cd7be|0,o=e+(s^(r=n+(o<<22|o>>>10))&(n^s))+i[12]+0x6b901122|0,o=s+(n^(e=r+(o<<7|o>>>25))&(r^n))+i[13]+0xfd987193|0,o=n+(r^(s=e+(o<<12|o>>>20))&(e^r))+i[14]+0xa679438e|0,o=r+(e^(n=s+(o<<17|o>>>15))&(s^e))+i[15]+0x49b40821|0,r=n+(o<<22|o>>>10),o=e+(n^s&(r^n))+i[1]+0xf61e2562|0,e=r+(o<<5|o>>>27),o=s+(r^n&(e^r))+i[6]+0xc040b340|0,s=e+(o<<9|o>>>23),o=n+(e^r&(s^e))+i[11]+0x265e5a51|0,n=s+(o<<14|o>>>18),o=r+(s^e&(n^s))+i[0]+0xe9b6c7aa|0,r=n+(o<<20|o>>>12),o=e+(n^s&(r^n))+i[5]+0xd62f105d|0,e=r+(o<<5|o>>>27),o=s+(r^n&(e^r))+i[10]+0x2441453|0,s=e+(o<<9|o>>>23),o=n+(e^r&(s^e))+i[15]+0xd8a1e681|0,n=s+(o<<14|o>>>18),o=r+(s^e&(n^s))+i[4]+0xe7d3fbc8|0,r=n+(o<<20|o>>>12),o=e+(n^s&(r^n))+i[9]+0x21e1cde6|0,e=r+(o<<5|o>>>27),o=s+(r^n&(e^r))+i[14]+0xc33707d6|0,s=e+(o<<9|o>>>23),o=n+(e^r&(s^e))+i[3]+0xf4d50d87|0,n=s+(o<<14|o>>>18),o=r+(s^e&(n^s))+i[8]+0x455a14ed|0,r=n+(o<<20|o>>>12),o=e+(n^s&(r^n))+i[13]+0xa9e3e905|0,e=r+(o<<5|o>>>27),o=s+(r^n&(e^r))+i[2]+0xfcefa3f8|0,s=e+(o<<9|o>>>23),o=n+(e^r&(s^e))+i[7]+0x676f02d9|0,n=s+(o<<14|o>>>18),o=r+(s^e&(n^s))+i[12]+0x8d2a4c8a|0,o=e+((r=n+(o<<20|o>>>12))^n^s)+i[5]+0xfffa3942|0,o=s+((e=r+(o<<4|o>>>28))^r^n)+i[8]+0x8771f681|0,o=n+((s=e+(o<<11|o>>>21))^e^r)+i[11]+0x6d9d6122|0,o=r+((n=s+(o<<16|o>>>16))^s^e)+i[14]+0xfde5380c|0,o=e+((r=n+(o<<23|o>>>9))^n^s)+i[1]+0xa4beea44|0,o=s+((e=r+(o<<4|o>>>28))^r^n)+i[4]+0x4bdecfa9|0,o=n+((s=e+(o<<11|o>>>21))^e^r)+i[7]+0xf6bb4b60|0,o=r+((n=s+(o<<16|o>>>16))^s^e)+i[10]+0xbebfbc70|0,o=e+((r=n+(o<<23|o>>>9))^n^s)+i[13]+0x289b7ec6|0,o=s+((e=r+(o<<4|o>>>28))^r^n)+i[0]+0xeaa127fa|0,o=n+((s=e+(o<<11|o>>>21))^e^r)+i[3]+0xd4ef3085|0,o=r+((n=s+(o<<16|o>>>16))^s^e)+i[6]+0x4881d05|0,o=e+((r=n+(o<<23|o>>>9))^n^s)+i[9]+0xd9d4d039|0,o=s+((e=r+(o<<4|o>>>28))^r^n)+i[12]+0xe6db99e5|0,o=n+((s=e+(o<<11|o>>>21))^e^r)+i[15]+0x1fa27cf8|0,o=r+((n=s+(o<<16|o>>>16))^s^e)+i[2]+0xc4ac5665|0,r=n+(o<<23|o>>>9),o=e+(n^(r|~s))+i[0]+0xf4292244|0,e=r+(o<<6|o>>>26),o=s+(r^(e|~n))+i[7]+0x432aff97|0,s=e+(o<<10|o>>>22),o=n+(e^(s|~r))+i[14]+0xab9423a7|0,n=s+(o<<15|o>>>17),o=r+(s^(n|~e))+i[5]+0xfc93a039|0,r=n+(o<<21|o>>>11),o=e+(n^(r|~s))+i[12]+0x655b59c3|0,e=r+(o<<6|o>>>26),o=s+(r^(e|~n))+i[3]+0x8f0ccc92|0,s=e+(o<<10|o>>>22),o=n+(e^(s|~r))+i[10]+0xffeff47d|0,n=s+(o<<15|o>>>17),o=r+(s^(n|~e))+i[1]+0x85845dd1|0,r=n+(o<<21|o>>>11),o=e+(n^(r|~s))+i[8]+0x6fa87e4f|0,e=r+(o<<6|o>>>26),o=s+(r^(e|~n))+i[15]+0xfe2ce6e0|0,s=e+(o<<10|o>>>22),o=n+(e^(s|~r))+i[6]+0xa3014314|0,n=s+(o<<15|o>>>17),o=r+(s^(n|~e))+i[13]+0x4e0811a1|0,r=n+(o<<21|o>>>11),o=e+(n^(r|~s))+i[4]+0xf7537e82|0,e=r+(o<<6|o>>>26),o=s+(r^(e|~n))+i[11]+0xbd3af235|0,s=e+(o<<10|o>>>22),o=n+(e^(s|~r))+i[2]+0x2ad7d2bb|0,n=s+(o<<15|o>>>17),o=r+(s^(n|~e))+i[9]+0xeb86d391|0,t.g[0]=t.g[0]+e|0,t.g[1]=t.g[1]+(n+(o<<21|o>>>11))|0,t.g[2]=t.g[2]+n|0,t.g[3]=t.g[3]+s|0}function a(t,e){this.h=e;for(var r=[],i=!0,n=t.length-1;0<=n;n--){var s=0|t[n];i&&s==e||(r[n]=s,i=!1)}this.g=r}r.prototype=e.prototype,t.D=e.prototype,t.prototype=new r,t.prototype.constructor=t,t.C=function(t,r,i){for(var n=Array(arguments.length-2),s=2;s<arguments.length;s++)n[s-2]=arguments[s];return e.prototype[r].apply(t,n)},t.prototype.s=function(){this.g[0]=0x67452301,this.g[1]=0xefcdab89,this.g[2]=0x98badcfe,this.g[3]=0x10325476,this.o=this.h=0},t.prototype.u=function(t,e){void 0===e&&(e=t.length);for(var r=e-this.blockSize,i=this.B,n=this.h,o=0;o<e;){if(0==n)for(;o<=r;)s(this,t,o),o+=this.blockSize;if("string"==typeof t){for(;o<e;)if(i[n++]=t.charCodeAt(o++),n==this.blockSize){s(this,i),n=0;break}}else for(;o<e;)if(i[n++]=t[o++],n==this.blockSize){s(this,i),n=0;break}}this.h=n,this.o+=e},t.prototype.v=function(){var t=Array((56>this.h?this.blockSize:2*this.blockSize)-this.h);t[0]=128;for(var e=1;e<t.length-8;++e)t[e]=0;var r=8*this.o;for(e=t.length-8;e<t.length;++e)t[e]=255&r,r/=256;for(this.u(t),t=Array(16),e=r=0;4>e;++e)for(var i=0;32>i;i+=8)t[r++]=this.g[e]>>>i&255;return t};var h,l={};function c(t){var e;return -128<=t&&128>t?(e=function(t){return new a([0|t],0>t?-1:0)},Object.prototype.hasOwnProperty.call(l,t)?l[t]:l[t]=e(t)):new a([0|t],0>t?-1:0)}function u(t){if(isNaN(t)||!isFinite(t))return f;if(0>t)return m(u(-t));for(var e=[],r=1,i=0;t>=r;i++)e[i]=t/r|0,r*=0x100000000;return new a(e,0)}var f=c(0),p=c(1),d=c(0x1000000);function g(t){if(0!=t.h)return!1;for(var e=0;e<t.g.length;e++)if(0!=t.g[e])return!1;return!0}function y(t){return -1==t.h}function m(t){for(var e=t.g.length,r=[],i=0;i<e;i++)r[i]=~t.g[i];return new a(r,~t.h).add(p)}function b(t,e){return t.add(m(e))}function v(t,e){for(;(65535&t[e])!=t[e];)t[e+1]+=t[e]>>>16,t[e]&=65535,e++}function _(t,e){this.g=t,this.h=e}function w(t,e){if(g(e))throw Error("division by zero");if(g(t))return new _(f,f);if(y(t))return e=w(m(t),e),new _(m(e.g),m(e.h));if(y(e))return e=w(t,m(e)),new _(m(e.g),e.h);if(30<t.g.length){if(y(t)||y(e))throw Error("slowDivide_ only works with positive integers.");for(var r=p,i=e;0>=i.l(t);)r=E(r),i=E(i);var n=A(r,1),s=A(i,1);for(i=A(i,2),r=A(r,2);!g(i);){var o=s.add(i);0>=o.l(t)&&(n=n.add(r),s=o),i=A(i,1),r=A(r,1)}return e=b(t,n.j(e)),new _(n,e)}for(n=f;0<=t.l(e);){for(i=48>=(i=Math.ceil(Math.log(r=Math.max(1,Math.floor(t.m()/e.m())))/Math.LN2))?1:Math.pow(2,i-48),o=(s=u(r)).j(e);y(o)||0<o.l(t);)r-=i,o=(s=u(r)).j(e);g(s)&&(s=p),n=n.add(s),t=b(t,o)}return new _(n,t)}function E(t){for(var e=t.g.length+1,r=[],i=0;i<e;i++)r[i]=t.i(i)<<1|t.i(i-1)>>>31;return new a(r,t.h)}function A(t,e){var r=e>>5;e%=32;for(var i=t.g.length-r,n=[],s=0;s<i;s++)n[s]=0<e?t.i(s+r)>>>e|t.i(s+r+1)<<32-e:t.i(s+r);return new a(n,t.h)}(h=a.prototype).m=function(){if(y(this))return-m(this).m();for(var t=0,e=1,r=0;r<this.g.length;r++){var i=this.i(r);t+=(0<=i?i:0x100000000+i)*e,e*=0x100000000}return t},h.toString=function(t){if(2>(t=t||10)||36<t)throw Error("radix out of range: "+t);if(g(this))return"0";if(y(this))return"-"+m(this).toString(t);for(var e=u(Math.pow(t,6)),r=this,i="";;){var n=w(r,e).g,s=((0<(r=b(r,n.j(e))).g.length?r.g[0]:r.h)>>>0).toString(t);if(g(r=n))return s+i;for(;6>s.length;)s="0"+s;i=s+i}},h.i=function(t){return 0>t?0:t<this.g.length?this.g[t]:this.h},h.l=function(t){return y(t=b(this,t))?-1:+!g(t)},h.abs=function(){return y(this)?m(this):this},h.add=function(t){for(var e=Math.max(this.g.length,t.g.length),r=[],i=0,n=0;n<=e;n++){var s=i+(65535&this.i(n))+(65535&t.i(n)),o=(s>>>16)+(this.i(n)>>>16)+(t.i(n)>>>16);i=o>>>16,s&=65535,o&=65535,r[n]=o<<16|s}return new a(r,-0x80000000&r[r.length-1]?-1:0)},h.j=function(t){if(g(this)||g(t))return f;if(y(this))return y(t)?m(this).j(m(t)):m(m(this).j(t));if(y(t))return m(this.j(m(t)));if(0>this.l(d)&&0>t.l(d))return u(this.m()*t.m());for(var e=this.g.length+t.g.length,r=[],i=0;i<2*e;i++)r[i]=0;for(i=0;i<this.g.length;i++)for(var n=0;n<t.g.length;n++){var s=this.i(i)>>>16,o=65535&this.i(i),h=t.i(n)>>>16,l=65535&t.i(n);r[2*i+2*n]+=o*l,v(r,2*i+2*n),r[2*i+2*n+1]+=s*l,v(r,2*i+2*n+1),r[2*i+2*n+1]+=o*h,v(r,2*i+2*n+1),r[2*i+2*n+2]+=s*h,v(r,2*i+2*n+2)}for(i=0;i<e;i++)r[i]=r[2*i+1]<<16|r[2*i];for(i=e;i<2*e;i++)r[i]=0;return new a(r,0)},h.A=function(t){return w(this,t).h},h.and=function(t){for(var e=Math.max(this.g.length,t.g.length),r=[],i=0;i<e;i++)r[i]=this.i(i)&t.i(i);return new a(r,this.h&t.h)},h.or=function(t){for(var e=Math.max(this.g.length,t.g.length),r=[],i=0;i<e;i++)r[i]=this.i(i)|t.i(i);return new a(r,this.h|t.h)},h.xor=function(t){for(var e=Math.max(this.g.length,t.g.length),r=[],i=0;i<e;i++)r[i]=this.i(i)^t.i(i);return new a(r,this.h^t.h)},t.prototype.digest=t.prototype.v,t.prototype.reset=t.prototype.s,t.prototype.update=t.prototype.u,n=o.Md5=t,a.prototype.add=a.prototype.add,a.prototype.multiply=a.prototype.j,a.prototype.modulo=a.prototype.A,a.prototype.compare=a.prototype.l,a.prototype.toNumber=a.prototype.m,a.prototype.toString=a.prototype.toString,a.prototype.getBits=a.prototype.i,a.fromNumber=u,a.fromString=function t(e,r){if(0==e.length)throw Error("number format error: empty string");if(2>(r=r||10)||36<r)throw Error("radix out of range: "+r);if("-"==e.charAt(0))return m(t(e.substring(1),r));if(0<=e.indexOf("-"))throw Error('number format error: interior "-" character');for(var i=u(Math.pow(r,8)),n=f,s=0;s<e.length;s+=8){var o=Math.min(8,e.length-s),a=parseInt(e.substring(s,s+o),r);8>o?(o=u(Math.pow(r,o)),n=n.j(o).add(u(a))):n=(n=n.j(i)).add(u(a))}return n},i=o.Integer=a}).apply(void 0!==s?s:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},2881:(t,e,r)=>{"use strict";r.d(e,{h1:()=>a,uA:()=>n});var i=r(1280);class n{constructor(t,e,r){this.name=t,this.instanceFactory=e,this.type=r,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(t){return this.instantiationMode=t,this}setMultipleInstances(t){return this.multipleInstances=t,this}setServiceProps(t){return this.serviceProps=t,this}setInstanceCreatedCallback(t){return this.onInstanceCreated=t,this}}let s="[DEFAULT]";class o{constructor(t,e){this.name=t,this.container=e,this.component=null,this.instances=new Map,this.instancesDeferred=new Map,this.instancesOptions=new Map,this.onInitCallbacks=new Map}get(t){let e=this.normalizeInstanceIdentifier(t);if(!this.instancesDeferred.has(e)){let t=new i.cY;if(this.instancesDeferred.set(e,t),this.isInitialized(e)||this.shouldAutoInitialize())try{let r=this.getOrInitializeService({instanceIdentifier:e});r&&t.resolve(r)}catch(t){}}return this.instancesDeferred.get(e).promise}getImmediate(t){let e=this.normalizeInstanceIdentifier(t?.identifier),r=t?.optional??!1;if(this.isInitialized(e)||this.shouldAutoInitialize())try{return this.getOrInitializeService({instanceIdentifier:e})}catch(t){if(r)return null;throw t}if(r)return null;throw Error(`Service ${this.name} is not available`)}getComponent(){return this.component}setComponent(t){if(t.name!==this.name)throw Error(`Mismatching Component ${t.name} for Provider ${this.name}.`);if(this.component)throw Error(`Component for ${this.name} has already been provided`);if(this.component=t,this.shouldAutoInitialize()){if("EAGER"===t.instantiationMode)try{this.getOrInitializeService({instanceIdentifier:s})}catch(t){}for(let[t,e]of this.instancesDeferred.entries()){let r=this.normalizeInstanceIdentifier(t);try{let t=this.getOrInitializeService({instanceIdentifier:r});e.resolve(t)}catch(t){}}}}clearInstance(t=s){this.instancesDeferred.delete(t),this.instancesOptions.delete(t),this.instances.delete(t)}async delete(){let t=Array.from(this.instances.values());await Promise.all([...t.filter(t=>"INTERNAL"in t).map(t=>t.INTERNAL.delete()),...t.filter(t=>"_delete"in t).map(t=>t._delete())])}isComponentSet(){return null!=this.component}isInitialized(t=s){return this.instances.has(t)}getOptions(t=s){return this.instancesOptions.get(t)||{}}initialize(t={}){let{options:e={}}=t,r=this.normalizeInstanceIdentifier(t.instanceIdentifier);if(this.isInitialized(r))throw Error(`${this.name}(${r}) has already been initialized`);if(!this.isComponentSet())throw Error(`Component ${this.name} has not been registered yet`);let i=this.getOrInitializeService({instanceIdentifier:r,options:e});for(let[t,e]of this.instancesDeferred.entries())r===this.normalizeInstanceIdentifier(t)&&e.resolve(i);return i}onInit(t,e){let r=this.normalizeInstanceIdentifier(e),i=this.onInitCallbacks.get(r)??new Set;i.add(t),this.onInitCallbacks.set(r,i);let n=this.instances.get(r);return n&&t(n,r),()=>{i.delete(t)}}invokeOnInitCallbacks(t,e){let r=this.onInitCallbacks.get(e);if(r)for(let i of r)try{i(t,e)}catch{}}getOrInitializeService({instanceIdentifier:t,options:e={}}){var r;let i=this.instances.get(t);if(!i&&this.component&&(i=this.component.instanceFactory(this.container,{instanceIdentifier:(r=t)===s?void 0:r,options:e}),this.instances.set(t,i),this.instancesOptions.set(t,e),this.invokeOnInitCallbacks(i,t),this.component.onInstanceCreated))try{this.component.onInstanceCreated(this.container,t,i)}catch{}return i||null}normalizeInstanceIdentifier(t=s){return this.component?this.component.multipleInstances?t:s:t}shouldAutoInitialize(){return!!this.component&&"EXPLICIT"!==this.component.instantiationMode}}class a{constructor(t){this.name=t,this.providers=new Map}addComponent(t){let e=this.getProvider(t.name);if(e.isComponentSet())throw Error(`Component ${t.name} has already been registered with ${this.name}`);e.setComponent(t)}addOrOverwriteComponent(t){this.getProvider(t.name).isComponentSet()&&this.providers.delete(t.name),this.addComponent(t)}getProvider(t){if(this.providers.has(t))return this.providers.get(t);let e=new o(t,this);return this.providers.set(t,e),e}getProviders(){return Array.from(this.providers.values())}}},3915:(t,e,r)=>{"use strict";r.d(e,{Wp:()=>i.Wp});var i=r(1055);(0,i.KO)("firebase","12.0.0","app")},4134:(t,e,r)=>{"use strict";var i=r(7719),n=r(7610),s="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function o(t){if(t>0x7fffffff)throw RangeError('The value "'+t+'" is invalid for option "size"');var e=new Uint8Array(t);return Object.setPrototypeOf(e,a.prototype),e}function a(t,e,r){if("number"==typeof t){if("string"==typeof e)throw TypeError('The "string" argument must be of type string. Received type number');return c(t)}return h(t,e,r)}function h(t,e,r){if("string"==typeof t){var i=t,n=e;if(("string"!=typeof n||""===n)&&(n="utf8"),!a.isEncoding(n))throw TypeError("Unknown encoding: "+n);var s=0|d(i,n),h=o(s),l=h.write(i,n);return l!==s&&(h=h.slice(0,l)),h}if(ArrayBuffer.isView(t)){var c=t;if(x(c,Uint8Array)){var g=new Uint8Array(c);return f(g.buffer,g.byteOffset,g.byteLength)}return u(c)}if(null==t)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);if(x(t,ArrayBuffer)||t&&x(t.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(x(t,SharedArrayBuffer)||t&&x(t.buffer,SharedArrayBuffer)))return f(t,e,r);if("number"==typeof t)throw TypeError('The "value" argument must not be of type number. Received type number');var y=t.valueOf&&t.valueOf();if(null!=y&&y!==t)return a.from(y,e,r);var m=function(t){if(a.isBuffer(t)){var e=0|p(t.length),r=o(e);return 0===r.length||t.copy(r,0,0,e),r}return void 0!==t.length?"number"!=typeof t.length||function(t){return t!=t}(t.length)?o(0):u(t):"Buffer"===t.type&&Array.isArray(t.data)?u(t.data):void 0}(t);if(m)return m;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof t[Symbol.toPrimitive])return a.from(t[Symbol.toPrimitive]("string"),e,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t)}function l(t){if("number"!=typeof t)throw TypeError('"size" argument must be of type number');if(t<0)throw RangeError('The value "'+t+'" is invalid for option "size"')}function c(t){return l(t),o(t<0?0:0|p(t))}function u(t){for(var e=t.length<0?0:0|p(t.length),r=o(e),i=0;i<e;i+=1)r[i]=255&t[i];return r}function f(t,e,r){var i;if(e<0||t.byteLength<e)throw RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(i=void 0===e&&void 0===r?new Uint8Array(t):void 0===r?new Uint8Array(t,e):new Uint8Array(t,e,r),a.prototype),i}function p(t){if(t>=0x7fffffff)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|t}function d(t,e){if(a.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||x(t,ArrayBuffer))return t.byteLength;if("string"!=typeof t)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof t);var r=t.length,i=arguments.length>2&&!0===arguments[2];if(!i&&0===r)return 0;for(var n=!1;;)switch(e){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return k(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return O(t).length;default:if(n)return i?-1:k(t).length;e=(""+e).toLowerCase(),n=!0}}function g(t,e,r){var n,s,o,a=!1;if((void 0===e||e<0)&&(e=0),e>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(e>>>=0)))return"";for(t||(t="utf8");;)switch(t){case"hex":return function(t,e,r){var i=t.length;(!e||e<0)&&(e=0),(!r||r<0||r>i)&&(r=i);for(var n="",s=e;s<r;++s)n+=I[t[s]];return n}(this,e,r);case"utf8":case"utf-8":return v(this,e,r);case"ascii":return function(t,e,r){var i="";r=Math.min(t.length,r);for(var n=e;n<r;++n)i+=String.fromCharCode(127&t[n]);return i}(this,e,r);case"latin1":case"binary":return function(t,e,r){var i="";r=Math.min(t.length,r);for(var n=e;n<r;++n)i+=String.fromCharCode(t[n]);return i}(this,e,r);case"base64":return n=this,s=e,o=r,0===s&&o===n.length?i.fromByteArray(n):i.fromByteArray(n.slice(s,o));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(t,e,r){for(var i=t.slice(e,r),n="",s=0;s<i.length-1;s+=2)n+=String.fromCharCode(i[s]+256*i[s+1]);return n}(this,e,r);default:if(a)throw TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),a=!0}}function y(t,e,r){var i=t[e];t[e]=t[r],t[r]=i}function m(t,e,r,i,n){var s;if(0===t.length)return -1;if("string"==typeof r?(i=r,r=0):r>0x7fffffff?r=0x7fffffff:r<-0x80000000&&(r=-0x80000000),(s=r*=1)!=s&&(r=n?0:t.length-1),r<0&&(r=t.length+r),r>=t.length)if(n)return -1;else r=t.length-1;else if(r<0)if(!n)return -1;else r=0;if("string"==typeof e&&(e=a.from(e,i)),a.isBuffer(e))return 0===e.length?-1:b(t,e,r,i,n);if("number"==typeof e){if(e&=255,"function"==typeof Uint8Array.prototype.indexOf)if(n)return Uint8Array.prototype.indexOf.call(t,e,r);else return Uint8Array.prototype.lastIndexOf.call(t,e,r);return b(t,[e],r,i,n)}throw TypeError("val must be string, number or Buffer")}function b(t,e,r,i,n){var s,o=1,a=t.length,h=e.length;if(void 0!==i&&("ucs2"===(i=String(i).toLowerCase())||"ucs-2"===i||"utf16le"===i||"utf-16le"===i)){if(t.length<2||e.length<2)return -1;o=2,a/=2,h/=2,r/=2}function l(t,e){return 1===o?t[e]:t.readUInt16BE(e*o)}if(n){var c=-1;for(s=r;s<a;s++)if(l(t,s)===l(e,-1===c?0:s-c)){if(-1===c&&(c=s),s-c+1===h)return c*o}else -1!==c&&(s-=s-c),c=-1}else for(r+h>a&&(r=a-h),s=r;s>=0;s--){for(var u=!0,f=0;f<h;f++)if(l(t,s+f)!==l(e,f)){u=!1;break}if(u)return s}return -1}function v(t,e,r){r=Math.min(t.length,r);for(var i=[],n=e;n<r;){var s,o,a,h,l=t[n],c=null,u=l>239?4:l>223?3:l>191?2:1;if(n+u<=r)switch(u){case 1:l<128&&(c=l);break;case 2:(192&(s=t[n+1]))==128&&(h=(31&l)<<6|63&s)>127&&(c=h);break;case 3:s=t[n+1],o=t[n+2],(192&s)==128&&(192&o)==128&&(h=(15&l)<<12|(63&s)<<6|63&o)>2047&&(h<55296||h>57343)&&(c=h);break;case 4:s=t[n+1],o=t[n+2],a=t[n+3],(192&s)==128&&(192&o)==128&&(192&a)==128&&(h=(15&l)<<18|(63&s)<<12|(63&o)<<6|63&a)>65535&&h<1114112&&(c=h)}null===c?(c=65533,u=1):c>65535&&(c-=65536,i.push(c>>>10&1023|55296),c=56320|1023&c),i.push(c),n+=u}var f=i,p=f.length;if(p<=4096)return String.fromCharCode.apply(String,f);for(var d="",g=0;g<p;)d+=String.fromCharCode.apply(String,f.slice(g,g+=4096));return d}function _(t,e,r){if(t%1!=0||t<0)throw RangeError("offset is not uint");if(t+e>r)throw RangeError("Trying to access beyond buffer length")}function w(t,e,r,i,n,s){if(!a.isBuffer(t))throw TypeError('"buffer" argument must be a Buffer instance');if(e>n||e<s)throw RangeError('"value" argument is out of bounds');if(r+i>t.length)throw RangeError("Index out of range")}function E(t,e,r,i,n,s){if(r+i>t.length||r<0)throw RangeError("Index out of range")}function A(t,e,r,i,s){return e*=1,r>>>=0,s||E(t,e,r,4,34028234663852886e22,-34028234663852886e22),n.write(t,e,r,i,23,4),r+4}function C(t,e,r,i,s){return e*=1,r>>>=0,s||E(t,e,r,8,17976931348623157e292,-17976931348623157e292),n.write(t,e,r,i,52,8),r+8}e.hp=a,e.IS=50,a.TYPED_ARRAY_SUPPORT=function(){try{var t=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(t,e),42===t.foo()}catch(t){return!1}}(),a.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(a.prototype,"parent",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.buffer}}),Object.defineProperty(a.prototype,"offset",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.byteOffset}}),a.poolSize=8192,a.from=function(t,e,r){return h(t,e,r)},Object.setPrototypeOf(a.prototype,Uint8Array.prototype),Object.setPrototypeOf(a,Uint8Array),a.alloc=function(t,e,r){return(l(t),t<=0)?o(t):void 0!==e?"string"==typeof r?o(t).fill(e,r):o(t).fill(e):o(t)},a.allocUnsafe=function(t){return c(t)},a.allocUnsafeSlow=function(t){return c(t)},a.isBuffer=function(t){return null!=t&&!0===t._isBuffer&&t!==a.prototype},a.compare=function(t,e){if(x(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),x(e,Uint8Array)&&(e=a.from(e,e.offset,e.byteLength)),!a.isBuffer(t)||!a.isBuffer(e))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;for(var r=t.length,i=e.length,n=0,s=Math.min(r,i);n<s;++n)if(t[n]!==e[n]){r=t[n],i=e[n];break}return r<i?-1:+(i<r)},a.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},a.concat=function(t,e){if(!Array.isArray(t))throw TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return a.alloc(0);if(void 0===e)for(r=0,e=0;r<t.length;++r)e+=t[r].length;var r,i=a.allocUnsafe(e),n=0;for(r=0;r<t.length;++r){var s=t[r];if(x(s,Uint8Array))n+s.length>i.length?a.from(s).copy(i,n):Uint8Array.prototype.set.call(i,s,n);else if(a.isBuffer(s))s.copy(i,n);else throw TypeError('"list" argument must be an Array of Buffers');n+=s.length}return i},a.byteLength=d,a.prototype._isBuffer=!0,a.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)y(this,e,e+1);return this},a.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)y(this,e,e+3),y(this,e+1,e+2);return this},a.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)y(this,e,e+7),y(this,e+1,e+6),y(this,e+2,e+5),y(this,e+3,e+4);return this},a.prototype.toString=function(){var t=this.length;return 0===t?"":0==arguments.length?v(this,0,t):g.apply(this,arguments)},a.prototype.toLocaleString=a.prototype.toString,a.prototype.equals=function(t){if(!a.isBuffer(t))throw TypeError("Argument must be a Buffer");return this===t||0===a.compare(this,t)},a.prototype.inspect=function(){var t="",r=e.IS;return t=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(t+=" ... "),"<Buffer "+t+">"},s&&(a.prototype[s]=a.prototype.inspect),a.prototype.compare=function(t,e,r,i,n){if(x(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),!a.isBuffer(t))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(void 0===e&&(e=0),void 0===r&&(r=t?t.length:0),void 0===i&&(i=0),void 0===n&&(n=this.length),e<0||r>t.length||i<0||n>this.length)throw RangeError("out of range index");if(i>=n&&e>=r)return 0;if(i>=n)return -1;if(e>=r)return 1;if(e>>>=0,r>>>=0,i>>>=0,n>>>=0,this===t)return 0;for(var s=n-i,o=r-e,h=Math.min(s,o),l=this.slice(i,n),c=t.slice(e,r),u=0;u<h;++u)if(l[u]!==c[u]){s=l[u],o=c[u];break}return s<o?-1:+(o<s)},a.prototype.includes=function(t,e,r){return -1!==this.indexOf(t,e,r)},a.prototype.indexOf=function(t,e,r){return m(this,t,e,r,!0)},a.prototype.lastIndexOf=function(t,e,r){return m(this,t,e,r,!1)},a.prototype.write=function(t,e,r,i){if(void 0===e)i="utf8",r=this.length,e=0;else if(void 0===r&&"string"==typeof e)i=e,r=this.length,e=0;else if(isFinite(e))e>>>=0,isFinite(r)?(r>>>=0,void 0===i&&(i="utf8")):(i=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var n,s,o,a,h,l,c,u,f=this.length-e;if((void 0===r||r>f)&&(r=f),t.length>0&&(r<0||e<0)||e>this.length)throw RangeError("Attempt to write outside buffer bounds");i||(i="utf8");for(var p=!1;;)switch(i){case"hex":return function(t,e,r,i){r=Number(r)||0;var n=t.length-r;i?(i=Number(i))>n&&(i=n):i=n;var s=e.length;i>s/2&&(i=s/2);for(var o=0;o<i;++o){var a,h=parseInt(e.substr(2*o,2),16);if((a=h)!=a)break;t[r+o]=h}return o}(this,t,e,r);case"utf8":case"utf-8":return n=e,s=r,S(k(t,this.length-n),this,n,s);case"ascii":case"latin1":case"binary":return o=e,a=r,S(function(t){for(var e=[],r=0;r<t.length;++r)e.push(255&t.charCodeAt(r));return e}(t),this,o,a);case"base64":return h=e,l=r,S(O(t),this,h,l);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return c=e,u=r,S(function(t,e){for(var r,i,n=[],s=0;s<t.length&&!((e-=2)<0);++s)i=(r=t.charCodeAt(s))>>8,n.push(r%256),n.push(i);return n}(t,this.length-c),this,c,u);default:if(p)throw TypeError("Unknown encoding: "+i);i=(""+i).toLowerCase(),p=!0}},a.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},a.prototype.slice=function(t,e){var r=this.length;t=~~t,e=void 0===e?r:~~e,t<0?(t+=r)<0&&(t=0):t>r&&(t=r),e<0?(e+=r)<0&&(e=0):e>r&&(e=r),e<t&&(e=t);var i=this.subarray(t,e);return Object.setPrototypeOf(i,a.prototype),i},a.prototype.readUintLE=a.prototype.readUIntLE=function(t,e,r){t>>>=0,e>>>=0,r||_(t,e,this.length);for(var i=this[t],n=1,s=0;++s<e&&(n*=256);)i+=this[t+s]*n;return i},a.prototype.readUintBE=a.prototype.readUIntBE=function(t,e,r){t>>>=0,e>>>=0,r||_(t,e,this.length);for(var i=this[t+--e],n=1;e>0&&(n*=256);)i+=this[t+--e]*n;return i},a.prototype.readUint8=a.prototype.readUInt8=function(t,e){return t>>>=0,e||_(t,1,this.length),this[t]},a.prototype.readUint16LE=a.prototype.readUInt16LE=function(t,e){return t>>>=0,e||_(t,2,this.length),this[t]|this[t+1]<<8},a.prototype.readUint16BE=a.prototype.readUInt16BE=function(t,e){return t>>>=0,e||_(t,2,this.length),this[t]<<8|this[t+1]},a.prototype.readUint32LE=a.prototype.readUInt32LE=function(t,e){return t>>>=0,e||_(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+0x1000000*this[t+3]},a.prototype.readUint32BE=a.prototype.readUInt32BE=function(t,e){return t>>>=0,e||_(t,4,this.length),0x1000000*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},a.prototype.readIntLE=function(t,e,r){t>>>=0,e>>>=0,r||_(t,e,this.length);for(var i=this[t],n=1,s=0;++s<e&&(n*=256);)i+=this[t+s]*n;return i>=(n*=128)&&(i-=Math.pow(2,8*e)),i},a.prototype.readIntBE=function(t,e,r){t>>>=0,e>>>=0,r||_(t,e,this.length);for(var i=e,n=1,s=this[t+--i];i>0&&(n*=256);)s+=this[t+--i]*n;return s>=(n*=128)&&(s-=Math.pow(2,8*e)),s},a.prototype.readInt8=function(t,e){return(t>>>=0,e||_(t,1,this.length),128&this[t])?-((255-this[t]+1)*1):this[t]},a.prototype.readInt16LE=function(t,e){t>>>=0,e||_(t,2,this.length);var r=this[t]|this[t+1]<<8;return 32768&r?0xffff0000|r:r},a.prototype.readInt16BE=function(t,e){t>>>=0,e||_(t,2,this.length);var r=this[t+1]|this[t]<<8;return 32768&r?0xffff0000|r:r},a.prototype.readInt32LE=function(t,e){return t>>>=0,e||_(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},a.prototype.readInt32BE=function(t,e){return t>>>=0,e||_(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},a.prototype.readFloatLE=function(t,e){return t>>>=0,e||_(t,4,this.length),n.read(this,t,!0,23,4)},a.prototype.readFloatBE=function(t,e){return t>>>=0,e||_(t,4,this.length),n.read(this,t,!1,23,4)},a.prototype.readDoubleLE=function(t,e){return t>>>=0,e||_(t,8,this.length),n.read(this,t,!0,52,8)},a.prototype.readDoubleBE=function(t,e){return t>>>=0,e||_(t,8,this.length),n.read(this,t,!1,52,8)},a.prototype.writeUintLE=a.prototype.writeUIntLE=function(t,e,r,i){if(t*=1,e>>>=0,r>>>=0,!i){var n=Math.pow(2,8*r)-1;w(this,t,e,r,n,0)}var s=1,o=0;for(this[e]=255&t;++o<r&&(s*=256);)this[e+o]=t/s&255;return e+r},a.prototype.writeUintBE=a.prototype.writeUIntBE=function(t,e,r,i){if(t*=1,e>>>=0,r>>>=0,!i){var n=Math.pow(2,8*r)-1;w(this,t,e,r,n,0)}var s=r-1,o=1;for(this[e+s]=255&t;--s>=0&&(o*=256);)this[e+s]=t/o&255;return e+r},a.prototype.writeUint8=a.prototype.writeUInt8=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,1,255,0),this[e]=255&t,e+1},a.prototype.writeUint16LE=a.prototype.writeUInt16LE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,2,65535,0),this[e]=255&t,this[e+1]=t>>>8,e+2},a.prototype.writeUint16BE=a.prototype.writeUInt16BE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=255&t,e+2},a.prototype.writeUint32LE=a.prototype.writeUInt32LE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,4,0xffffffff,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t,e+4},a.prototype.writeUint32BE=a.prototype.writeUInt32BE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,4,0xffffffff,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},a.prototype.writeIntLE=function(t,e,r,i){if(t*=1,e>>>=0,!i){var n=Math.pow(2,8*r-1);w(this,t,e,r,n-1,-n)}var s=0,o=1,a=0;for(this[e]=255&t;++s<r&&(o*=256);)t<0&&0===a&&0!==this[e+s-1]&&(a=1),this[e+s]=(t/o|0)-a&255;return e+r},a.prototype.writeIntBE=function(t,e,r,i){if(t*=1,e>>>=0,!i){var n=Math.pow(2,8*r-1);w(this,t,e,r,n-1,-n)}var s=r-1,o=1,a=0;for(this[e+s]=255&t;--s>=0&&(o*=256);)t<0&&0===a&&0!==this[e+s+1]&&(a=1),this[e+s]=(t/o|0)-a&255;return e+r},a.prototype.writeInt8=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=255&t,e+1},a.prototype.writeInt16LE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,2,32767,-32768),this[e]=255&t,this[e+1]=t>>>8,e+2},a.prototype.writeInt16BE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=255&t,e+2},a.prototype.writeInt32LE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,4,0x7fffffff,-0x80000000),this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},a.prototype.writeInt32BE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,4,0x7fffffff,-0x80000000),t<0&&(t=0xffffffff+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},a.prototype.writeFloatLE=function(t,e,r){return A(this,t,e,!0,r)},a.prototype.writeFloatBE=function(t,e,r){return A(this,t,e,!1,r)},a.prototype.writeDoubleLE=function(t,e,r){return C(this,t,e,!0,r)},a.prototype.writeDoubleBE=function(t,e,r){return C(this,t,e,!1,r)},a.prototype.copy=function(t,e,r,i){if(!a.isBuffer(t))throw TypeError("argument should be a Buffer");if(r||(r=0),i||0===i||(i=this.length),e>=t.length&&(e=t.length),e||(e=0),i>0&&i<r&&(i=r),i===r||0===t.length||0===this.length)return 0;if(e<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(i<0)throw RangeError("sourceEnd out of bounds");i>this.length&&(i=this.length),t.length-e<i-r&&(i=t.length-e+r);var n=i-r;return this===t&&"function"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(e,r,i):Uint8Array.prototype.set.call(t,this.subarray(r,i),e),n},a.prototype.fill=function(t,e,r,i){if("string"==typeof t){if("string"==typeof e?(i=e,e=0,r=this.length):"string"==typeof r&&(i=r,r=this.length),void 0!==i&&"string"!=typeof i)throw TypeError("encoding must be a string");if("string"==typeof i&&!a.isEncoding(i))throw TypeError("Unknown encoding: "+i);if(1===t.length){var n,s=t.charCodeAt(0);("utf8"===i&&s<128||"latin1"===i)&&(t=s)}}else"number"==typeof t?t&=255:"boolean"==typeof t&&(t=Number(t));if(e<0||this.length<e||this.length<r)throw RangeError("Out of range index");if(r<=e)return this;if(e>>>=0,r=void 0===r?this.length:r>>>0,t||(t=0),"number"==typeof t)for(n=e;n<r;++n)this[n]=t;else{var o=a.isBuffer(t)?t:a.from(t,i),h=o.length;if(0===h)throw TypeError('The value "'+t+'" is invalid for argument "value"');for(n=0;n<r-e;++n)this[n+e]=o[n%h]}return this};var T=/[^+/0-9A-Za-z-_]/g;function k(t,e){e=e||1/0;for(var r,i=t.length,n=null,s=[],o=0;o<i;++o){if((r=t.charCodeAt(o))>55295&&r<57344){if(!n){if(r>56319||o+1===i){(e-=3)>-1&&s.push(239,191,189);continue}n=r;continue}if(r<56320){(e-=3)>-1&&s.push(239,191,189),n=r;continue}r=(n-55296<<10|r-56320)+65536}else n&&(e-=3)>-1&&s.push(239,191,189);if(n=null,r<128){if((e-=1)<0)break;s.push(r)}else if(r<2048){if((e-=2)<0)break;s.push(r>>6|192,63&r|128)}else if(r<65536){if((e-=3)<0)break;s.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((e-=4)<0)break;s.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return s}function O(t){return i.toByteArray(function(t){if((t=(t=t.split("=")[0]).trim().replace(T,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function S(t,e,r,i){for(var n=0;n<i&&!(n+r>=e.length)&&!(n>=t.length);++n)e[n+r]=t[n];return n}function x(t,e){return t instanceof e||null!=t&&null!=t.constructor&&null!=t.constructor.name&&t.constructor.name===e.name}var I=function(){for(var t="0123456789abcdef",e=Array(256),r=0;r<16;++r)for(var i=16*r,n=0;n<16;++n)e[i+n]=t[r]+t[n];return e}()},4298:(t,e,r)=>{"use strict";let i,n;r.d(e,{io:()=>tw});var s,o={};r.r(o),r.d(o,{Decoder:()=>tp,Encoder:()=>tu,PacketType:()=>s,protocol:()=>tc});let a=Object.create(null);a.open="0",a.close="1",a.ping="2",a.pong="3",a.message="4",a.upgrade="5",a.noop="6";let h=Object.create(null);Object.keys(a).forEach(t=>{h[a[t]]=t});let l={type:"error",data:"parser error"},c="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===Object.prototype.toString.call(Blob),u="function"==typeof ArrayBuffer,f=t=>"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(t):t&&t.buffer instanceof ArrayBuffer,p=({type:t,data:e},r,i)=>{if(c&&e instanceof Blob)if(r)return i(e);else return d(e,i);if(u&&(e instanceof ArrayBuffer||f(e)))if(r)return i(e);else return d(new Blob([e]),i);return i(a[t]+(e||""))},d=(t,e)=>{let r=new FileReader;return r.onload=function(){e("b"+(r.result.split(",")[1]||""))},r.readAsDataURL(t)};function g(t){return t instanceof Uint8Array?t:t instanceof ArrayBuffer?new Uint8Array(t):new Uint8Array(t.buffer,t.byteOffset,t.byteLength)}let y="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",m="undefined"==typeof Uint8Array?[]:new Uint8Array(256);for(let t=0;t<y.length;t++)m[y.charCodeAt(t)]=t;let b="function"==typeof ArrayBuffer,v=(t,e)=>{if("string"!=typeof t)return{type:"message",data:w(t,e)};let r=t.charAt(0);return"b"===r?{type:"message",data:_(t.substring(1),e)}:h[r]?t.length>1?{type:h[r],data:t.substring(1)}:{type:h[r]}:l},_=(t,e)=>b?w((t=>{let e=.75*t.length,r=t.length,i,n=0,s,o,a,h;"="===t[t.length-1]&&(e--,"="===t[t.length-2]&&e--);let l=new ArrayBuffer(e),c=new Uint8Array(l);for(i=0;i<r;i+=4)s=m[t.charCodeAt(i)],o=m[t.charCodeAt(i+1)],a=m[t.charCodeAt(i+2)],h=m[t.charCodeAt(i+3)],c[n++]=s<<2|o>>4,c[n++]=(15&o)<<4|a>>2,c[n++]=(3&a)<<6|63&h;return l})(t),e):{base64:!0,data:t},w=(t,e)=>"blob"===e?t instanceof Blob?t:new Blob([t]):t instanceof ArrayBuffer?t:t.buffer;function E(t){return t.reduce((t,e)=>t+e.length,0)}function A(t,e){if(t[0].length===e)return t.shift();let r=new Uint8Array(e),i=0;for(let n=0;n<e;n++)r[n]=t[0][i++],i===t[0].length&&(t.shift(),i=0);return t.length&&i<t[0].length&&(t[0]=t[0].slice(i)),r}function C(t){if(t){var e=t;for(var r in C.prototype)e[r]=C.prototype[r];return e}}C.prototype.on=C.prototype.addEventListener=function(t,e){return this._callbacks=this._callbacks||{},(this._callbacks["$"+t]=this._callbacks["$"+t]||[]).push(e),this},C.prototype.once=function(t,e){function r(){this.off(t,r),e.apply(this,arguments)}return r.fn=e,this.on(t,r),this},C.prototype.off=C.prototype.removeListener=C.prototype.removeAllListeners=C.prototype.removeEventListener=function(t,e){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var r,i=this._callbacks["$"+t];if(!i)return this;if(1==arguments.length)return delete this._callbacks["$"+t],this;for(var n=0;n<i.length;n++)if((r=i[n])===e||r.fn===e){i.splice(n,1);break}return 0===i.length&&delete this._callbacks["$"+t],this},C.prototype.emit=function(t){this._callbacks=this._callbacks||{};for(var e=Array(arguments.length-1),r=this._callbacks["$"+t],i=1;i<arguments.length;i++)e[i-1]=arguments[i];if(r){r=r.slice(0);for(var i=0,n=r.length;i<n;++i)r[i].apply(this,e)}return this},C.prototype.emitReserved=C.prototype.emit,C.prototype.listeners=function(t){return this._callbacks=this._callbacks||{},this._callbacks["$"+t]||[]},C.prototype.hasListeners=function(t){return!!this.listeners(t).length};let T="function"==typeof Promise&&"function"==typeof Promise.resolve?t=>Promise.resolve().then(t):(t,e)=>e(t,0),k="undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")();function O(t,...e){return e.reduce((e,r)=>(t.hasOwnProperty(r)&&(e[r]=t[r]),e),{})}let S=k.setTimeout,x=k.clearTimeout;function I(t,e){e.useNativeTimers?(t.setTimeoutFn=S.bind(k),t.clearTimeoutFn=x.bind(k)):(t.setTimeoutFn=k.setTimeout.bind(k),t.clearTimeoutFn=k.clearTimeout.bind(k))}function R(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}class B extends Error{constructor(t,e,r){super(t),this.description=e,this.context=r,this.type="TransportError"}}class L extends C{constructor(t){super(),this.writable=!1,I(this,t),this.opts=t,this.query=t.query,this.socket=t.socket,this.supportsBinary=!t.forceBase64}onError(t,e,r){return super.emitReserved("error",new B(t,e,r)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return("opening"===this.readyState||"open"===this.readyState)&&(this.doClose(),this.onClose()),this}send(t){"open"===this.readyState&&this.write(t)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(t){let e=v(t,this.socket.binaryType);this.onPacket(e)}onPacket(t){super.emitReserved("packet",t)}onClose(t){this.readyState="closed",super.emitReserved("close",t)}pause(t){}createUri(t,e={}){return t+"://"+this._hostname()+this._port()+this.opts.path+this._query(e)}_hostname(){let t=this.opts.hostname;return -1===t.indexOf(":")?t:"["+t+"]"}_port(){return this.opts.port&&(this.opts.secure&&Number(443!==this.opts.port)||!this.opts.secure&&80!==Number(this.opts.port))?":"+this.opts.port:""}_query(t){let e=function(t){let e="";for(let r in t)t.hasOwnProperty(r)&&(e.length&&(e+="&"),e+=encodeURIComponent(r)+"="+encodeURIComponent(t[r]));return e}(t);return e.length?"?"+e:""}}class D extends L{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(t){this.readyState="pausing";let e=()=>{this.readyState="paused",t()};if(this._polling||!this.writable){let t=0;this._polling&&(t++,this.once("pollComplete",function(){--t||e()})),this.writable||(t++,this.once("drain",function(){--t||e()}))}else e()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(t){((t,e)=>{let r=t.split("\x1e"),i=[];for(let t=0;t<r.length;t++){let n=v(r[t],e);if(i.push(n),"error"===n.type)break}return i})(t,this.socket.binaryType).forEach(t=>{if("opening"===this.readyState&&"open"===t.type&&this.onOpen(),"close"===t.type)return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(t)}),"closed"!==this.readyState&&(this._polling=!1,this.emitReserved("pollComplete"),"open"===this.readyState&&this._poll())}doClose(){let t=()=>{this.write([{type:"close"}])};"open"===this.readyState?t():this.once("open",t)}write(t){this.writable=!1,((t,e)=>{let r=t.length,i=Array(r),n=0;t.forEach((t,s)=>{p(t,!1,t=>{i[s]=t,++n===r&&e(i.join("\x1e"))})})})(t,t=>{this.doWrite(t,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){let t=this.opts.secure?"https":"http",e=this.query||{};return!1!==this.opts.timestampRequests&&(e[this.opts.timestampParam]=R()),this.supportsBinary||e.sid||(e.b64=1),this.createUri(t,e)}}let N=!1;try{N="undefined"!=typeof XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}catch(t){}let P=N;function U(){}class j extends D{constructor(t){if(super(t),"undefined"!=typeof location){let e="https:"===location.protocol,r=location.port;r||(r=e?"443":"80"),this.xd="undefined"!=typeof location&&t.hostname!==location.hostname||r!==t.port}}doWrite(t,e){let r=this.request({method:"POST",data:t});r.on("success",e),r.on("error",(t,e)=>{this.onError("xhr post error",t,e)})}doPoll(){let t=this.request();t.on("data",this.onData.bind(this)),t.on("error",(t,e)=>{this.onError("xhr poll error",t,e)}),this.pollXhr=t}}class M extends C{constructor(t,e,r){super(),this.createRequest=t,I(this,r),this._opts=r,this._method=r.method||"GET",this._uri=e,this._data=void 0!==r.data?r.data:null,this._create()}_create(){var t;let e=O(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");e.xdomain=!!this._opts.xd;let r=this._xhr=this.createRequest(e);try{r.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders)for(let t in r.setDisableHeaderCheck&&r.setDisableHeaderCheck(!0),this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(t)&&r.setRequestHeader(t,this._opts.extraHeaders[t])}catch(t){}if("POST"===this._method)try{r.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(t){}try{r.setRequestHeader("Accept","*/*")}catch(t){}null==(t=this._opts.cookieJar)||t.addCookies(r),"withCredentials"in r&&(r.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(r.timeout=this._opts.requestTimeout),r.onreadystatechange=()=>{var t;3===r.readyState&&(null==(t=this._opts.cookieJar)||t.parseCookies(r.getResponseHeader("set-cookie"))),4===r.readyState&&(200===r.status||1223===r.status?this._onLoad():this.setTimeoutFn(()=>{this._onError("number"==typeof r.status?r.status:0)},0))},r.send(this._data)}catch(t){this.setTimeoutFn(()=>{this._onError(t)},0);return}"undefined"!=typeof document&&(this._index=M.requestsCount++,M.requests[this._index]=this)}_onError(t){this.emitReserved("error",t,this._xhr),this._cleanup(!0)}_cleanup(t){if(void 0!==this._xhr&&null!==this._xhr){if(this._xhr.onreadystatechange=U,t)try{this._xhr.abort()}catch(t){}"undefined"!=typeof document&&delete M.requests[this._index],this._xhr=null}}_onLoad(){let t=this._xhr.responseText;null!==t&&(this.emitReserved("data",t),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}function F(){for(let t in M.requests)M.requests.hasOwnProperty(t)&&M.requests[t].abort()}M.requestsCount=0,M.requests={},"undefined"!=typeof document&&("function"==typeof attachEvent?attachEvent("onunload",F):"function"==typeof addEventListener&&addEventListener("onpagehide"in k?"pagehide":"unload",F,!1));let H=function(){let t=q({xdomain:!1});return t&&null!==t.responseType}();class V extends j{constructor(t){super(t);let e=t&&t.forceBase64;this.supportsBinary=H&&!e}request(t={}){return Object.assign(t,{xd:this.xd},this.opts),new M(q,this.uri(),t)}}function q(t){let e=t.xdomain;try{if("undefined"!=typeof XMLHttpRequest&&(!e||P))return new XMLHttpRequest}catch(t){}if(!e)try{return new k[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch(t){}}let z="undefined"!=typeof navigator&&"string"==typeof navigator.product&&"reactnative"===navigator.product.toLowerCase();class $ extends L{get name(){return"websocket"}doOpen(){let t=this.uri(),e=this.opts.protocols,r=z?{}:O(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(r.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(t,e,r)}catch(t){return this.emitReserved("error",t)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=t=>this.onClose({description:"websocket connection closed",context:t}),this.ws.onmessage=t=>this.onData(t.data),this.ws.onerror=t=>this.onError("websocket error",t)}write(t){this.writable=!1;for(let e=0;e<t.length;e++){let r=t[e],i=e===t.length-1;p(r,this.supportsBinary,t=>{try{this.doWrite(r,t)}catch(t){}i&&T(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){void 0!==this.ws&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){let t=this.opts.secure?"wss":"ws",e=this.query||{};return this.opts.timestampRequests&&(e[this.opts.timestampParam]=R()),this.supportsBinary||(e.b64=1),this.createUri(t,e)}}let W=k.WebSocket||k.MozWebSocket;class K extends ${createSocket(t,e,r){return z?new W(t,e,r):e?new W(t,e):new W(t)}doWrite(t,e){this.ws.send(e)}}class X extends L{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(t){return this.emitReserved("error",t)}this._transport.closed.then(()=>{this.onClose()}).catch(t=>{this.onError("webtransport error",t)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(t=>{let e=function(t,e){n||(n=new TextDecoder);let r=[],i=0,s=-1,o=!1;return new TransformStream({transform(a,h){for(r.push(a);;){if(0===i){if(1>E(r))break;let t=A(r,1);o=(128&t[0])==128,i=(s=127&t[0])<126?3:126===s?1:2}else if(1===i){if(2>E(r))break;let t=A(r,2);s=new DataView(t.buffer,t.byteOffset,t.length).getUint16(0),i=3}else if(2===i){if(8>E(r))break;let t=A(r,8),e=new DataView(t.buffer,t.byteOffset,t.length),n=e.getUint32(0);if(n>2097151){h.enqueue(l);break}s=0x100000000*n+e.getUint32(4),i=3}else{if(E(r)<s)break;let t=A(r,s);h.enqueue(v(o?t:n.decode(t),e)),i=0}if(0===s||s>t){h.enqueue(l);break}}}})}(Number.MAX_SAFE_INTEGER,this.socket.binaryType),r=t.readable.pipeThrough(e).getReader(),s=new TransformStream({transform(t,e){var r;r=r=>{let i,n=r.length;if(n<126)new DataView((i=new Uint8Array(1)).buffer).setUint8(0,n);else if(n<65536){let t=new DataView((i=new Uint8Array(3)).buffer);t.setUint8(0,126),t.setUint16(1,n)}else{let t=new DataView((i=new Uint8Array(9)).buffer);t.setUint8(0,127),t.setBigUint64(1,BigInt(n))}t.data&&"string"!=typeof t.data&&(i[0]|=128),e.enqueue(i),e.enqueue(r)},c&&t.data instanceof Blob?t.data.arrayBuffer().then(g).then(r):u&&(t.data instanceof ArrayBuffer||f(t.data))?r(g(t.data)):p(t,!1,t=>{i||(i=new TextEncoder),r(i.encode(t))})}});s.readable.pipeTo(t.writable),this._writer=s.writable.getWriter();let o=()=>{r.read().then(({done:t,value:e})=>{t||(this.onPacket(e),o())}).catch(t=>{})};o();let a={type:"open"};this.query.sid&&(a.data=`{"sid":"${this.query.sid}"}`),this._writer.write(a).then(()=>this.onOpen())})})}write(t){this.writable=!1;for(let e=0;e<t.length;e++){let r=t[e],i=e===t.length-1;this._writer.write(r).then(()=>{i&&T(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var t;null==(t=this._transport)||t.close()}}let J={websocket:K,webtransport:X,polling:V},Y=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,G=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function Z(t){if(t.length>8e3)throw"URI too long";let e=t,r=t.indexOf("["),i=t.indexOf("]");-1!=r&&-1!=i&&(t=t.substring(0,r)+t.substring(r,i).replace(/:/g,";")+t.substring(i,t.length));let n=Y.exec(t||""),s={},o=14;for(;o--;)s[G[o]]=n[o]||"";return -1!=r&&-1!=i&&(s.source=e,s.host=s.host.substring(1,s.host.length-1).replace(/;/g,":"),s.authority=s.authority.replace("[","").replace("]","").replace(/;/g,":"),s.ipv6uri=!0),s.pathNames=function(t,e){let r=e.replace(/\/{2,9}/g,"/").split("/");return("/"==e.slice(0,1)||0===e.length)&&r.splice(0,1),"/"==e.slice(-1)&&r.splice(r.length-1,1),r}(0,s.path),s.queryKey=function(t,e){let r={};return e.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(t,e,i){e&&(r[e]=i)}),r}(0,s.query),s}let Q="function"==typeof addEventListener&&"function"==typeof removeEventListener,tt=[];Q&&addEventListener("offline",()=>{tt.forEach(t=>t())},!1);class te extends C{constructor(t,e){if(super(),this.binaryType="arraybuffer",this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,t&&"object"==typeof t&&(e=t,t=null),t){let r=Z(t);e.hostname=r.host,e.secure="https"===r.protocol||"wss"===r.protocol,e.port=r.port,r.query&&(e.query=r.query)}else e.host&&(e.hostname=Z(e.host).host);I(this,e),this.secure=null!=e.secure?e.secure:"undefined"!=typeof location&&"https:"===location.protocol,e.hostname&&!e.port&&(e.port=this.secure?"443":"80"),this.hostname=e.hostname||("undefined"!=typeof location?location.hostname:"localhost"),this.port=e.port||("undefined"!=typeof location&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},e.transports.forEach(t=>{let e=t.prototype.name;this.transports.push(e),this._transportsByName[e]=t}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},e),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),"string"==typeof this.opts.query&&(this.opts.query=function(t){let e={},r=t.split("&");for(let t=0,i=r.length;t<i;t++){let i=r[t].split("=");e[decodeURIComponent(i[0])]=decodeURIComponent(i[1])}return e}(this.opts.query)),Q&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),"localhost"!==this.hostname&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},tt.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(t){let e=Object.assign({},this.opts.query);e.EIO=4,e.transport=t,this.id&&(e.sid=this.id);let r=Object.assign({},this.opts,{query:e,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[t]);return new this._transportsByName[t](r)}_open(){if(0===this.transports.length)return void this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);let t=this.opts.rememberUpgrade&&te.priorWebsocketSuccess&&-1!==this.transports.indexOf("websocket")?"websocket":this.transports[0];this.readyState="opening";let e=this.createTransport(t);e.open(),this.setTransport(e)}setTransport(t){this.transport&&this.transport.removeAllListeners(),this.transport=t,t.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",t=>this._onClose("transport close",t))}onOpen(){this.readyState="open",te.priorWebsocketSuccess="websocket"===this.transport.name,this.emitReserved("open"),this.flush()}_onPacket(t){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)switch(this.emitReserved("packet",t),this.emitReserved("heartbeat"),t.type){case"open":this.onHandshake(JSON.parse(t.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":let e=Error("server error");e.code=t.data,this._onError(e);break;case"message":this.emitReserved("data",t.data),this.emitReserved("message",t.data)}}onHandshake(t){this.emitReserved("handshake",t),this.id=t.sid,this.transport.query.sid=t.sid,this._pingInterval=t.pingInterval,this._pingTimeout=t.pingTimeout,this._maxPayload=t.maxPayload,this.onOpen(),"closed"!==this.readyState&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);let t=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+t,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},t),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,0===this.writeBuffer.length?this.emitReserved("drain"):this.flush()}flush(){if("closed"!==this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){let t=this._getWritablePackets();this.transport.send(t),this._prevBufferLen=t.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&"polling"===this.transport.name&&this.writeBuffer.length>1))return this.writeBuffer;let t=1;for(let e=0;e<this.writeBuffer.length;e++){let r=this.writeBuffer[e].data;if(r&&(t+="string"==typeof r?function(t){let e=0,r=0;for(let i=0,n=t.length;i<n;i++)(e=t.charCodeAt(i))<128?r+=1:e<2048?r+=2:e<55296||e>=57344?r+=3:(i++,r+=4);return r}(r):Math.ceil(1.33*(r.byteLength||r.size))),e>0&&t>this._maxPayload)return this.writeBuffer.slice(0,e);t+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;let t=Date.now()>this._pingTimeoutTime;return t&&(this._pingTimeoutTime=0,T(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),t}write(t,e,r){return this._sendPacket("message",t,e,r),this}send(t,e,r){return this._sendPacket("message",t,e,r),this}_sendPacket(t,e,r,i){if("function"==typeof e&&(i=e,e=void 0),"function"==typeof r&&(i=r,r=null),"closing"===this.readyState||"closed"===this.readyState)return;(r=r||{}).compress=!1!==r.compress;let n={type:t,data:e,options:r};this.emitReserved("packetCreate",n),this.writeBuffer.push(n),i&&this.once("flush",i),this.flush()}close(){let t=()=>{this._onClose("forced close"),this.transport.close()},e=()=>{this.off("upgrade",e),this.off("upgradeError",e),t()},r=()=>{this.once("upgrade",e),this.once("upgradeError",e)};return("opening"===this.readyState||"open"===this.readyState)&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?r():t()}):this.upgrading?r():t()),this}_onError(t){if(te.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&"opening"===this.readyState)return this.transports.shift(),this._open();this.emitReserved("error",t),this._onClose("transport error",t)}_onClose(t,e){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),Q&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){let t=tt.indexOf(this._offlineEventListener);-1!==t&&tt.splice(t,1)}this.readyState="closed",this.id=null,this.emitReserved("close",t,e),this.writeBuffer=[],this._prevBufferLen=0}}}te.protocol=4;class tr extends te{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),"open"===this.readyState&&this.opts.upgrade)for(let t=0;t<this._upgrades.length;t++)this._probe(this._upgrades[t])}_probe(t){let e=this.createTransport(t),r=!1;te.priorWebsocketSuccess=!1;let i=()=>{r||(e.send([{type:"ping",data:"probe"}]),e.once("packet",t=>{if(!r)if("pong"===t.type&&"probe"===t.data){if(this.upgrading=!0,this.emitReserved("upgrading",e),!e)return;te.priorWebsocketSuccess="websocket"===e.name,this.transport.pause(()=>{r||"closed"!==this.readyState&&(l(),this.setTransport(e),e.send([{type:"upgrade"}]),this.emitReserved("upgrade",e),e=null,this.upgrading=!1,this.flush())})}else{let t=Error("probe error");t.transport=e.name,this.emitReserved("upgradeError",t)}}))};function n(){r||(r=!0,l(),e.close(),e=null)}let s=t=>{let r=Error("probe error: "+t);r.transport=e.name,n(),this.emitReserved("upgradeError",r)};function o(){s("transport closed")}function a(){s("socket closed")}function h(t){e&&t.name!==e.name&&n()}let l=()=>{e.removeListener("open",i),e.removeListener("error",s),e.removeListener("close",o),this.off("close",a),this.off("upgrading",h)};e.once("open",i),e.once("error",s),e.once("close",o),this.once("close",a),this.once("upgrading",h),-1!==this._upgrades.indexOf("webtransport")&&"webtransport"!==t?this.setTimeoutFn(()=>{r||e.open()},200):e.open()}onHandshake(t){this._upgrades=this._filterUpgrades(t.upgrades),super.onHandshake(t)}_filterUpgrades(t){let e=[];for(let r=0;r<t.length;r++)~this.transports.indexOf(t[r])&&e.push(t[r]);return e}}class ti extends tr{constructor(t,e={}){let r="object"==typeof t?t:e;(!r.transports||r.transports&&"string"==typeof r.transports[0])&&(r.transports=(r.transports||["polling","websocket","webtransport"]).map(t=>J[t]).filter(t=>!!t)),super(t,r)}}ti.protocol;let tn="function"==typeof ArrayBuffer,ts=Object.prototype.toString,to="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===ts.call(Blob),ta="function"==typeof File||"undefined"!=typeof File&&"[object FileConstructor]"===ts.call(File);function th(t){return tn&&(t instanceof ArrayBuffer||("function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(t):t.buffer instanceof ArrayBuffer))||to&&t instanceof Blob||ta&&t instanceof File}let tl=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],tc=5;!function(t){t[t.CONNECT=0]="CONNECT",t[t.DISCONNECT=1]="DISCONNECT",t[t.EVENT=2]="EVENT",t[t.ACK=3]="ACK",t[t.CONNECT_ERROR=4]="CONNECT_ERROR",t[t.BINARY_EVENT=5]="BINARY_EVENT",t[t.BINARY_ACK=6]="BINARY_ACK"}(s||(s={}));class tu{constructor(t){this.replacer=t}encode(t){return(t.type===s.EVENT||t.type===s.ACK)&&function t(e,r){if(!e||"object"!=typeof e)return!1;if(Array.isArray(e)){for(let r=0,i=e.length;r<i;r++)if(t(e[r]))return!0;return!1}if(th(e))return!0;if(e.toJSON&&"function"==typeof e.toJSON&&1==arguments.length)return t(e.toJSON(),!0);for(let r in e)if(Object.prototype.hasOwnProperty.call(e,r)&&t(e[r]))return!0;return!1}(t)?this.encodeAsBinary({type:t.type===s.EVENT?s.BINARY_EVENT:s.BINARY_ACK,nsp:t.nsp,data:t.data,id:t.id}):[this.encodeAsString(t)]}encodeAsString(t){let e=""+t.type;return(t.type===s.BINARY_EVENT||t.type===s.BINARY_ACK)&&(e+=t.attachments+"-"),t.nsp&&"/"!==t.nsp&&(e+=t.nsp+","),null!=t.id&&(e+=t.id),null!=t.data&&(e+=JSON.stringify(t.data,this.replacer)),e}encodeAsBinary(t){let e=function(t){let e=[],r=t.data;return t.data=function t(e,r){if(!e)return e;if(th(e)){let t={_placeholder:!0,num:r.length};return r.push(e),t}if(Array.isArray(e)){let i=Array(e.length);for(let n=0;n<e.length;n++)i[n]=t(e[n],r);return i}if("object"==typeof e&&!(e instanceof Date)){let i={};for(let n in e)Object.prototype.hasOwnProperty.call(e,n)&&(i[n]=t(e[n],r));return i}return e}(r,e),t.attachments=e.length,{packet:t,buffers:e}}(t),r=this.encodeAsString(e.packet),i=e.buffers;return i.unshift(r),i}}function tf(t){return"[object Object]"===Object.prototype.toString.call(t)}class tp extends C{constructor(t){super(),this.reviver=t}add(t){let e;if("string"==typeof t){if(this.reconstructor)throw Error("got plaintext data when reconstructing a packet");let r=(e=this.decodeString(t)).type===s.BINARY_EVENT;r||e.type===s.BINARY_ACK?(e.type=r?s.EVENT:s.ACK,this.reconstructor=new td(e),0===e.attachments&&super.emitReserved("decoded",e)):super.emitReserved("decoded",e)}else if(th(t)||t.base64)if(this.reconstructor)(e=this.reconstructor.takeBinaryData(t))&&(this.reconstructor=null,super.emitReserved("decoded",e));else throw Error("got binary data when not reconstructing a packet");else throw Error("Unknown type: "+t)}decodeString(t){let e=0,r={type:Number(t.charAt(0))};if(void 0===s[r.type])throw Error("unknown packet type "+r.type);if(r.type===s.BINARY_EVENT||r.type===s.BINARY_ACK){let i=e+1;for(;"-"!==t.charAt(++e)&&e!=t.length;);let n=t.substring(i,e);if(n!=Number(n)||"-"!==t.charAt(e))throw Error("Illegal attachments");r.attachments=Number(n)}if("/"===t.charAt(e+1)){let i=e+1;for(;++e&&","!==t.charAt(e)&&e!==t.length;);r.nsp=t.substring(i,e)}else r.nsp="/";let i=t.charAt(e+1);if(""!==i&&Number(i)==i){let i=e+1;for(;++e;){let r=t.charAt(e);if(null==r||Number(r)!=r){--e;break}if(e===t.length)break}r.id=Number(t.substring(i,e+1))}if(t.charAt(++e)){let i=this.tryParse(t.substr(e));if(tp.isPayloadValid(r.type,i))r.data=i;else throw Error("invalid payload")}return r}tryParse(t){try{return JSON.parse(t,this.reviver)}catch(t){return!1}}static isPayloadValid(t,e){switch(t){case s.CONNECT:return tf(e);case s.DISCONNECT:return void 0===e;case s.CONNECT_ERROR:return"string"==typeof e||tf(e);case s.EVENT:case s.BINARY_EVENT:return Array.isArray(e)&&("number"==typeof e[0]||"string"==typeof e[0]&&-1===tl.indexOf(e[0]));case s.ACK:case s.BINARY_ACK:return Array.isArray(e)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class td{constructor(t){this.packet=t,this.buffers=[],this.reconPack=t}takeBinaryData(t){if(this.buffers.push(t),this.buffers.length===this.reconPack.attachments){var e,r;let t=(e=this.reconPack,r=this.buffers,e.data=function t(e,r){if(!e)return e;if(e&&!0===e._placeholder){if("number"==typeof e.num&&e.num>=0&&e.num<r.length)return r[e.num];throw Error("illegal attachments")}if(Array.isArray(e))for(let i=0;i<e.length;i++)e[i]=t(e[i],r);else if("object"==typeof e)for(let i in e)Object.prototype.hasOwnProperty.call(e,i)&&(e[i]=t(e[i],r));return e}(e.data,r),delete e.attachments,e);return this.finishedReconstruction(),t}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}function tg(t,e,r){return t.on(e,r),function(){t.off(e,r)}}let ty=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class tm extends C{constructor(t,e,r){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=t,this.nsp=e,r&&r.auth&&(this.auth=r.auth),this._opts=Object.assign({},r),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;let t=this.io;this.subs=[tg(t,"open",this.onopen.bind(this)),tg(t,"packet",this.onpacket.bind(this)),tg(t,"error",this.onerror.bind(this)),tg(t,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected||(this.subEvents(),this.io._reconnecting||this.io.open(),"open"===this.io._readyState&&this.onopen()),this}open(){return this.connect()}send(...t){return t.unshift("message"),this.emit.apply(this,t),this}emit(t,...e){var r,i,n;if(ty.hasOwnProperty(t))throw Error('"'+t.toString()+'" is a reserved event name');if(e.unshift(t),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(e),this;let o={type:s.EVENT,data:e};if(o.options={},o.options.compress=!1!==this.flags.compress,"function"==typeof e[e.length-1]){let t=this.ids++,r=e.pop();this._registerAckCallback(t,r),o.id=t}let a=null==(i=null==(r=this.io.engine)?void 0:r.transport)?void 0:i.writable,h=this.connected&&!(null==(n=this.io.engine)?void 0:n._hasPingExpired());return this.flags.volatile&&!a||(h?(this.notifyOutgoingListeners(o),this.packet(o)):this.sendBuffer.push(o)),this.flags={},this}_registerAckCallback(t,e){var r;let i=null!=(r=this.flags.timeout)?r:this._opts.ackTimeout;if(void 0===i){this.acks[t]=e;return}let n=this.io.setTimeoutFn(()=>{delete this.acks[t];for(let e=0;e<this.sendBuffer.length;e++)this.sendBuffer[e].id===t&&this.sendBuffer.splice(e,1);e.call(this,Error("operation has timed out"))},i),s=(...t)=>{this.io.clearTimeoutFn(n),e.apply(this,t)};s.withError=!0,this.acks[t]=s}emitWithAck(t,...e){return new Promise((r,i)=>{let n=(t,e)=>t?i(t):r(e);n.withError=!0,e.push(n),this.emit(t,...e)})}_addToQueue(t){let e;"function"==typeof t[t.length-1]&&(e=t.pop());let r={id:this._queueSeq++,tryCount:0,pending:!1,args:t,flags:Object.assign({fromQueue:!0},this.flags)};t.push((t,...i)=>{if(r===this._queue[0])return null!==t?r.tryCount>this._opts.retries&&(this._queue.shift(),e&&e(t)):(this._queue.shift(),e&&e(null,...i)),r.pending=!1,this._drainQueue()}),this._queue.push(r),this._drainQueue()}_drainQueue(t=!1){if(!this.connected||0===this._queue.length)return;let e=this._queue[0];(!e.pending||t)&&(e.pending=!0,e.tryCount++,this.flags=e.flags,this.emit.apply(this,e.args))}packet(t){t.nsp=this.nsp,this.io._packet(t)}onopen(){"function"==typeof this.auth?this.auth(t=>{this._sendConnectPacket(t)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(t){this.packet({type:s.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},t):t})}onerror(t){this.connected||this.emitReserved("connect_error",t)}onclose(t,e){this.connected=!1,delete this.id,this.emitReserved("disconnect",t,e),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(t=>{if(!this.sendBuffer.some(e=>String(e.id)===t)){let e=this.acks[t];delete this.acks[t],e.withError&&e.call(this,Error("socket has been disconnected"))}})}onpacket(t){if(t.nsp===this.nsp)switch(t.type){case s.CONNECT:t.data&&t.data.sid?this.onconnect(t.data.sid,t.data.pid):this.emitReserved("connect_error",Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case s.EVENT:case s.BINARY_EVENT:this.onevent(t);break;case s.ACK:case s.BINARY_ACK:this.onack(t);break;case s.DISCONNECT:this.ondisconnect();break;case s.CONNECT_ERROR:this.destroy();let e=Error(t.data.message);e.data=t.data.data,this.emitReserved("connect_error",e)}}onevent(t){let e=t.data||[];null!=t.id&&e.push(this.ack(t.id)),this.connected?this.emitEvent(e):this.receiveBuffer.push(Object.freeze(e))}emitEvent(t){if(this._anyListeners&&this._anyListeners.length)for(let e of this._anyListeners.slice())e.apply(this,t);super.emit.apply(this,t),this._pid&&t.length&&"string"==typeof t[t.length-1]&&(this._lastOffset=t[t.length-1])}ack(t){let e=this,r=!1;return function(...i){r||(r=!0,e.packet({type:s.ACK,id:t,data:i}))}}onack(t){let e=this.acks[t.id];"function"==typeof e&&(delete this.acks[t.id],e.withError&&t.data.unshift(null),e.apply(this,t.data))}onconnect(t,e){this.id=t,this.recovered=e&&this._pid===e,this._pid=e,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(t=>this.emitEvent(t)),this.receiveBuffer=[],this.sendBuffer.forEach(t=>{this.notifyOutgoingListeners(t),this.packet(t)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(t=>t()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:s.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(t){return this.flags.compress=t,this}get volatile(){return this.flags.volatile=!0,this}timeout(t){return this.flags.timeout=t,this}onAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(t),this}prependAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(t),this}offAny(t){if(!this._anyListeners)return this;if(t){let e=this._anyListeners;for(let r=0;r<e.length;r++)if(t===e[r]){e.splice(r,1);break}}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(t),this}prependAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(t),this}offAnyOutgoing(t){if(!this._anyOutgoingListeners)return this;if(t){let e=this._anyOutgoingListeners;for(let r=0;r<e.length;r++)if(t===e[r]){e.splice(r,1);break}}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(t){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length)for(let e of this._anyOutgoingListeners.slice())e.apply(this,t.data)}}function tb(t){t=t||{},this.ms=t.min||100,this.max=t.max||1e4,this.factor=t.factor||2,this.jitter=t.jitter>0&&t.jitter<=1?t.jitter:0,this.attempts=0}tb.prototype.duration=function(){var t=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var e=Math.random(),r=Math.floor(e*this.jitter*t);t=(1&Math.floor(10*e))==0?t-r:t+r}return 0|Math.min(t,this.max)},tb.prototype.reset=function(){this.attempts=0},tb.prototype.setMin=function(t){this.ms=t},tb.prototype.setMax=function(t){this.max=t},tb.prototype.setJitter=function(t){this.jitter=t};class tv extends C{constructor(t,e){var r;super(),this.nsps={},this.subs=[],t&&"object"==typeof t&&(e=t,t=void 0),(e=e||{}).path=e.path||"/socket.io",this.opts=e,I(this,e),this.reconnection(!1!==e.reconnection),this.reconnectionAttempts(e.reconnectionAttempts||1/0),this.reconnectionDelay(e.reconnectionDelay||1e3),this.reconnectionDelayMax(e.reconnectionDelayMax||5e3),this.randomizationFactor(null!=(r=e.randomizationFactor)?r:.5),this.backoff=new tb({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(null==e.timeout?2e4:e.timeout),this._readyState="closed",this.uri=t;let i=e.parser||o;this.encoder=new i.Encoder,this.decoder=new i.Decoder,this._autoConnect=!1!==e.autoConnect,this._autoConnect&&this.open()}reconnection(t){return arguments.length?(this._reconnection=!!t,t||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(t){return void 0===t?this._reconnectionAttempts:(this._reconnectionAttempts=t,this)}reconnectionDelay(t){var e;return void 0===t?this._reconnectionDelay:(this._reconnectionDelay=t,null==(e=this.backoff)||e.setMin(t),this)}randomizationFactor(t){var e;return void 0===t?this._randomizationFactor:(this._randomizationFactor=t,null==(e=this.backoff)||e.setJitter(t),this)}reconnectionDelayMax(t){var e;return void 0===t?this._reconnectionDelayMax:(this._reconnectionDelayMax=t,null==(e=this.backoff)||e.setMax(t),this)}timeout(t){return arguments.length?(this._timeout=t,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&0===this.backoff.attempts&&this.reconnect()}open(t){if(~this._readyState.indexOf("open"))return this;this.engine=new ti(this.uri,this.opts);let e=this.engine,r=this;this._readyState="opening",this.skipReconnect=!1;let i=tg(e,"open",function(){r.onopen(),t&&t()}),n=e=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",e),t?t(e):this.maybeReconnectOnOpen()},s=tg(e,"error",n);if(!1!==this._timeout){let t=this._timeout,r=this.setTimeoutFn(()=>{i(),n(Error("timeout")),e.close()},t);this.opts.autoUnref&&r.unref(),this.subs.push(()=>{this.clearTimeoutFn(r)})}return this.subs.push(i),this.subs.push(s),this}connect(t){return this.open(t)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");let t=this.engine;this.subs.push(tg(t,"ping",this.onping.bind(this)),tg(t,"data",this.ondata.bind(this)),tg(t,"error",this.onerror.bind(this)),tg(t,"close",this.onclose.bind(this)),tg(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(t){try{this.decoder.add(t)}catch(t){this.onclose("parse error",t)}}ondecoded(t){T(()=>{this.emitReserved("packet",t)},this.setTimeoutFn)}onerror(t){this.emitReserved("error",t)}socket(t,e){let r=this.nsps[t];return r?this._autoConnect&&!r.active&&r.connect():(r=new tm(this,t,e),this.nsps[t]=r),r}_destroy(t){for(let t of Object.keys(this.nsps))if(this.nsps[t].active)return;this._close()}_packet(t){let e=this.encoder.encode(t);for(let r=0;r<e.length;r++)this.engine.write(e[r],t.options)}cleanup(){this.subs.forEach(t=>t()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(t,e){var r;this.cleanup(),null==(r=this.engine)||r.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",t,e),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;let t=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{let e=this.backoff.duration();this._reconnecting=!0;let r=this.setTimeoutFn(()=>{!t.skipReconnect&&(this.emitReserved("reconnect_attempt",t.backoff.attempts),t.skipReconnect||t.open(e=>{e?(t._reconnecting=!1,t.reconnect(),this.emitReserved("reconnect_error",e)):t.onreconnect()}))},e);this.opts.autoUnref&&r.unref(),this.subs.push(()=>{this.clearTimeoutFn(r)})}}onreconnect(){let t=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",t)}}let t_={};function tw(t,e){let r;"object"==typeof t&&(e=t,t=void 0);let i=function(t,e="",r){let i=t;r=r||"undefined"!=typeof location&&location,null==t&&(t=r.protocol+"//"+r.host),"string"==typeof t&&("/"===t.charAt(0)&&(t="/"===t.charAt(1)?r.protocol+t:r.host+t),/^(https?|wss?):\/\//.test(t)||(t=void 0!==r?r.protocol+"//"+t:"https://"+t),i=Z(t)),!i.port&&(/^(http|ws)$/.test(i.protocol)?i.port="80":/^(http|ws)s$/.test(i.protocol)&&(i.port="443")),i.path=i.path||"/";let n=-1!==i.host.indexOf(":")?"["+i.host+"]":i.host;return i.id=i.protocol+"://"+n+":"+i.port+e,i.href=i.protocol+"://"+n+(r&&r.port===i.port?"":":"+i.port),i}(t,(e=e||{}).path||"/socket.io"),n=i.source,s=i.id,o=i.path,a=t_[s]&&o in t_[s].nsps;return e.forceNew||e["force new connection"]||!1===e.multiplex||a?r=new tv(n,e):(t_[s]||(t_[s]=new tv(n,e)),r=t_[s]),i.query&&!e.query&&(e.query=i.queryKey),r.socket(i.path,e)}Object.assign(tw,{Manager:tv,Socket:tm,io:tw,connect:tw})},5317:(t,e,r)=>{"use strict";r.d(e,{BN:()=>i.BN,GG:()=>i.GG,H9:()=>i.H9,My:()=>i.My,O5:()=>i.O5,P:()=>i.P,_M:()=>i._M,aU:()=>i.aU,kd:()=>i.kd,rJ:()=>i.rJ,x7:()=>i.x7});var i=r(9745)},5404:(t,e,r)=>{"use strict";r.d(e,{eJ:()=>i.ab,xI:()=>i.p,hg:()=>i.z,x9:()=>i.ac,CI:()=>i.D,r7:()=>i.al});var i=r(3239);r(1055),r(1280),r(6702),r(2881)},6702:(t,e,r)=>{"use strict";var i;r.d(e,{$b:()=>i,Vy:()=>l});let n=[];!function(t){t[t.DEBUG=0]="DEBUG",t[t.VERBOSE=1]="VERBOSE",t[t.INFO=2]="INFO",t[t.WARN=3]="WARN",t[t.ERROR=4]="ERROR",t[t.SILENT=5]="SILENT"}(i||(i={}));let s={debug:i.DEBUG,verbose:i.VERBOSE,info:i.INFO,warn:i.WARN,error:i.ERROR,silent:i.SILENT},o=i.INFO,a={[i.DEBUG]:"log",[i.VERBOSE]:"log",[i.INFO]:"info",[i.WARN]:"warn",[i.ERROR]:"error"},h=(t,e,...r)=>{if(e<t.logLevel)return;let i=new Date().toISOString(),n=a[e];if(n)console[n](`[${i}]  ${t.name}:`,...r);else throw Error(`Attempted to log a message with an invalid logType (value: ${e})`)};class l{constructor(t){this.name=t,this._logLevel=o,this._logHandler=h,this._userLogHandler=null,n.push(this)}get logLevel(){return this._logLevel}set logLevel(t){if(!(t in i))throw TypeError(`Invalid value "${t}" assigned to \`logLevel\``);this._logLevel=t}setLogLevel(t){this._logLevel="string"==typeof t?s[t]:t}get logHandler(){return this._logHandler}set logHandler(t){if("function"!=typeof t)throw TypeError("Value assigned to `logHandler` must be a function");this._logHandler=t}get userLogHandler(){return this._userLogHandler}set userLogHandler(t){this._userLogHandler=t}debug(...t){this._userLogHandler&&this._userLogHandler(this,i.DEBUG,...t),this._logHandler(this,i.DEBUG,...t)}log(...t){this._userLogHandler&&this._userLogHandler(this,i.VERBOSE,...t),this._logHandler(this,i.VERBOSE,...t)}info(...t){this._userLogHandler&&this._userLogHandler(this,i.INFO,...t),this._logHandler(this,i.INFO,...t)}warn(...t){this._userLogHandler&&this._userLogHandler(this,i.WARN,...t),this._logHandler(this,i.WARN,...t)}error(...t){this._userLogHandler&&this._userLogHandler(this,i.ERROR,...t),this._logHandler(this,i.ERROR,...t)}}},7505:(t,e,r)=>{"use strict";r.d(e,{c7:()=>C});var i,n,s=r(1055),o=r(1280),a=r(2881);let h="firebasestorage.googleapis.com";class l extends o.g{constructor(t,e,r=0){super(c(t),`Firebase Storage: ${e} (${c(t)})`),this.status_=r,this.customData={serverResponse:null},this._baseMessage=this.message,Object.setPrototypeOf(this,l.prototype)}get status(){return this.status_}set status(t){this.status_=t}_codeEquals(t){return c(t)===this.code}get serverResponse(){return this.customData.serverResponse}set serverResponse(t){this.customData.serverResponse=t,this.customData.serverResponse?this.message=`${this._baseMessage}
${this.customData.serverResponse}`:this.message=this._baseMessage}}function c(t){return"storage/"+t}function u(t){return new l(i.INVALID_ARGUMENT,t)}function f(){return new l(i.APP_DELETED,"The Firebase app was deleted.")}!function(t){t.UNKNOWN="unknown",t.OBJECT_NOT_FOUND="object-not-found",t.BUCKET_NOT_FOUND="bucket-not-found",t.PROJECT_NOT_FOUND="project-not-found",t.QUOTA_EXCEEDED="quota-exceeded",t.UNAUTHENTICATED="unauthenticated",t.UNAUTHORIZED="unauthorized",t.UNAUTHORIZED_APP="unauthorized-app",t.RETRY_LIMIT_EXCEEDED="retry-limit-exceeded",t.INVALID_CHECKSUM="invalid-checksum",t.CANCELED="canceled",t.INVALID_EVENT_NAME="invalid-event-name",t.INVALID_URL="invalid-url",t.INVALID_DEFAULT_BUCKET="invalid-default-bucket",t.NO_DEFAULT_BUCKET="no-default-bucket",t.CANNOT_SLICE_BLOB="cannot-slice-blob",t.SERVER_FILE_WRONG_SIZE="server-file-wrong-size",t.NO_DOWNLOAD_URL="no-download-url",t.INVALID_ARGUMENT="invalid-argument",t.INVALID_ARGUMENT_COUNT="invalid-argument-count",t.APP_DELETED="app-deleted",t.INVALID_ROOT_OPERATION="invalid-root-operation",t.INVALID_FORMAT="invalid-format",t.INTERNAL_ERROR="internal-error",t.UNSUPPORTED_ENVIRONMENT="unsupported-environment"}(i||(i={}));class p{constructor(t,e){this.bucket=t,this.path_=e}get path(){return this.path_}get isRoot(){return 0===this.path.length}fullServerUrl(){let t=encodeURIComponent;return"/b/"+t(this.bucket)+"/o/"+t(this.path)}bucketOnlyServerUrl(){return"/b/"+encodeURIComponent(this.bucket)+"/o"}static makeFromBucketSpec(t,e){let r;try{r=p.makeFromUrl(t,e)}catch(e){return new p(t,"")}if(""===r.path)return r;throw new l(i.INVALID_DEFAULT_BUCKET,"Invalid default bucket '"+t+"'.")}static makeFromUrl(t,e){let r=null,n="([A-Za-z0-9.\\-_]+)",s=RegExp("^gs://"+n+"(/(.*))?$","i");function o(t){t.path_=decodeURIComponent(t.path)}let a=e.replace(/[.]/g,"\\."),c=RegExp(`^https?://${a}/v[A-Za-z0-9_]+/b/${n}/o(/([^?#]*).*)?$`,"i"),u=e===h?"(?:storage.googleapis.com|storage.cloud.google.com)":e,f=[{regex:s,indices:{bucket:1,path:3},postModify:function(t){"/"===t.path.charAt(t.path.length-1)&&(t.path_=t.path_.slice(0,-1))}},{regex:c,indices:{bucket:1,path:3},postModify:o},{regex:RegExp(`^https?://${u}/${n}/([^?#]*)`,"i"),indices:{bucket:1,path:2},postModify:o}];for(let e=0;e<f.length;e++){let i=f[e],n=i.regex.exec(t);if(n){let t=n[i.indices.bucket],e=n[i.indices.path];e||(e=""),r=new p(t,e),i.postModify(r);break}}if(null==r)throw new l(i.INVALID_URL,"Invalid URL '"+t+"'.");return r}}class d{constructor(t){this.promise_=Promise.reject(t)}getPromise(){return this.promise_}cancel(t=!1){}}function g(t,e,r,i){if(i<e)throw u(`Invalid value for '${t}'. Expected ${e} or greater.`);if(i>r)throw u(`Invalid value for '${t}'. Expected ${r} or less.`)}!function(t){t[t.NO_ERROR=0]="NO_ERROR",t[t.NETWORK_ERROR=1]="NETWORK_ERROR",t[t.ABORT=2]="ABORT"}(n||(n={}));class y{constructor(t,e,r,i,n,s,o,a,h,l,c,u=!0,f=!1){this.url_=t,this.method_=e,this.headers_=r,this.body_=i,this.successCodes_=n,this.additionalRetryCodes_=s,this.callback_=o,this.errorCallback_=a,this.timeout_=h,this.progressCallback_=l,this.connectionFactory_=c,this.retry=u,this.isUsingEmulator=f,this.pendingConnection_=null,this.backoffId_=null,this.canceled_=!1,this.appDelete_=!1,this.promise_=new Promise((t,e)=>{this.resolve_=t,this.reject_=e,this.start_()})}start_(){let t=(t,e)=>{let r=this.resolve_,n=this.reject_,s=e.connection;if(e.wasSuccessCode)try{let t=this.callback_(s,s.getResponse());void 0!==t?r(t):r()}catch(t){n(t)}else if(null!==s){let t=new l(i.UNKNOWN,"An unknown error occurred, please check the error payload for server response.");t.serverResponse=s.getErrorText(),n(this.errorCallback_?this.errorCallback_(s,t):t)}else n(e.canceled?this.appDelete_?f():new l(i.CANCELED,"User canceled the upload/download."):new l(i.RETRY_LIMIT_EXCEEDED,"Max retry time for operation exceeded, please try again."))};this.canceled_?t(!1,new m(!1,null,!0)):this.backoffId_=function(t,e,r){let i=1,n=null,s=null,o=!1,a=0,h=!1;function l(...t){h||(h=!0,e.apply(null,t))}function c(e){n=setTimeout(()=>{n=null,t(f,2===a)},e)}function u(){s&&clearTimeout(s)}function f(t,...e){let r;if(h)return void u();if(t||2===a||o){u(),l.call(null,t,...e);return}i<64&&(i*=2),1===a?(a=2,r=0):r=(i+Math.random())*1e3,c(r)}let p=!1;function d(t){p||(p=!0,u(),!h&&(null!==n?(t||(a=2),clearTimeout(n),c(0)):t||(a=1)))}return c(0),s=setTimeout(()=>{o=!0,d(!0)},r),d}((t,e)=>{if(e)return void t(!1,new m(!1,null,!0));let r=this.connectionFactory_();this.pendingConnection_=r;let i=t=>{let e=t.loaded,r=t.lengthComputable?t.total:-1;null!==this.progressCallback_&&this.progressCallback_(e,r)};null!==this.progressCallback_&&r.addUploadProgressListener(i),r.send(this.url_,this.method_,this.isUsingEmulator,this.body_,this.headers_).then(()=>{null!==this.progressCallback_&&r.removeUploadProgressListener(i),this.pendingConnection_=null;let e=r.getErrorCode()===n.NO_ERROR,s=r.getStatus();if(!e||function(t,e){let r=t>=500&&t<600,i=-1!==[408,429].indexOf(t),n=-1!==e.indexOf(t);return r||i||n}(s,this.additionalRetryCodes_)&&this.retry)return void t(!1,new m(!1,null,r.getErrorCode()===n.ABORT));t(!0,new m(-1!==this.successCodes_.indexOf(s),r))})},t,this.timeout_)}getPromise(){return this.promise_}cancel(t){this.canceled_=!0,this.appDelete_=t||!1,null!==this.backoffId_&&(0,this.backoffId_)(!1),null!==this.pendingConnection_&&this.pendingConnection_.abort()}}class m{constructor(t,e,r){this.wasSuccessCode=t,this.connection=e,this.canceled=!!r}}class b{constructor(t,e){this._service=t,e instanceof p?this._location=e:this._location=p.makeFromUrl(e,t.host)}toString(){return"gs://"+this._location.bucket+"/"+this._location.path}_newRef(t,e){return new b(t,e)}get root(){let t=new p(this._location.bucket,"");return this._newRef(this._service,t)}get bucket(){return this._location.bucket}get fullPath(){return this._location.path}get name(){var t=this._location.path;let e=t.lastIndexOf("/",t.length-2);return -1===e?t:t.slice(e+1)}get storage(){return this._service}get parent(){let t=function(t){if(0===t.length)return null;let e=t.lastIndexOf("/");return -1===e?"":t.slice(0,e)}(this._location.path);if(null===t)return null;let e=new p(this._location.bucket,t);return new b(this._service,e)}_throwIfRoot(t){if(""===this._location.path)throw new l(i.INVALID_ROOT_OPERATION,"The operation '"+t+"' cannot be performed on a root reference, create a non-root reference using child, such as .child('file.png').")}}function v(t,e){let r=e?.storageBucket;return null==r?null:p.makeFromBucketSpec(r,t)}class _{constructor(t,e,r,i,n,s=!1){this.app=t,this._authProvider=e,this._appCheckProvider=r,this._url=i,this._firebaseVersion=n,this._isUsingEmulator=s,this._bucket=null,this._host=h,this._protocol="https",this._appId=null,this._deleted=!1,this._maxOperationRetryTime=12e4,this._maxUploadRetryTime=6e5,this._requests=new Set,null!=i?this._bucket=p.makeFromBucketSpec(i,this._host):this._bucket=v(this._host,this.app.options)}get host(){return this._host}set host(t){this._host=t,null!=this._url?this._bucket=p.makeFromBucketSpec(this._url,t):this._bucket=v(t,this.app.options)}get maxUploadRetryTime(){return this._maxUploadRetryTime}set maxUploadRetryTime(t){g("time",0,1/0,t),this._maxUploadRetryTime=t}get maxOperationRetryTime(){return this._maxOperationRetryTime}set maxOperationRetryTime(t){g("time",0,1/0,t),this._maxOperationRetryTime=t}async _getAuthToken(){if(this._overrideAuthToken)return this._overrideAuthToken;let t=this._authProvider.getImmediate({optional:!0});if(t){let e=await t.getToken();if(null!==e)return e.accessToken}return null}async _getAppCheckToken(){if((0,s.xZ)(this.app)&&this.app.settings.appCheckToken)return this.app.settings.appCheckToken;let t=this._appCheckProvider.getImmediate({optional:!0});return t?(await t.getToken()).token:null}_delete(){return this._deleted||(this._deleted=!0,this._requests.forEach(t=>t.cancel()),this._requests.clear()),Promise.resolve()}_makeStorageReference(t){return new b(this,t)}_makeRequest(t,e,r,i,n=!0){if(this._deleted)return new d(f());{let s=function(t,e,r,i,n,s,o=!0,a=!1){let h=function(t){let e=encodeURIComponent,r="?";for(let i in t)t.hasOwnProperty(i)&&(r=r+(e(i)+"=")+e(t[i])+"&");return r.slice(0,-1)}(t.urlParams),l=t.url+h,c=Object.assign({},t.headers);return e&&(c["X-Firebase-GMPID"]=e),null!==r&&r.length>0&&(c.Authorization="Firebase "+r),c["X-Firebase-Storage-Version"]="webjs/"+(s??"AppManager"),null!==i&&(c["X-Firebase-AppCheck"]=i),new y(l,t.method,c,t.body,t.successCodes,t.additionalRetryCodes,t.handler,t.errorHandler,t.timeout,t.progressCallback,n,o,a)}(t,this._appId,r,i,e,this._firebaseVersion,n,this._isUsingEmulator);return this._requests.add(s),s.getPromise().then(()=>this._requests.delete(s),()=>this._requests.delete(s)),s}}async makeRequestWithTokens(t,e){let[r,i]=await Promise.all([this._getAuthToken(),this._getAppCheckToken()]);return this._makeRequest(t,e,r,i).getPromise()}}let w="@firebase/storage",E="0.14.0",A="storage";function C(t=(0,s.Sx)(),e){t=(0,o.Ku)(t);let r=(0,s.j6)(t,A).getImmediate({identifier:e}),i=(0,o.yU)("storage");return i&&function(t,e,r,i={}){!function(t,e,r,i={}){t.host=`${e}:${r}`;let n=(0,o.zJ)(e);n&&((0,o.gE)(`https://${t.host}/b`),(0,o.P1)("Storage",!0)),t._isUsingEmulator=!0,t._protocol=n?"https":"http";let{mockUserToken:s}=i;s&&(t._overrideAuthToken="string"==typeof s?s:(0,o.Fy)(s,t.app.options.projectId))}(t,e,r,i)}(r,...i),r}(0,s.om)(new a.uA(A,function(t,{instanceIdentifier:e}){let r=t.getProvider("app").getImmediate();return new _(r,t.getProvider("auth-internal"),t.getProvider("app-check-internal"),e,s.MF)},"PUBLIC").setMultipleInstances(!0)),(0,s.KO)(w,E,""),(0,s.KO)(w,E,"esm2020")},7610:(t,e)=>{e.read=function(t,e,r,i,n){var s,o,a=8*n-i-1,h=(1<<a)-1,l=h>>1,c=-7,u=r?n-1:0,f=r?-1:1,p=t[e+u];for(u+=f,s=p&(1<<-c)-1,p>>=-c,c+=a;c>0;s=256*s+t[e+u],u+=f,c-=8);for(o=s&(1<<-c)-1,s>>=-c,c+=i;c>0;o=256*o+t[e+u],u+=f,c-=8);if(0===s)s=1-l;else{if(s===h)return o?NaN:1/0*(p?-1:1);o+=Math.pow(2,i),s-=l}return(p?-1:1)*o*Math.pow(2,s-i)},e.write=function(t,e,r,i,n,s){var o,a,h,l=8*s-n-1,c=(1<<l)-1,u=c>>1,f=5960464477539062e-23*(23===n),p=i?0:s-1,d=i?1:-1,g=+(e<0||0===e&&1/e<0);for(isNaN(e=Math.abs(e))||e===1/0?(a=+!!isNaN(e),o=c):(o=Math.floor(Math.log(e)/Math.LN2),e*(h=Math.pow(2,-o))<1&&(o--,h*=2),o+u>=1?e+=f/h:e+=f*Math.pow(2,1-u),e*h>=2&&(o++,h/=2),o+u>=c?(a=0,o=c):o+u>=1?(a=(e*h-1)*Math.pow(2,n),o+=u):(a=e*Math.pow(2,u-1)*Math.pow(2,n),o=0));n>=8;t[r+p]=255&a,p+=d,a/=256,n-=8);for(o=o<<n|a,l+=n;l>0;t[r+p]=255&o,p+=d,o/=256,l-=8);t[r+p-d]|=128*g}},7719:(t,e)=>{"use strict";e.byteLength=function(t){var e=h(t),r=e[0],i=e[1];return(r+i)*3/4-i},e.toByteArray=function(t){var e,r,s=h(t),o=s[0],a=s[1],l=new n((o+a)*3/4-a),c=0,u=a>0?o-4:o;for(r=0;r<u;r+=4)e=i[t.charCodeAt(r)]<<18|i[t.charCodeAt(r+1)]<<12|i[t.charCodeAt(r+2)]<<6|i[t.charCodeAt(r+3)],l[c++]=e>>16&255,l[c++]=e>>8&255,l[c++]=255&e;return 2===a&&(e=i[t.charCodeAt(r)]<<2|i[t.charCodeAt(r+1)]>>4,l[c++]=255&e),1===a&&(e=i[t.charCodeAt(r)]<<10|i[t.charCodeAt(r+1)]<<4|i[t.charCodeAt(r+2)]>>2,l[c++]=e>>8&255,l[c++]=255&e),l},e.fromByteArray=function(t){for(var e,i=t.length,n=i%3,s=[],o=0,a=i-n;o<a;o+=16383)s.push(function(t,e,i){for(var n,s=[],o=e;o<i;o+=3)n=(t[o]<<16&0xff0000)+(t[o+1]<<8&65280)+(255&t[o+2]),s.push(r[n>>18&63]+r[n>>12&63]+r[n>>6&63]+r[63&n]);return s.join("")}(t,o,o+16383>a?a:o+16383));return 1===n?s.push(r[(e=t[i-1])>>2]+r[e<<4&63]+"=="):2===n&&s.push(r[(e=(t[i-2]<<8)+t[i-1])>>10]+r[e>>4&63]+r[e<<2&63]+"="),s.join("")};for(var r=[],i=[],n="undefined"!=typeof Uint8Array?Uint8Array:Array,s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",o=0,a=s.length;o<a;++o)r[o]=s[o],i[s.charCodeAt(o)]=o;function h(t){var e=t.length;if(e%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=t.indexOf("=");-1===r&&(r=e);var i=r===e?0:4-r%4;return[r,i]}i[45]=62,i[95]=63}}]);