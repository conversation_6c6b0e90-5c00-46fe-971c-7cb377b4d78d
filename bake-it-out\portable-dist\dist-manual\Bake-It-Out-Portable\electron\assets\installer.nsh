; Custom NSIS installer script for Bake It Out
; This script provides additional customization for the Windows installer

!include "MUI2.nsh"
!include "FileFunc.nsh"

; Installer customization
!define MUI_ICON "${NSISDIR}\Contrib\Graphics\Icons\orange-install.ico"
!define MUI_UNICON "${NSISDIR}\Contrib\Graphics\Icons\orange-uninstall.ico"

; Welcome page customization
!define MUI_WELCOMEPAGE_TITLE "Welcome to Bake It Out Setup"
!define MUI_WELCOMEPAGE_TEXT "This wizard will guide you through the installation of Bake It Out, the ultimate multiplayer bakery management game.$\r$\n$\r$\nBake It Out features:$\r$\n• Single-player and multiplayer modes$\r$\n• Real-time collaboration with friends$\r$\n• Multiple languages (English & Czech)$\r$\n• Progressive difficulty and automation$\r$\n$\r$\nClick Next to continue."

; Finish page customization
!define MUI_FINISHPAGE_TITLE "Bake It Out Installation Complete"
!define MUI_FINISHPAGE_TEXT "Bake It Out has been successfully installed on your computer.$\r$\n$\r$\nY<PERSON> can now start baking and managing your virtual bakery!$\r$\n$\r$\nClick Finish to close this wizard."
!define MUI_FINISHPAGE_RUN "$INSTDIR\Bake It Out.exe"
!define MUI_FINISHPAGE_RUN_TEXT "Launch Bake It Out now"

; Directory page customization
!define MUI_DIRECTORYPAGE_TEXT_TOP "Setup will install Bake It Out in the following folder. To install in a different folder, click Browse and select another folder."

; Components page (if needed)
!define MUI_COMPONENTSPAGE_TEXT_TOP "Select the components you want to install and clear the components you do not want to install."

; Installation progress
!define MUI_INSTFILESPAGE_FINISHHEADER_TEXT "Installation Complete"
!define MUI_INSTFILESPAGE_FINISHHEADER_SUBTEXT "Bake It Out has been successfully installed."

; Uninstaller customization
!define MUI_UNCONFIRMPAGE_TEXT_TOP "This wizard will uninstall Bake It Out from your computer."

; Custom functions
Function .onInit
    ; Check if already installed
    ReadRegStr $R0 HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\{${APP_GUID}}" "UninstallString"
    StrCmp $R0 "" done
    
    MessageBox MB_OKCANCEL|MB_ICONEXCLAMATION \
        "Bake It Out is already installed. $\n$\nClick 'OK' to remove the previous version or 'Cancel' to cancel this upgrade." \
        IDOK uninst
    Abort
    
    uninst:
        ClearErrors
        ExecWait '$R0 _?=$INSTDIR'
        
        IfErrors no_remove_uninstaller done
        no_remove_uninstaller:
    
    done:
FunctionEnd

Function .onInstSuccess
    ; Create desktop shortcut if requested
    CreateShortCut "$DESKTOP\Bake It Out.lnk" "$INSTDIR\Bake It Out.exe" "" "$INSTDIR\Bake It Out.exe" 0
    
    ; Create start menu shortcuts
    CreateDirectory "$SMPROGRAMS\Bake It Out"
    CreateShortCut "$SMPROGRAMS\Bake It Out\Bake It Out.lnk" "$INSTDIR\Bake It Out.exe" "" "$INSTDIR\Bake It Out.exe" 0
    CreateShortCut "$SMPROGRAMS\Bake It Out\Uninstall.lnk" "$INSTDIR\Uninstall Bake It Out.exe" "" "$INSTDIR\Uninstall Bake It Out.exe" 0
    
    ; Register file associations (if needed)
    WriteRegStr HKCR ".bakesave" "" "BakeItOut.SaveFile"
    WriteRegStr HKCR "BakeItOut.SaveFile" "" "Bake It Out Save File"
    WriteRegStr HKCR "BakeItOut.SaveFile\DefaultIcon" "" "$INSTDIR\Bake It Out.exe,0"
    WriteRegStr HKCR "BakeItOut.SaveFile\shell\open\command" "" '"$INSTDIR\Bake It Out.exe" "%1"'
    
    ; Write registry information for Add/Remove Programs
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\BakeItOut" "DisplayName" "Bake It Out"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\BakeItOut" "UninstallString" "$INSTDIR\Uninstall Bake It Out.exe"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\BakeItOut" "InstallLocation" "$INSTDIR"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\BakeItOut" "DisplayIcon" "$INSTDIR\Bake It Out.exe"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\BakeItOut" "Publisher" "Bake It Out Team"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\BakeItOut" "DisplayVersion" "${VERSION}"
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\BakeItOut" "NoModify" 1
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\BakeItOut" "NoRepair" 1
    
    ; Calculate and write estimated size
    ${GetSize} "$INSTDIR" "/S=0K" $0 $1 $2
    IntFmt $0 "0x%08X" $0
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\BakeItOut" "EstimatedSize" "$0"
FunctionEnd

Function un.onInit
    MessageBox MB_ICONQUESTION|MB_YESNO|MB_DEFBUTTON2 \
        "Are you sure you want to completely remove Bake It Out and all of its components?" \
        IDYES +2
    Abort
FunctionEnd

Function un.onUninstSuccess
    ; Remove shortcuts
    Delete "$DESKTOP\Bake It Out.lnk"
    Delete "$SMPROGRAMS\Bake It Out\Bake It Out.lnk"
    Delete "$SMPROGRAMS\Bake It Out\Uninstall.lnk"
    RMDir "$SMPROGRAMS\Bake It Out"
    
    ; Remove file associations
    DeleteRegKey HKCR ".bakesave"
    DeleteRegKey HKCR "BakeItOut.SaveFile"
    
    ; Remove registry entries
    DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\BakeItOut"
    
    MessageBox MB_ICONINFORMATION "Bake It Out has been successfully removed from your computer."
FunctionEnd

; Custom page for additional options
Function CustomOptionsPage
    ; Add any custom installation options here
    ; For example: language selection, additional components, etc.
FunctionEnd

; Macro for checking .NET Framework (if needed)
!macro CheckDotNet
    ; Check if .NET Framework is installed
    ; Add version checking logic here if your app requires specific .NET version
!macroend

; Macro for checking Visual C++ Redistributables (if needed)
!macro CheckVCRedist
    ; Check if Visual C++ Redistributables are installed
    ; Add checking logic here if your app requires specific VC++ version
!macroend

; Custom installation types
InstType "Full Installation"
InstType "Minimal Installation"

; Sections for different components
Section "Core Application" SecCore
    SectionIn 1 2 RO  ; Required for all installation types
    
    ; Core application files are installed by electron-builder
    ; This section is mainly for registry and additional setup
SectionEnd

Section "Desktop Shortcut" SecDesktop
    SectionIn 1  ; Only in full installation
    
    ; Desktop shortcut creation is handled in .onInstSuccess
SectionEnd

Section "Start Menu Shortcuts" SecStartMenu
    SectionIn 1 2  ; In both installation types
    
    ; Start menu shortcuts creation is handled in .onInstSuccess
SectionEnd

Section "File Associations" SecFileAssoc
    SectionIn 1  ; Only in full installation
    
    ; File associations are handled in .onInstSuccess
SectionEnd

; Section descriptions
LangString DESC_SecCore ${LANG_ENGLISH} "Core application files (required)"
LangString DESC_SecDesktop ${LANG_ENGLISH} "Create a desktop shortcut for easy access"
LangString DESC_SecStartMenu ${LANG_ENGLISH} "Create Start Menu shortcuts"
LangString DESC_SecFileAssoc ${LANG_ENGLISH} "Associate .bakesave files with Bake It Out"

!insertmacro MUI_FUNCTION_DESCRIPTION_BEGIN
    !insertmacro MUI_DESCRIPTION_TEXT ${SecCore} $(DESC_SecCore)
    !insertmacro MUI_DESCRIPTION_TEXT ${SecDesktop} $(DESC_SecDesktop)
    !insertmacro MUI_DESCRIPTION_TEXT ${SecStartMenu} $(DESC_SecStartMenu)
    !insertmacro MUI_DESCRIPTION_TEXT ${SecFileAssoc} $(DESC_SecFileAssoc)
!insertmacro MUI_FUNCTION_DESCRIPTION_END
